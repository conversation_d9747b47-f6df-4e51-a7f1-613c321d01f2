@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Inventaire des Médicaments
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Gestion des stocks et traçabilité des médicaments
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
                <a href="{{ route('admin.medications') }}" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                    Catalogue
                </a>
                <button onclick="document.getElementById('add_stock_modal').checked = true" class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Ajouter du stock
                </button>
            </div>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="alert alert-success mt-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{{ session('success') }}</span>
            </div>
        @endif

        <!-- Low Stock Alerts -->
        @if($lowStockMedications->count() > 0)
            <div class="alert alert-warning mt-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                    <h3 class="font-bold">Alerte Stock Bas !</h3>
                    <div class="text-xs">{{ $lowStockMedications->count() }} médicament(s) en stock critique (≤ 10 unités)</div>
                </div>
            </div>

            <div class="mt-4">
                <div class="card bg-base-100 shadow-xl border-l-4 border-warning">
                    <div class="card-body">
                        <h3 class="card-title text-warning">Médicaments en Stock Critique</h3>
                        <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3 mt-4">
                            @foreach($lowStockMedications as $inventory)
                                <div class="flex items-center justify-between p-3 bg-warning/10 rounded-lg">
                                    <div>
                                        <div class="font-semibold">{{ $inventory->medication->name }}</div>
                                        <div class="text-sm opacity-70">{{ $inventory->medication->code }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="badge badge-error">{{ $inventory->quantity }}</div>
                                        <div class="text-xs">unités</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Stats Cards -->
        <div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-figure text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                    </div>
                    <div class="stat-title">Entrées Stock</div>
                    <div class="stat-value text-primary">{{ $inventories->total() }}</div>
                </div>
            </div>

            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Stock Total</div>
                    <div class="stat-value text-success">{{ $inventories->sum('quantity') }}</div>
                </div>
            </div>

            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Stock Critique</div>
                    <div class="stat-value text-error">{{ $lowStockMedications->count() }}</div>
                </div>
            </div>

            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Valeur Totale</div>
                    <div class="stat-value text-info">{{ number_format($inventories->sum(function($inv) { return $inv->quantity * $inv->purchase_price; })) }}</div>
                    <div class="stat-desc">FCFA</div>
                </div>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full">
                            <thead>
                                <tr>
                                    <th>Médicament</th>
                                    <th>Lot</th>
                                    <th>Quantité</th>
                                    <th>Stock actuel</th>
                                    <th>Prix d'achat</th>
                                    <th>Prix de vente</th>
                                    <th>Expiration</th>
                                    <th>Ajouté par</th>
                                    <th>Date d'ajout</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($inventories as $inventory)
                                    <tr>
                                        <td>
                                            <div>
                                                <div class="font-bold">{{ $inventory->medication->name }}</div>
                                                <div class="text-sm opacity-50">{{ $inventory->medication->code }}</div>
                                                @if($inventory->medication->strength)
                                                    <div class="text-sm text-blue-600">{{ $inventory->medication->strength }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if($inventory->batch_number)
                                                <span class="badge badge-outline">{{ $inventory->batch_number }}</span>
                                            @else
                                                <span class="text-gray-400">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $stockStatus = $inventory->quantity <= 10 ? 'error' : ($inventory->quantity <= 50 ? 'warning' : 'success');
                                            @endphp
                                            <span class="badge badge-{{ $stockStatus }}">{{ $inventory->quantity }}</span>
                                        </td>
                                        <td>
                                            @php
                                                $currentStockStatus = ($inventory->current_stock ?? 0) <= 10 ? 'error' : (($inventory->current_stock ?? 0) <= 50 ? 'warning' : 'success');
                                            @endphp
                                            <span class="badge badge-{{ $currentStockStatus }}">{{ $inventory->current_stock ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <span class="font-semibold">{{ number_format($inventory->purchase_price) }} FCFA</span>
                                            <div class="text-xs text-gray-500">
                                                Total: {{ number_format($inventory->quantity * $inventory->purchase_price) }} FCFA
                                            </div>
                                        </td>
                                        <td>
                                            <span class="font-semibold text-green-600">{{ number_format($inventory->selling_price ?? 0) }} FCFA</span>
                                            @if($inventory->selling_price && $inventory->purchase_price)
                                                @php
                                                    $margin = (($inventory->selling_price - $inventory->purchase_price) / $inventory->purchase_price) * 100;
                                                @endphp
                                                <div class="text-xs text-gray-500">
                                                    Marge: {{ number_format($margin, 1) }}%
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            @if($inventory->expiry_date)
                                                @php
                                                    $daysToExpiry = $inventory->expiry_date->diffInDays();
                                                    $isExpired = $inventory->expiry_date->isPast();
                                                    $isExpiringSoon = $daysToExpiry <= 30 && !$isExpired;
                                                @endphp
                                                <div class="text-sm">{{ $inventory->expiry_date->format('d/m/Y') }}</div>
                                                @if($isExpired)
                                                    <span class="badge badge-error badge-xs">Expiré</span>
                                                @elseif($isExpiringSoon)
                                                    <span class="badge badge-warning badge-xs">{{ $daysToExpiry }}j restants</span>
                                                @endif
                                            @else
                                                <span class="text-gray-400">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($inventory->addedBy)
                                                <div class="text-sm">{{ $inventory->addedBy->name }}</div>
                                            @else
                                                <span class="text-gray-400">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="text-sm">{{ $inventory->created_at->format('d/m/Y H:i') }}</div>
                                        </td>
                                        <td>
                                            @if($inventory->notes)
                                                <div class="tooltip" data-tip="{{ $inventory->notes }}">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </div>
                                            @else
                                                <span class="text-gray-400">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-8">
                                            <div class="text-gray-500">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                                </svg>
                                                <p>Aucun stock trouvé</p>
                                                <button onclick="document.getElementById('add_stock_modal').checked = true" class="btn btn-primary mt-4">Ajouter le premier stock</button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($inventories->hasPages())
                        <div class="mt-6 flex justify-center">
                            {{ $inventories->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Stock Modal -->
<input type="checkbox" id="add_stock_modal" class="modal-toggle" />
<div class="modal" role="dialog">
    <div class="modal-box w-11/12 max-w-2xl">
        <div class="flex justify-between items-center mb-4">
            <h3 class="font-bold text-lg">Ajouter du Stock</h3>
            <button onclick="document.getElementById('add_stock_modal').checked = false" class="btn btn-sm btn-circle btn-ghost">✕</button>
        </div>

        <form action="{{ route('admin.medications.inventory.add') }}" method="POST">
            @csrf

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <!-- Medication -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Médicament *</span>
                    </label>
                    <select name="medication_id" class="select select-bordered" required>
                        <option value="">Sélectionner un médicament</option>
                        @foreach(\App\Models\Medication::where('is_active', true)->orderBy('name')->get() as $medication)
                            <option value="{{ $medication->id }}">
                                {{ $medication->name }} ({{ $medication->code }})
                                @if($medication->strength) - {{ $medication->strength }} @endif
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Quantity -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Quantité *</span>
                    </label>
                    <input type="number" name="quantity" class="input input-bordered"
                           placeholder="0" min="1" required>
                </div>

                <!-- Purchase Price -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Prix d'achat unitaire *</span>
                    </label>
                    <div class="input-group">
                        <input type="number" name="purchase_price" class="input input-bordered"
                               placeholder="0" min="0" step="10" required>
                        <span class="bg-base-200 px-4 py-3 text-sm">FCFA</span>
                    </div>
                </div>

                <!-- Selling Price -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Prix de vente unitaire *</span>
                    </label>
                    <div class="input-group">
                        <input type="number" name="selling_price" class="input input-bordered"
                               placeholder="0" min="0" step="10" required>
                        <span class="bg-base-200 px-4 py-3 text-sm">FCFA</span>
                    </div>
                </div>

                <!-- Batch Number -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Numéro de lot</span>
                    </label>
                    <input type="text" name="batch_number" class="input input-bordered"
                           placeholder="Ex: LOT2024001">
                </div>

                <!-- Expiry Date -->
                <div class="form-control sm:col-span-2">
                    <label class="label">
                        <span class="label-text">Date d'expiration</span>
                    </label>
                    <input type="date" name="expiry_date" class="input input-bordered"
                           min="{{ date('Y-m-d') }}">
                </div>
            </div>

            <!-- Notes -->
            <div class="form-control mt-4">
                <label class="label">
                    <span class="label-text">Notes</span>
                </label>
                <textarea name="notes" class="textarea textarea-bordered"
                          placeholder="Notes sur cet ajout de stock..."></textarea>
            </div>

            <div class="modal-action">
                <button type="submit" class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Ajouter le stock
                </button>
                <button type="button" onclick="document.getElementById('add_stock_modal').checked = false" class="btn">Annuler</button>
            </div>
        </form>
    </div>
    <div class="modal-backdrop" onclick="document.getElementById('add_stock_modal').checked = false"></div>
</div>
@endsection
