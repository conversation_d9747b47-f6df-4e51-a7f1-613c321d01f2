<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InsuranceCoverage extends Model
{
    protected $table = 'insurance_coverage';

    // Types d'assurance
    public const TYPE_FIXED = 'fixed';
    public const TYPE_VARIABLE = 'variable';

    protected $fillable = [
        'insurance_type',
        'coverage_type',
        'coverage_percentage',
        'min_coverage_percentage',
        'max_coverage_percentage',
        'description',
        'is_active',
    ];

    protected $casts = [
        'coverage_percentage' => 'decimal:2',
        'min_coverage_percentage' => 'decimal:2',
        'max_coverage_percentage' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get coverage percentage for a specific insurance type
     * For variable insurance, returns null (must be specified per patient)
     */
    public static function getCoveragePercentage(string $insuranceType): ?float
    {
        $coverage = self::where('insurance_type', $insuranceType)
                       ->where('is_active', true)
                       ->first();

        if (!$coverage) {
            return null;
        }

        // Pour les assurances fixes, retourner le pourcentage défini
        if ($coverage->coverage_type === self::TYPE_FIXED) {
            return $coverage->coverage_percentage;
        }

        // Pour les assurances variables, retourner null (doit être spécifié par patient)
        return null;
    }

    /**
     * Get coverage percentage for a patient with variable insurance
     */
    public static function getPatientCoveragePercentage(string $insuranceType, ?float $customPercentage = null): float
    {
        $coverage = self::where('insurance_type', $insuranceType)
                       ->where('is_active', true)
                       ->first();

        if (!$coverage) {
            return 0;
        }

        // Pour les assurances fixes
        if ($coverage->coverage_type === self::TYPE_FIXED) {
            return $coverage->coverage_percentage ?? 0;
        }

        // Pour les assurances variables
        if ($coverage->coverage_type === self::TYPE_VARIABLE && $customPercentage !== null) {
            // Vérifier que le pourcentage est dans les limites
            $min = $coverage->min_coverage_percentage ?? 0;
            $max = $coverage->max_coverage_percentage ?? 100;

            return max($min, min($max, $customPercentage));
        }

        return 0;
    }

    /**
     * Check if insurance type is fixed
     */
    public function isFixed(): bool
    {
        return $this->coverage_type === self::TYPE_FIXED;
    }

    /**
     * Check if insurance type is variable
     */
    public function isVariable(): bool
    {
        return $this->coverage_type === self::TYPE_VARIABLE;
    }

    /**
     * Get all active insurance types with their coverage info
     */
    public static function getActiveCoverages(): array
    {
        return self::where('is_active', true)
                  ->get()
                  ->mapWithKeys(function ($coverage) {
                      return [$coverage->insurance_type => [
                          'type' => $coverage->coverage_type,
                          'percentage' => $coverage->coverage_percentage,
                          'min_percentage' => $coverage->min_coverage_percentage,
                          'max_percentage' => $coverage->max_coverage_percentage,
                      ]];
                  })
                  ->toArray();
    }

    /**
     * Validate custom percentage for variable insurance
     */
    public function validateCustomPercentage(float $percentage): bool
    {
        if ($this->coverage_type !== self::TYPE_VARIABLE) {
            return false;
        }

        $min = $this->min_coverage_percentage ?? 0;
        $max = $this->max_coverage_percentage ?? 100;

        return $percentage >= $min && $percentage <= $max;
    }
}
