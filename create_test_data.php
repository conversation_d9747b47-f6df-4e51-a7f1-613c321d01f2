<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Medication;
use App\Models\MedicationInventory;
use App\Models\User;

// Créer un utilisateur admin si nécessaire
$admin = User::first();
if (!$admin) {
    $admin = User::create([
        'name' => 'Admin Test',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role' => 'admin'
    ]);
}

// Créer le médicament Paracetamol
$paracetamol = Medication::firstOrCreate(
    ['name' => 'Paracetamol', 'code' => 'PARA001'],
    [
        'generic_name' => 'Paracétamol',
        'unit_price' => 75.00,
        'dosage_form' => 'Comprimé',
        'strength' => '500mg',
        'manufacturer' => 'Pharma Test',
        'description' => 'Antalgique et antipyrétique',
        'prescription_required' => false,
        'is_active' => true
    ]
);

// Créer l'inventaire avec les nouveaux champs
MedicationInventory::create([
    'medication_id' => $paracetamol->id,
    'quantity' => 100,
    'current_stock' => 85,
    'purchase_price' => 50.00,
    'selling_price' => 75.00,
    'batch_number' => 'LOT001',
    'expiry_date' => '2025-12-31',
    'added_by' => $admin->id,
    'added_at' => now()
]);

// Créer d'autres médicaments pour les tests
$ibuprofene = Medication::firstOrCreate(
    ['name' => 'Ibuprofène', 'code' => 'IBU001'],
    [
        'generic_name' => 'Ibuprofène',
        'unit_price' => 120.00,
        'dosage_form' => 'Comprimé',
        'strength' => '400mg',
        'manufacturer' => 'Pharma Test',
        'description' => 'Anti-inflammatoire non stéroïdien',
        'prescription_required' => true,
        'is_active' => true
    ]
);

MedicationInventory::create([
    'medication_id' => $ibuprofene->id,
    'quantity' => 50,
    'current_stock' => 30,
    'purchase_price' => 80.00,
    'selling_price' => 120.00,
    'batch_number' => 'LOT002',
    'expiry_date' => '2026-06-30',
    'added_by' => $admin->id,
    'added_at' => now()
]);

echo "Données de test créées avec succès!\n";
echo "Paracetamol - Stock: 85, Prix: 75 FCFA\n";
echo "Ibuprofène - Stock: 30, Prix: 120 FCFA\n";