@extends('layouts.app')

@section('title', 'Assurances - ' . $medication->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('admin.dashboard') }}" class="text-gray-700 hover:text-blue-600">
                        Admin
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{{ route('admin.medications.index') }}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                            Médicaments
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">Assurances</span>
                    </div>
                </li>
            </ol>
        </nav>

        <div class="mt-4 flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Couvertures d'Assurance</h1>
                <p class="mt-1 text-sm text-gray-600">
                    Gérer les couvertures d'assurance pour <strong>{{ $medication->name }}</strong>
                </p>
            </div>
            <a href="{{ route('admin.medications.insurance.create', $medication) }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Ajouter une Couverture
            </a>
        </div>
    </div>

    <!-- Medication Info Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">{{ $medication->name }}</h3>
                <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                    @if($medication->generic_name)
                        <span>{{ $medication->generic_name }}</span>
                    @endif
                    @if($medication->dosage_form)
                        <span>{{ $medication->dosage_form }}</span>
                    @endif
                    @if($medication->strength)
                        <span>{{ $medication->strength }}</span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Insurance Coverages Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Couvertures Configurées</h3>
        </div>

        @if($medication->insuranceCoverages->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type d'Assurance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Couverture
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Conditions
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Validité
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Statut
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($medication->insuranceCoverages as $coverage)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $coverage->insurance_type }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ number_format($coverage->coverage_percentage, 1) }}%</div>
                                    @if($coverage->minimum_amount || $coverage->maximum_coverage_amount)
                                        <div class="text-xs text-gray-500">
                                            @if($coverage->minimum_amount)
                                                Min: {{ number_format($coverage->minimum_amount, 2) }} DH
                                            @endif
                                            @if($coverage->maximum_coverage_amount)
                                                Max: {{ number_format($coverage->maximum_coverage_amount, 2) }} DH
                                            @endif
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    @if($coverage->conditions)
                                        <div class="text-sm text-gray-900 max-w-xs truncate" title="{{ $coverage->conditions }}">
                                            {{ $coverage->conditions }}
                                        </div>
                                    @else
                                        <span class="text-sm text-gray-400">Aucune</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @if($coverage->valid_from || $coverage->valid_until)
                                        <div>
                                            @if($coverage->valid_from)
                                                Du {{ $coverage->valid_from->format('d/m/Y') }}
                                            @endif
                                            @if($coverage->valid_until)
                                                <br>Au {{ $coverage->valid_until->format('d/m/Y') }}
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-gray-400">Permanente</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($coverage->is_active && $coverage->isCurrentlyValid())
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    @elseif($coverage->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            En attente
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="{{ route('admin.medications.insurance.edit', [$medication, $coverage]) }}" 
                                           class="text-blue-600 hover:text-blue-900">
                                            Modifier
                                        </a>
                                        <form action="{{ route('admin.medications.insurance.destroy', [$medication, $coverage]) }}" 
                                              method="POST" 
                                              class="inline"
                                              onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette couverture ?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">
                                                Supprimer
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune couverture configurée</h3>
                <p class="mt-1 text-sm text-gray-500">Commencez par ajouter une couverture d'assurance pour ce médicament.</p>
                <div class="mt-6">
                    <a href="{{ route('admin.medications.insurance.create', $medication) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Ajouter une Couverture
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Available Insurance Types Info -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-blue-900 mb-2">Types d'assurance disponibles</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-blue-700">
            @foreach($availableInsuranceTypes as $type)
                <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100">
                    {{ $type }}
                </span>
            @endforeach
        </div>
    </div>
</div>
@endsection
