<?php

namespace App\Http\Controllers;

use App\Models\Prescription;
use App\Models\PrescriptionItem;
use App\Models\Medication;
use App\Models\MedicationInventory;
use App\Models\Patient;
use App\Models\Payment;
use App\Models\InsuranceCoverage;
use App\Models\Sale;
use App\Models\SaleItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use PDF;

class PharmacistController extends Controller
{
    /**
     * Display pharmacist dashboard
     */
    public function dashboard()
    {
        $pendingPrescriptions = Prescription::where('status', 'pending')
            ->with(['patient', 'appointment.doctor'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();
            
        $todayDispensed = Prescription::where('status', 'dispensed')
            ->whereDate('dispensed_at', today())
            ->count();
            
        $lowStockMedications = MedicationInventory::select('medication_id')
            ->selectRaw('SUM(quantity) as total_stock')
            ->with('medication')
            ->groupBy('medication_id')
            ->havingRaw('SUM(quantity) < 10')
            ->get();
            
        $totalInventoryValue = MedicationInventory::join('medications', 'medication_inventories.medication_id', '=', 'medications.id')
            ->selectRaw('SUM(medication_inventories.quantity * medications.unit_price) as total_value')
            ->first()
            ->total_value ?? 0;
            
        return view('pharmacist.dashboard', compact(
            'pendingPrescriptions',
            'todayDispensed', 
            'lowStockMedications',
            'totalInventoryValue'
        ));
    }
    
    /**
     * List all prescriptions with filters
     */
    public function prescriptions(Request $request)
    {
        $query = Prescription::with(['patient', 'appointment.doctor', 'prescriptionItems.medication'])
            ->orderBy('created_at', 'desc');
            
        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('patient', function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('patient_number', 'like', "%{$search}%");
            });
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('date')) {
            $query->whereDate('created_at', $request->date);
        }
        
        $prescriptions = $query->paginate(20);
        
        // Calculate statistics
        $stats = [
            'pending_count' => Prescription::where('status', 'pending')->count(),
            'dispensed_today' => Prescription::where('status', 'dispensed')
                ->whereDate('dispensed_at', today())->count(),
            'pending_total_value' => Prescription::where('status', 'pending')
                ->with('prescriptionItems')
                ->get()
                ->sum(function($prescription) {
                    return $prescription->prescriptionItems->sum('final_price');
                }),
            'pending_patients' => Prescription::where('status', 'pending')
                ->distinct('patient_id')->count()
        ];
        
        return view('pharmacist.prescriptions.index', compact('prescriptions', 'stats'));
    }
    
    /**
     * Show prescription details
     */
    public function showPrescription(Prescription $prescription)
    {
        $prescription->load(['patient', 'appointment.doctor', 'prescriptionItems.medication']);
        
        // Get available stock for each medication
        $stockInfo = [];
        foreach ($prescription->prescriptionItems as $item) {
            if ($item->medication_id) {
                $availableStock = MedicationInventory::where('medication_id', $item->medication_id)
                    ->sum('quantity');
                $stockInfo[$item->medication_id] = $availableStock;
            }
        }
        
        return view('pharmacist.prescriptions.show', compact('prescription', 'stockInfo'));
    }
    
    /**
     * Process prescription dispensing
     */
    public function dispensePrescription(Request $request, Prescription $prescription)
    {
        $request->validate([
            'prescriptionItems' => 'required|array',
            'prescriptionItems.*.quantity_dispensed' => 'required|numeric|min:0',
            'prescriptionItems.*.price' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,mobile_money',
            'apply_insurance' => 'boolean',
        ]);
        
        DB::beginTransaction();
        
        try {
            $totalAmount = 0;
            $totalInsuranceDiscount = 0;
            
            // Get patient insurance information
            $patient = $prescription->patient;
            $applyInsurance = $request->apply_insurance && $patient->hasValidInsurance();

            if ($request->apply_insurance && !$patient->hasValidInsurance()) {
                if (!$patient->has_insurance) {
                    throw new \Exception('Le patient n\'a pas d\'assurance enregistrée');
                } else {
                    throw new \Exception('L\'assurance du patient n\'est pas valide ou a expiré');
                }
            }
            
            // Update prescription items with dispensed quantities and prices
            foreach ($request->prescriptionItems as $itemId => $itemData) {
                $prescriptionItem = PrescriptionItem::findOrFail($itemId);
                
                $quantityDispensed = $itemData['quantity_dispensed'];
                $unitPrice = $itemData['price'];
                
                // Check stock availability
                if ($prescriptionItem->medication_id) {
                    $availableStock = MedicationInventory::where('medication_id', $prescriptionItem->medication_id)
                        ->sum('quantity');
                    
                    if ($quantityDispensed > $availableStock) {
                        throw new \Exception("Stock insuffisant pour {$prescriptionItem->medication_name}");
                    }
                    
                    // Reduce stock
                    $this->reduceStock($prescriptionItem->medication_id, $quantityDispensed);
                }
                
                // Calculate prices with insurance
                $prescriptionItem->unit_price = $unitPrice;
                $prescriptionItem->quantity_dispensed = $quantityDispensed;
                $prescriptionItem->calculateTotalPrice();

                // Apply insurance if applicable
                if ($applyInsurance && $prescriptionItem->medication_id) {
                    // Check if this specific medication is covered by patient's insurance
                    $medicationCoverage = $prescriptionItem->medication->getCoveragePercentage($patient->insurance_type);

                    if ($medicationCoverage !== null) {
                        // Use medication-specific coverage
                        $prescriptionItem->applyInsuranceDiscount($medicationCoverage);
                    } else {
                        // Fall back to patient's general insurance coverage
                        $generalCoverage = $patient->getInsuranceCoveragePercentage();
                        if ($generalCoverage > 0) {
                            $prescriptionItem->applyInsuranceDiscount($generalCoverage);
                        } else {
                            $prescriptionItem->insurance_discount = 0;
                            $prescriptionItem->calculateFinalPrice();
                        }
                    }
                } else {
                    $prescriptionItem->insurance_discount = 0;
                    $prescriptionItem->calculateFinalPrice();
                }
                
                $prescriptionItem->save();
                
                $totalAmount += $prescriptionItem->final_price;
                $totalInsuranceDiscount += $prescriptionItem->insurance_discount;
            }
            
            // Update prescription status
            $prescription->update([
                'status' => 'dispensed',
                'dispensed_at' => now(),
                'dispensed_by' => Auth::id(),
                'total_amount' => $totalAmount,
            ]);
            
            // Create payment record
            $payment = Payment::create([
                'patient_id' => $prescription->patient_id,
                'appointment_id' => $prescription->appointment_id,
                'amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'payment_type' => 'pharmacy',
                'reference_number' => 'PHARM-' . date('Ymd') . '-' . str_pad($prescription->id, 4, '0', STR_PAD_LEFT),
                'status' => 'completed',
                'received_by' => Auth::id(),
            ]);
            
            DB::commit();
            
            return redirect()->route('pharmacist.prescriptions.receipt', $prescription)
                ->with('success', 'Ordonnance délivrée avec succès!');
                
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }
    
    /**
     * Show prescription receipt
     */
    public function showReceipt(Prescription $prescription)
    {
        $prescription->load(['patient', 'appointment.doctor', 'prescriptionItems', 'payment']);
        
        return view('pharmacist.prescriptions.receipt', compact('prescription'));
    }
    
    /**
     * Download prescription receipt as PDF
     */
    public function downloadReceipt(Prescription $prescription)
    {
        $prescription->load(['patient', 'appointment.doctor', 'prescriptionItems', 'payment']);
        
        $pdf = PDF::loadView('pharmacist.prescriptions.receipt-pdf', compact('prescription'));
        
        return $pdf->download('recu-pharmacie-' . $prescription->prescription_number . '.pdf');
    }
    
    /**
     * Search for patients (AJAX)
     */
    public function searchPatient(Request $request)
    {
        $query = $request->get('q');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }
        
        $patients = Patient::where('first_name', 'like', "%{$query}%")
            ->orWhere('last_name', 'like', "%{$query}%")
            ->orWhere('patient_number', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->limit(10)
            ->get(['id', 'first_name', 'last_name', 'patient_number', 'phone', 'gender']);
            
        return response()->json($patients);
    }
    
    /**
     * Show direct sale form
     */


    public function searchMedications(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $medications = MedicationInventory::with('medication')
            ->whereHas('medication', function($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('generic_name', 'LIKE', "%{$query}%")
                  ->orWhere('code', 'LIKE', "%{$query}%");
            })
            ->orWhere('batch_number', 'LIKE', "%{$query}%")
            ->where('current_stock', '>', 0)
            ->orderBy('current_stock', 'desc')
            ->limit(10)
            ->get()
            ->map(function($inventory) {
                return [
                    'id' => $inventory->id,
                    'name' => $inventory->medication->name,
                    'form' => $inventory->medication->dosage_form,
                    'code' => $inventory->medication->code,
                    'active_ingredient' => $inventory->medication->generic_name,
                    'price' => $inventory->selling_price,
                    'stock' => $inventory->current_stock,
                    'batch' => $inventory->batch_number,
                    'expiry' => $inventory->expiry_date?->format('d/m/Y')
                ];
            });

        return response()->json($medications);
    }
    
    /**
     * Show cart page
     */
    public function cart()
    {
        return view('pharmacist.cart');
    }
    
    /**
     * Search prescriptions for cart system
     */
    public function searchPrescriptions(Request $request)
    {
        $query = $request->get('q');
        
        $prescriptions = Prescription::with(['patient', 'appointment.doctor'])
            ->where(function($q) use ($query) {
                $q->where('prescription_number', 'like', "%{$query}%")
                  ->orWhereHas('patient', function($patientQuery) use ($query) {
                      $patientQuery->where('first_name', 'like', "%{$query}%")
                                  ->orWhere('last_name', 'like', "%{$query}%")
                                  ->orWhere('patient_number', 'like', "%{$query}%");
                  });
            })
            ->limit(10)
            ->get();
            
        return response()->json($prescriptions);
    }
    
    /**
     * Get prescription medications for cart
     */
    public function getPrescriptionMedications(Prescription $prescription)
    {
        $items = $prescription->prescriptionItems()->with(['medication', 'medicationInventory'])->get();
        
        $medications = $items->map(function($item) {
            return [
                'medication_id' => $item->medication_id,
                'medication_name' => $item->medication ? $item->medication->name : null,
                'medication_form' => $item->medication ? $item->medication->form : null,
                'manual_medication_name' => $item->manual_medication_name,
                'manual_medication_form' => $item->manual_medication_form,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'stock' => $item->medicationInventory ? $item->medicationInventory->current_stock : 0
            ];
        });
        
        return response()->json($medications);
    }
    
    /**
     * Process cart sale with individual insurance coverage per medication
     */
    public function processCartSale(Request $request)
    {
        $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'prescription_id' => 'nullable|exists:prescriptions,id',
            'payment_method' => 'required|in:cash,mobile_money',
            'items' => 'required|array|min:1',
            'items.*.medication_id' => 'required|exists:medication_inventories,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.insurance_coverage_percentage' => 'required|numeric|min:0|max:100'
        ]);
        
        DB::beginTransaction();
        try {
            $patient = Patient::findOrFail($request->patient_id);
            $prescription = $request->prescription_id ? Prescription::findOrFail($request->prescription_id) : null;
            
            // Calculate totals
            $subtotal = 0;
            $totalDiscount = 0;
            $saleItems = [];
            
            foreach ($request->items as $itemData) {
                $inventory = MedicationInventory::findOrFail($itemData['medication_id']);
                
                // Check stock availability
                if ($inventory->current_stock < $itemData['quantity']) {
                    throw new \Exception("Stock insuffisant pour {$inventory->medication->name}. Stock disponible: {$inventory->current_stock}");
                }
                
                $itemTotal = $itemData['unit_price'] * $itemData['quantity'];
                $itemDiscount = $itemTotal * ($itemData['insurance_coverage_percentage'] / 100);
                
                $subtotal += $itemTotal;
                $totalDiscount += $itemDiscount;
                
                $saleItems[] = [
                    'inventory' => $inventory,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'total_price' => $itemTotal,
                    'insurance_coverage_percentage' => $itemData['insurance_coverage_percentage'],
                    'insurance_discount' => $itemDiscount
                ];
            }
            
            $finalTotal = $subtotal - $totalDiscount;
            
            // Create the sale record
            $sale = Sale::create([
                'patient_id' => $patient->id,
                'pharmacist_id' => auth()->id(),
                'prescription_id' => $request->prescription_id,
                'subtotal' => $subtotal,
                'total_discount' => $totalDiscount,
                'total_amount' => $finalTotal,
                'payment_method' => $request->payment_method,
                'sale_type' => $request->prescription_id ? 'prescription' : 'direct',
                'dispensed_at' => now()
            ]);
            
            // Create sale items
            foreach ($saleItems as $item) {
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'medication_inventory_id' => $item['inventory']->id,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['total_price'],
                    'insurance_coverage_percentage' => $item['insurance_coverage_percentage'],
                    'insurance_discount' => $item['insurance_discount']
                ]);
                
                // Update stock
                $item['inventory']->decrement('current_stock', $item['quantity']);
            }
            
            // Update prescription status if applicable
            if ($request->prescription_id) {
                $prescription = Prescription::find($request->prescription_id);
                if ($prescription) {
                    $prescription->update(['status' => 'dispensed']);
                }
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Vente effectuée avec succès',
                'sale_id' => $sale->id,
                'total' => $finalTotal,
                'redirect_url' => route('pharmacist.sales.receipt', $sale->id)
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Reduce medication stock
     */
    // private function reduceStock($medicationId, $quantity)
    // {
    //     $inventories = MedicationInventory::where('medication_id', $medicationId)
    //         ->where('quantity', '>', 0)
    //         ->orderBy('expiry_date', 'asc') // FIFO - First In, First Out
    //         ->get();
        
    //     $remainingQuantity = $quantity;
        
    //     foreach ($inventories as $inventory) {
    //         if ($remainingQuantity <= 0) break;
            
    //         if ($inventory->quantity >= $remainingQuantity) {
    //             // This inventory has enough stock
    //             $inventory->quantity -= $remainingQuantity;
    //             $inventory->save();
    //             $remainingQuantity = 0;
    //         } else {
    //             // Use all of this inventory and continue
    //             $remainingQuantity -= $inventory->quantity;
    //             $inventory->quantity = 0;
    //             $inventory->save();
    //         }
    //     }
        
    //     if ($remainingQuantity > 0) {
    //         throw new \Exception('Stock insuffisant');
    //     }
    // }
    

    
    /**
     * List all sales (both prescription and direct sales)
     */
    public function salesList(Request $request)
    {
        $query = Sale::with(['patient', 'pharmacist', 'saleItems.medicationInventory.medication'])
            ->orderBy('dispensed_at', 'desc');
            
        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('patient', function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('patient_number', 'like', "%{$search}%");
            });
        }
        
        if ($request->filled('sale_type')) {
            $query->where('sale_type', $request->sale_type);
        }
        
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }
        
        if ($request->filled('date_from')) {
            $query->whereDate('dispensed_at', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('dispensed_at', '<=', $request->date_to);
        }
        
        $sales = $query->paginate(20);
        
        // Calculate statistics
        $stats = [
            'total_sales' => Sale::sum('total_amount'),
            'today_sales' => Sale::whereDate('dispensed_at', today())->sum('total_amount'),
            'total_transactions' => Sale::count(),
            'today_transactions' => Sale::whereDate('dispensed_at', today())->count(),
            'prescription_sales' => Sale::where('sale_type', 'prescription')->sum('total_amount'),
            'direct_sales' => Sale::where('sale_type', 'direct')->sum('total_amount')
        ];
        
        return view('pharmacist.sales.index', compact('sales', 'stats'));
    }
    
    /**
     * Show sale receipt
     */
    public function showSaleReceipt(Sale $sale)
    {
        $sale->load(['patient', 'pharmacist', 'saleItems.medicationInventory.medication']);
        
        return view('pharmacist.sales.receipt', compact('sale'));
    }
    
    /**
     * Download sale receipt as PDF
     */
    public function downloadSaleReceipt(Sale $sale)
    {
        $sale->load(['patient', 'pharmacist', 'saleItems.medicationInventory.medication']);
        
        $pdf = PDF::loadView('pharmacist.sales.receipt-pdf', compact('sale'));
        
        $filename = 'recu_vente_' . $sale->id . '_' . date('Y-m-d') . '.pdf';
        
        return $pdf->download($filename);
    }
    
    /**
     * Generate sales report
     */
    public function salesReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        
        $sales = Prescription::where('status', 'dispensed')
            ->whereBetween('dispensed_at', [$startDate, $endDate])
            ->with(['patient', 'prescriptionItems.medication', 'dispensedBy'])
            ->orderBy('dispensed_at', 'desc')
            ->get();
            
        $totalSales = $sales->sum('total_amount');
        $totalTransactions = $sales->count();
        
        return view('pharmacist.sales-report', compact('sales', 'totalSales', 'totalTransactions', 'startDate', 'endDate'));
    }
    
    /**
     * Reduce medication stock using FIFO method
     */
    private function reduceStock($medicationId, $quantity)
    {
        $remainingQuantity = $quantity;
        
        // Get inventory items ordered by expiry date (FIFO)
        $inventoryItems = MedicationInventory::where('medication_id', $medicationId)
            ->where('quantity', '>', 0)
            ->orderBy('expiry_date', 'asc')
            ->orderBy('created_at', 'asc')
            ->get();
            
        foreach ($inventoryItems as $item) {
            if ($remainingQuantity <= 0) {
                break;
            }
            
            $quantityToReduce = min($item->quantity, $remainingQuantity);
            
            $item->quantity -= $quantityToReduce;
            $item->save();
            
            $remainingQuantity -= $quantityToReduce;
        }
        
        if ($remainingQuantity > 0) {
            throw new \Exception("Stock insuffisant pour réduire la quantité demandée");
        }
    }
}