<?php

namespace Database\Seeders;

use App\Models\Doctor;
use App\Models\Service;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class Doctor<PERSON>eeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les services pour associer les médecins
        $generalService = Service::where('service_code', 'CONS-GEN')->first();
        $pediatricService = Service::where('service_code', 'CONS-PED')->first();
        $gynecoService = Service::where('service_code', 'CONS-GYN')->first();
        $cardioService = Service::where('service_code', 'CONS-CAR')->first();
        $dentalService = Service::where('service_code', 'CONS-DEN')->first();
        $psychService = Service::where('service_code', 'CONS-PSY')->first();
        $kineService = Service::where('service_code', 'KINE-GEN')->first();

        // Tableau des docteurs à créer avec leurs informations
        $doctors = [
            [
                'user' => [
                    'name' => 'Dr. Amadou <PERSON>',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'phone_number' => '+223 70 12 34 56',
                    'address' => 'Bamako, Hamdallaye ACI 2000',
                ],
                'doctor' => [
                    'service_id' => $generalService->id,
                    'specialization' => 'Médecine Générale',
                    'qualification' => 'Doctorat en Médecine, Université de Bamako',
                    'license_number' => 'MG-2015-1234',
                    'biography' => 'Dr. Amadou Diallo est un médecin généraliste expérimenté avec plus de 10 ans de pratique médicale. Il est spécialisé dans le traitement des maladies chroniques et la médecine préventive.',
                    'is_active' => true,
                    'working_hours_start' => '08:00:00',
                    'working_hours_end' => '16:00:00',
                    'working_days' => ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'],
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. Fatoumata Touré',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'phone_number' => '+223 70 23 45 67',
                    'address' => 'Bamako, Badalabougou',
                ],
                'doctor' => [
                    'service_id' => $pediatricService->id,
                    'specialization' => 'Pédiatrie',
                    'qualification' => 'Doctorat en Médecine, Spécialisation en Pédiatrie, Université de Dakar',
                    'license_number' => 'PED-2016-2345',
                    'biography' => 'Dr. Fatoumata Touré est pédiatre avec une attention particulière aux soins néonatals et au développement infantile. Elle a complété sa formation au Sénégal et en France.',
                    'is_active' => true,
                    'working_hours_start' => '09:00:00',
                    'working_hours_end' => '17:00:00',
                    'working_days' => ['Lundi', 'Mardi', 'Jeudi', 'Vendredi', 'Samedi'],
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. Ibrahim Coulibaly',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'phone_number' => '+223 70 34 56 78',
                    'address' => 'Bamako, Magnambougou',
                ],
                'doctor' => [
                    'service_id' => $cardioService->id,
                    'specialization' => 'Cardiologie',
                    'qualification' => 'Doctorat en Médecine, Spécialisation en Cardiologie, Université de Tunis',
                    'license_number' => 'CAR-2014-3456',
                    'biography' => 'Dr. Ibrahim Coulibaly est cardiologue spécialisé dans le diagnostic et le traitement des maladies cardiovasculaires. Il possède une expertise particulière en échocardiographie.',
                    'is_active' => true,
                    'working_hours_start' => '08:00:00',
                    'working_hours_end' => '16:00:00',
                    'working_days' => ['Lundi', 'Mercredi', 'Jeudi', 'Vendredi'],
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. Aïcha Keita',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'phone_number' => '+223 70 45 67 89',
                    'address' => 'Bamako, Kalaban Coura',
                ],
                'doctor' => [
                    'service_id' => $gynecoService->id,
                    'specialization' => 'Gynécologie',
                    'qualification' => 'Doctorat en Médecine, Spécialisation en Gynécologie et Obstétrique, Université de Rabat',
                    'license_number' => 'GYN-2017-4567',
                    'biography' => 'Dr. Aïcha Keita est gynécologue-obstétricienne spécialisée dans la santé reproductive et le suivi de grossesse. Elle a complété sa formation au Maroc avec une expérience complémentaire en France.',
                    'is_active' => true,
                    'working_hours_start' => '09:00:00',
                    'working_hours_end' => '17:00:00',
                    'working_days' => ['Lundi', 'Mardi', 'Mercredi', 'Vendredi', 'Samedi'],
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. Moussa Traoré',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'phone_number' => '+223 70 56 78 90',
                    'address' => 'Bamako, Lafiabougou',
                ],
                'doctor' => [
                    'service_id' => $dentalService->id,
                    'specialization' => 'Dentisterie',
                    'qualification' => 'Doctorat en Médecine Dentaire, Université de Casablanca',
                    'license_number' => 'DEN-2018-5678',
                    'biography' => 'Dr. Moussa Traoré est chirurgien-dentiste avec une expertise en soins dentaires généraux, en orthodontie et en implantologie. Il a pratiqué au Maroc avant de revenir au Mali.',
                    'is_active' => true,
                    'working_hours_start' => '08:30:00',
                    'working_hours_end' => '16:30:00',
                    'working_days' => ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Samedi'],
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. Mariam Cissé',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'phone_number' => '+223 70 67 89 01',
                    'address' => 'Bamako, Sotuba',
                ],
                'doctor' => [
                    'service_id' => $psychService->id,
                    'specialization' => 'Psychiatrie',
                    'qualification' => 'Doctorat en Médecine, Spécialisation en Psychiatrie, Université de Montréal',
                    'license_number' => 'PSY-2016-6789',
                    'biography' => 'Dr. Mariam Cissé est psychiatre spécialisée dans le traitement des troubles anxieux et dépressifs. Elle a complété sa formation au Canada et a travaillé dans plusieurs hôpitaux internationaux.',
                    'is_active' => true,
                    'working_hours_start' => '10:00:00',
                    'working_hours_end' => '18:00:00',
                    'working_days' => ['Lundi', 'Mardi', 'Jeudi', 'Vendredi'],
                ]
            ],
            [
                'user' => [
                    'name' => 'Dr. Bakary Sangaré',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'phone_number' => '+223 70 78 90 12',
                    'address' => 'Bamako, Torokorobougou',
                ],
                'doctor' => [
                    'service_id' => $kineService->id,
                    'specialization' => 'Kinésithérapie',
                    'qualification' => 'Diplôme d\'État en Kinésithérapie, École de Kinésithérapie de Paris',
                    'license_number' => 'KIN-2019-7890',
                    'biography' => 'Dr. Bakary Sangaré est kinésithérapeute spécialisé en rééducation fonctionnelle et sportive. Il a travaillé avec plusieurs équipes sportives professionnelles avant de rejoindre notre clinique.',
                    'is_active' => true,
                    'working_hours_start' => '08:00:00',
                    'working_hours_end' => '16:00:00',
                    'working_days' => ['Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
                ]
            ],
        ];

        // Créer les utilisateurs docteurs et leurs profils
        foreach ($doctors as $doctorData) {
            // Créer l'utilisateur
            $user = User::create($doctorData['user']);
            
            // Assigner le rôle de docteur
            $user->assignRole('doctor');
            
            // Créer le profil de docteur
            $doctorData['doctor']['user_id'] = $user->id;
            Doctor::create($doctorData['doctor']);
        }
    }
} 