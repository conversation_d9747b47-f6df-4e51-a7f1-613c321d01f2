@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center">
                <a href="{{ route('appointments.show', $appointment) }}" class="btn btn-circle btn-ghost">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
                <div class="ml-4">
                    <h2 class="text-2xl font-bold">Modifier Rendez-vous</h2>
                    <p class="text-base-content/70">
                        {{ $appointment->appointment_datetime->format('d/m/Y à H:i') }} • {{ $appointment->service->name }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Form -->
        <div class="card bg-base-100 shadow-xl">
            <form action="{{ route('appointments.update', $appointment) }}" method="POST" class="card-body space-y-8">
                @csrf
                @method('PUT')
                
                <!-- Patient Selection -->
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <div class="avatar placeholder">
                            <div class="bg-primary/10 text-primary w-12 rounded-lg">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold">Patient</h3>
                            <p class="text-base-content/70">Patient pour ce rendez-vous</p>
                        </div>
                    </div>

                    <div>
                        <label class="label" for="patient_id">
                            <span class="label-text">Patient *</span>
                        </label>
                        <select name="patient_id" id="patient_id" required class="select select-bordered w-full @error('patient_id') select-error @enderror">
                            <option value="">Sélectionner un patient...</option>
                            @foreach($patients as $patient)
                                <option value="{{ $patient->id }}" {{ old('patient_id', $appointment->patient_id) == $patient->id ? 'selected' : '' }}>
                                    {{ $patient->first_name }} {{ $patient->last_name }} - {{ $patient->patient_number }}
                                </option>
                            @endforeach
                        </select>
                        @error('patient_id')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>
                </div>

                <!-- Appointment Details -->
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <div class="avatar placeholder">
                            <div class="bg-success/10 text-success w-12 rounded-lg">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold">Détails du rendez-vous</h3>
                            <p class="text-base-content/70">Date, heure et service médical</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <!-- Service -->
                        <div>
                            <label class="label" for="service_id">
                                <span class="label-text">Service médical *</span>
                            </label>
                            <select name="service_id" id="service_id" required class="select select-bordered w-full @error('service_id') select-error @enderror">
                                <option value="">Sélectionner un service...</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" {{ old('service_id', $appointment->service_id) == $service->id ? 'selected' : '' }}>
                                        {{ $service->name }} - {{ number_format($service->price) }} FCFA
                                    </option>
                                @endforeach
                            </select>
                            @error('service_id')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Doctor -->
                        <div>
                            <label class="label" for="doctor_id">
                                <span class="label-text">Médecin *</span>
                            </label>
                            <select name="doctor_id" id="doctor_id" required class="select select-bordered w-full @error('doctor_id') select-error @enderror">
                                <option value="">Sélectionner un médecin...</option>
                                @foreach($doctors as $doctor)
                                    <option value="{{ $doctor->id }}" {{ old('doctor_id', $appointment->doctor_id) == $doctor->id ? 'selected' : '' }}>
                                        Dr. {{ $doctor->user->name }} - {{ $doctor->specialization }}
                                    </option>
                                @endforeach
                            </select>
                            @error('doctor_id')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Date -->
                        <div>
                            <label class="label" for="appointment_date">
                                <span class="label-text">Date *</span>
                            </label>
                            <input type="date" name="appointment_date" id="appointment_date" 
                                   value="{{ old('appointment_date', $appointment->appointment_datetime->format('Y-m-d')) }}" required
                                   class="input input-bordered w-full @error('appointment_date') input-error @enderror">
                            @error('appointment_date')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Time -->
                        <div>
                            <label class="label" for="appointment_time">
                                <span class="label-text">Heure *</span>
                            </label>
                            <select name="appointment_time" id="appointment_time" required
                                    class="select select-bordered w-full @error('appointment_time') select-error @enderror">
                                <option value="">Sélectionner une heure...</option>
                                @for($hour = 8; $hour <= 17; $hour++)
                                    @for($minute = 0; $minute < 60; $minute += 30)
                                        @php
                                            $time = sprintf('%02d:%02d', $hour, $minute);
                                        @endphp
                                        <option value="{{ $time }}" {{ old('appointment_time', $appointment->appointment_datetime->format('H:i')) == $time ? 'selected' : '' }}>
                                            {{ $time }}
                                        </option>
                                    @endfor
                                @endfor
                            </select>
                            @error('appointment_time')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="label" for="status">
                                <span class="label-text">Statut *</span>
                            </label>
                            <select name="status" id="status" required
                                    class="select select-bordered w-full @error('status') select-error @enderror">
                                <option value="scheduled" {{ old('status', $appointment->status) == 'scheduled' ? 'selected' : '' }}>Planifié</option>
                                <option value="confirmed" {{ old('status', $appointment->status) == 'confirmed' ? 'selected' : '' }}>Confirmé</option>
                                <option value="completed" {{ old('status', $appointment->status) == 'completed' ? 'selected' : '' }}>Terminé</option>
                                <option value="cancelled" {{ old('status', $appointment->status) == 'cancelled' ? 'selected' : '' }}>Annulé</option>
                                <option value="no_show" {{ old('status', $appointment->status) == 'no_show' ? 'selected' : '' }}>Non présenté</option>
                            </select>
                            @error('status')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <div class="avatar placeholder">
                            <div class="bg-secondary/10 text-secondary w-12 rounded-lg">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold">Informations complémentaires</h3>
                            <p class="text-base-content/70">Motif et notes sur le rendez-vous</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <!-- Reason -->
                        <div>
                            <label class="label" for="reason">
                                <span class="label-text">Motif de consultation</span>
                            </label>
                            <input type="text" name="reason" id="reason" value="{{ old('reason', $appointment->reason) }}"
                                   placeholder="Ex: Consultation de routine, douleur abdominale..."
                                   class="input input-bordered w-full">
                        </div>

                        <!-- Notes -->
                        <div>
                            <label class="label" for="notes">
                                <span class="label-text">Notes</span>
                            </label>
                            <textarea name="notes" id="notes" rows="3"
                                      placeholder="Notes supplémentaires sur le rendez-vous..."
                                      class="textarea textarea-bordered w-full">{{ old('notes', $appointment->notes) }}</textarea>
                        </div>

                        @if(auth()->user()->role === 'doctor')
                        <!-- Doctor Notes (Only for doctors) -->
                        <div>
                            <label class="label" for="doctor_notes">
                                <span class="label-text">Notes médicales</span>
                            </label>
                            <textarea name="doctor_notes" id="doctor_notes" rows="3"
                                      placeholder="Notes médicales (visibles uniquement par les médecins)..."
                                      class="textarea textarea-bordered w-full">{{ old('doctor_notes', $appointment->doctor_notes) }}</textarea>
                        </div>

                        <!-- Diagnosis (Only for doctors) -->
                        <div>
                            <label class="label" for="diagnosis">
                                <span class="label-text">Diagnostic</span>
                            </label>
                            <textarea name="diagnosis" id="diagnosis" rows="3"
                                      placeholder="Diagnostic médical..."
                                      class="textarea textarea-bordered w-full">{{ old('diagnosis', $appointment->diagnosis) }}</textarea>
                        </div>
                        @endif

                        <!-- Follow-up -->
                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="checkbox" name="is_followup" id="is_followup" value="1" 
                                       {{ old('is_followup', $appointment->is_followup) ? 'checked' : '' }}
                                       class="checkbox checkbox-primary">
                                <span class="label-text">Il s'agit d'un rendez-vous de suivi</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card-actions justify-between items-center pt-6 border-t">
                    <div>
                        @if(!$appointment->payment)
                            <button type="button" onclick="confirmDelete()" class="btn btn-error btn-outline gap-2">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                Supprimer
                            </button>
                        @endif
                    </div>
                    <div class="flex gap-4">
                        <a href="{{ route('appointments.show', $appointment) }}" class="btn btn-ghost gap-2">
                            <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M7.793 2.232a.75.75 0 01-.025 1.06L3.622 7.25h10.003a5.375 5.375 0 010 10.75H10.75a.75.75 0 010-1.5h2.875a3.875 3.875 0 000-7.75H3.622l4.146 3.957a.75.75 0 01-1.036 1.085l-5.5-5.25a.75.75 0 010-1.085l5.5-5.25a.75.75 0 011.06.025z" clip-rule="evenodd" />
                            </svg>
                            Annuler
                        </a>
                        <button type="submit" class="btn btn-primary gap-2">
                            <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                            </svg>
                            Sauvegarder
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Delete Form (hidden) -->
        @if(!$appointment->payment)
            <form id="delete-form" action="{{ route('appointments.destroy', $appointment) }}" method="POST" class="hidden">
                @csrf
                @method('DELETE')
            </form>
        @endif
    </div>
</div>

<script>
function confirmDelete() {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce rendez-vous ? Cette action est irréversible.')) {
        document.getElementById('delete-form').submit();
    }
}
</script>
@endsection 