<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Appointment;
use App\Models\Patient;
use App\Models\LabPayment;
use App\Models\LabWorkOrder;
use App\Models\LabResult;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments.
     */
    public function index(Request $request)
    {
        $query = Payment::with(['appointment.patient', 'appointment.service', 'appointment.doctor.user', 'receiver'])
            ->orderBy('created_at', 'desc');

        // Filter by date
        if ($request->filled('date')) {
            $query->whereDate('payment_date', $request->date);
        } else {
            // Default to today's payments
            $query->whereDate('payment_date', today());
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Search by patient name or invoice number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('appointment.patient', function ($patientQuery) use ($search) {
                      $patientQuery->where('first_name', 'like', "%{$search}%")
                          ->orWhere('last_name', 'like', "%{$search}%")
                          ->orWhere('patient_number', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->paginate(20);

        return view('payments.index', compact('payments'));
    }

    /**
     * Show the form for creating a new payment.
     */
    public function create(Request $request)
    {
        // Get appointment if provided
        $appointment = null;
        if ($request->filled('appointment_id')) {
            $appointment = Appointment::with(['patient', 'service', 'doctor.user'])
                ->find($request->appointment_id);
        }

        // Get patients for manual payment entry
        $patients = Patient::orderBy('first_name')->get();

        return view('payments.create', compact('appointment', 'patients'));
    }

    /**
     * Store a newly created payment.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'appointment_id' => 'required|exists:appointments,id',
            'original_amount' => 'required|numeric|min:0',
            'apply_insurance' => 'boolean',
            'payment_method' => 'required|in:cash,mobile_money,card,insurance,other',
            'transaction_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        // Get appointment and check if payment already exists
        $appointment = Appointment::with(['patient', 'service'])->find($validated['appointment_id']);

        if ($appointment->payment) {
            return back()->withErrors(['appointment_id' => 'Ce rendez-vous a déjà été payé.'])
                ->withInput();
        }

        // Calculate insurance if applicable
        $originalAmount = $validated['original_amount'];
        $finalAmount = $originalAmount;
        $insuranceData = [
            'applied' => false,
            'type' => null,
            'number' => null,
            'coverage_percentage' => 0,
            'discount' => 0,
        ];

        if ($validated['apply_insurance'] ?? false) {
            $patient = $appointment->patient;
            if ($patient->hasValidInsurance()) {
                $insuranceCalculation = Payment::calculateInsuranceDiscount($patient, $originalAmount);
                $finalAmount = $insuranceCalculation['final_amount'];
                $insuranceData = [
                    'applied' => $insuranceCalculation['discount'] > 0,
                    'type' => $insuranceCalculation['insurance_type'],
                    'number' => $insuranceCalculation['insurance_number'],
                    'coverage_percentage' => $insuranceCalculation['coverage_percentage'],
                    'discount' => $insuranceCalculation['discount'],
                ];
            }
        }

        // Generate invoice number
        $invoiceNumber = $this->generateInvoiceNumber();

        // Create payment
        $payment = Payment::create([
            'invoice_number' => $invoiceNumber,
            'appointment_id' => $validated['appointment_id'],
            'patient_id' => $appointment->patient_id,
            'amount' => $finalAmount,
            'original_amount' => $originalAmount,
            'insurance_applied' => $insuranceData['applied'],
            'insurance_type' => $insuranceData['type'],
            'insurance_number' => $insuranceData['number'],
            'insurance_coverage_percentage' => $insuranceData['coverage_percentage'],
            'insurance_discount' => $insuranceData['discount'],
            'payment_method' => $validated['payment_method'],
            'transaction_reference' => $validated['transaction_reference'],
            'status' => 'completed',
            'received_by' => Auth::id(),
            'notes' => $validated['notes'],
            'payment_date' => now(),
        ]);

        // Update appointment status
        $appointment->update(['status' => 'confirmed']);

        $successMessage = 'Paiement enregistré avec succès.';
        if ($insuranceData['applied']) {
            $successMessage .= ' Réduction d\'assurance appliquée: ' . number_format($insuranceData['discount'], 2) . ' DH.';
        }

        return redirect()->route('payments.show', $payment)
            ->with('success', $successMessage);
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $payment->load(['appointment.patient', 'appointment.service', 'appointment.doctor.user', 'receiver']);

        return view('payments.show', compact('payment'));
    }

    /**
     * Show the form for editing the payment.
     */
    public function edit(Payment $payment)
    {
        $payment->load(['appointment.patient', 'appointment.service']);

        return view('payments.edit', compact('payment'));
    }

    /**
     * Update the specified payment.
     */
    public function update(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,mobile_money,card,insurance,other',
            'transaction_reference' => 'nullable|string|max:255',
            'status' => 'required|in:pending,completed,failed,refunded',
            'notes' => 'nullable|string',
        ]);

        $payment->update($validated);

        return redirect()->route('payments.show', $payment)
            ->with('success', 'Paiement mis à jour avec succès.');
    }

    /**
     * Generate and download payment receipt PDF.
     */
    public function downloadReceipt(Payment $payment)
    {
        $payment->load(['appointment.patient', 'appointment.service', 'appointment.doctor.user', 'receiver']);

        $pdf = Pdf::loadView('payments.receipt', compact('payment'));
        
        $filename = "recu_paiement_{$payment->invoice_number}.pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Display payment receipt in browser.
     */
    public function showReceipt(Payment $payment)
    {
        $payment->load(['appointment.patient', 'appointment.service', 'appointment.doctor.user', 'receiver']);

        return view('payments.receipt', compact('payment'));
    }

    /**
     * Get payments for a specific appointment.
     */
    public function getByAppointment(Appointment $appointment)
    {
        $payment = $appointment->payment;
        
        if (!$payment) {
            return response()->json(['message' => 'Aucun paiement trouvé pour ce rendez-vous.'], 404);
        }

        return response()->json($payment);
    }

    /**
     * Generate unique invoice number.
     */
    private function generateInvoiceNumber(): string
    {
        $year = date('Y');
        $month = date('m');
        
        // Get the last invoice number for this month
        $lastPayment = Payment::where('invoice_number', 'like', "INV{$year}{$month}%")
            ->orderBy('invoice_number', 'desc')
            ->first();

        if ($lastPayment) {
            $lastNumber = (int) substr($lastPayment->invoice_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return "INV{$year}{$month}" . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get payment statistics.
     */
    public function getStats()
    {
        $today = today();
        $thisMonth = now()->startOfMonth();
        
        $stats = [
            'total_today' => Payment::whereDate('payment_date', $today)
                ->where('status', 'completed')
                ->sum('amount'),
            'count_today' => Payment::whereDate('payment_date', $today)
                ->where('status', 'completed')
                ->count(),
            'total_month' => Payment::where('payment_date', '>=', $thisMonth)
                ->where('status', 'completed')
                ->sum('amount'),
            'pending_amount' => Payment::where('status', 'pending')->sum('amount'),
            'payment_methods' => Payment::whereDate('payment_date', $today)
                ->where('status', 'completed')
                ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('payment_method')
                ->get(),
        ];

        return $stats;
    }

    /**
     * Get daily payment summary.
     */
    public function getDailySummary(Request $request)
    {
        $date = $request->date ?? today();
        
        $summary = Payment::whereDate('payment_date', $date)
            ->where('status', 'completed')
            ->selectRaw('
                payment_method,
                COUNT(*) as count,
                SUM(amount) as total,
                AVG(amount) as average
            ')
            ->groupBy('payment_method')
            ->get();

        $totalAmount = $summary->sum('total');
        $totalCount = $summary->sum('count');

        return response()->json([
            'date' => $date,
            'summary' => $summary,
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
        ]);
    }

    /**
     * Process refund for a payment.
     */
    public function refund(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'refund_reason' => 'required|string|max:500',
        ]);

        if ($payment->status !== 'completed') {
            return back()->withErrors(['status' => 'Seuls les paiements complétés peuvent être remboursés.']);
        }

        $payment->update([
            'status' => 'refunded',
            'notes' => ($payment->notes ? $payment->notes . "\n\n" : '') . 
                      "REMBOURSEMENT: " . $validated['refund_reason'] . " (par " . Auth::user()->name . " le " . now()->format('d/m/Y H:i') . ")"
        ]);

        // Update appointment status
        $payment->appointment->update(['status' => 'cancelled']);

        return redirect()->route('payments.show', $payment)
            ->with('success', 'Paiement remboursé avec succès.');
    }

    /**
     * Show lab payment form for a prescription.
     */
    public function createLabPayment(Request $request)
    {
        $prescriptionNumber = $request->get('prescription_number');

        // Si aucun numéro de prescription, afficher la page de recherche
        if (!$prescriptionNumber) {
            return view('payments.lab.search');
        }

        // Get lab results for this prescription
        $labResults = LabResult::with(['labTest', 'patient', 'doctor.user', 'appointment'])
            ->where('prescription_number', $prescriptionNumber)
            ->get();

        if ($labResults->isEmpty()) {
            return view('payments.lab.search', [
                'error' => 'Aucune analyse trouvée pour la prescription N° ' . $prescriptionNumber,
                'prescriptionNumber' => $prescriptionNumber
            ]);
        }

        // Check if already paid
        $existingPayment = LabPayment::where('prescription_number', $prescriptionNumber)->first();
        if ($existingPayment) {
            return redirect()->route('payments.lab.show', $existingPayment)
                ->with('info', 'Cette prescription a déjà été payée.');
        }

        $patient = $labResults->first()->patient;
        $totalAmount = $labResults->sum(function($result) {
            return $result->labTest->price ?? 0;
        });

        return view('payments.lab.create', compact(
            'labResults',
            'patient',
            'prescriptionNumber',
            'totalAmount'
        ));
    }

    /**
     * Store lab payment and generate work order.
     */
    public function storeLabPayment(Request $request)
    {
        $validated = $request->validate([
            'prescription_number' => 'required|string',
            'patient_id' => 'required|exists:patients,id',
            'lab_tests' => 'required|array',
            'original_amount' => 'required|numeric|min:0',
            'apply_insurance' => 'boolean',
            'payment_method' => 'required|in:cash,mobile_money,card,insurance,credit',
            'transaction_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // Check if already paid
            $existingPayment = LabPayment::where('prescription_number', $validated['prescription_number'])->first();
            if ($existingPayment) {
                return back()->withErrors(['prescription_number' => 'Cette prescription a déjà été payée.']);
            }

            // Get patient and lab tests data
            $patient = Patient::findOrFail($validated['patient_id']);
            $originalAmount = $validated['original_amount'];
            $finalAmount = $originalAmount;

            // Prepare lab tests data for insurance calculation
            $labTestsData = [];
            foreach ($validated['lab_tests'] as $testId => $testData) {
                $labTestsData[] = [
                    'id' => $testId,
                    'name' => $testData['name'] ?? '',
                    'price' => $testData['price'] ?? 0,
                ];
            }

            // Calculate insurance if applicable
            $insuranceData = [
                'applied' => false,
                'type' => null,
                'number' => null,
                'coverage_percentage' => 0,
                'discount' => 0,
                'details' => [],
            ];

            if ($validated['apply_insurance'] ?? false) {
                if ($patient->hasValidInsurance()) {
                    $insuranceCalculation = LabPayment::calculateInsuranceDiscount($patient, $labTestsData);
                    $finalAmount = $insuranceCalculation['final_amount'];
                    $insuranceData = [
                        'applied' => $insuranceCalculation['total_discount'] > 0,
                        'type' => $insuranceCalculation['insurance_type'],
                        'number' => $insuranceCalculation['insurance_number'],
                        'coverage_percentage' => $insuranceCalculation['coverage_percentage'],
                        'discount' => $insuranceCalculation['total_discount'],
                        'details' => $insuranceCalculation['details'],
                    ];
                }
            }

            // Create lab payment
            $labPayment = LabPayment::create([
                'payment_number' => LabPayment::generatePaymentNumber(),
                'prescription_number' => $validated['prescription_number'],
                'patient_id' => $validated['patient_id'],
                'appointment_id' => $request->appointment_id,
                'lab_tests' => $validated['lab_tests'],
                'total_amount' => $finalAmount,
                'original_amount' => $originalAmount,
                'insurance_applied' => $insuranceData['applied'],
                'insurance_type' => $insuranceData['type'],
                'insurance_number' => $insuranceData['number'],
                'insurance_coverage_percentage' => $insuranceData['coverage_percentage'],
                'insurance_discount' => $insuranceData['discount'],
                'insurance_details' => $insuranceData['details'],
                'payment_method' => $validated['payment_method'],
                'transaction_reference' => $validated['transaction_reference'],
                'status' => 'paid',
                'received_by' => Auth::id(),
                'notes' => $validated['notes'],
                'payment_date' => now(),
            ]);

            // Create work order for laboratory
            $workOrder = LabWorkOrder::create([
                'work_order_number' => LabWorkOrder::generateWorkOrderNumber(),
                'lab_payment_id' => $labPayment->id,
                'patient_id' => $validated['patient_id'],
                'prescription_number' => $validated['prescription_number'],
                'lab_tests_details' => $validated['lab_tests'],
                'clinical_information' => $validated['lab_tests'][0]['clinical_information'] ?? null,
                'priority' => $validated['lab_tests'][0]['urgency'] ?? 'normal',
                'status' => 'pending',
            ]);

            // Update lab payment with work order generation time
            $labPayment->update(['work_order_generated_at' => now()]);

            DB::commit();

            return redirect()->route('payments.lab.show', $labPayment)
                ->with('success', 'Paiement enregistré avec succès. Bon de travail généré.');

        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Une erreur est survenue lors du paiement: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Show lab payment details.
     */
    public function showLabPayment(LabPayment $labPayment)
    {
        $labPayment->load([
            'patient',
            'appointment.doctor.user',
            'receiver',
            'workOrder',
            'labResults.labTest'
        ]);

        return view('payments.lab.show', compact('labPayment'));
    }

    /**
     * Generate lab payment receipt PDF.
     */
    public function downloadLabReceipt(LabPayment $labPayment)
    {
        $labPayment->load([
            'patient',
            'appointment.doctor.user',
            'receiver',
            'workOrder'
        ]);

        $pdf = Pdf::loadView('payments.lab.receipt', compact('labPayment'));

        $filename = "recu_analyses_{$labPayment->payment_number}.pdf";

        return $pdf->download($filename);
    }
}
