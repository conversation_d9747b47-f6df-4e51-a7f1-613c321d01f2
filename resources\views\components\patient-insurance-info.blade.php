@props(['patient', 'showDetails' => true])

@php
    $insuranceDetails = $patient->getInsuranceDetails();
@endphp

<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Informations d'Assurance
        </h3>
        
        @if($insuranceDetails && $insuranceDetails['is_valid'])
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Valide
            </span>
        @elseif($patient->has_insurance)
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                Expirée/Invalide
            </span>
        @else
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Aucune assurance
            </span>
        @endif
    </div>

    @if($patient->has_insurance)
        <div class="space-y-3">
            @if($showDetails)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Type d'assurance</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $patient->insurance_type }}</p>
                    </div>
                    
                    @if($patient->insurance_name)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Nom de l'assurance</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $patient->insurance_name }}</p>
                        </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Numéro d'assurance</label>
                        <p class="mt-1 text-sm text-gray-900 font-mono">{{ $patient->insurance_number }}</p>
                    </div>
                    
                    @if($patient->nina)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">NINA</label>
                            <p class="mt-1 text-sm text-gray-900 font-mono">{{ $patient->nina }}</p>
                        </div>
                    @endif
                    
                    @if($insuranceDetails)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Couverture</label>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ number_format($insuranceDetails['coverage_percentage'], 1) }}%
                                @if($insuranceDetails['coverage_type'] === 'variable')
                                    <span class="text-xs text-gray-500">(variable)</span>
                                @else
                                    <span class="text-xs text-gray-500">(fixe)</span>
                                @endif
                            </p>
                        </div>
                    @endif
                    
                    @if($patient->insurance_start_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date de début</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $patient->insurance_start_date->format('d/m/Y') }}</p>
                        </div>
                    @endif
                    
                    @if($patient->insurance_expiry_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date d'expiration</label>
                            <p class="mt-1 text-sm text-gray-900 {{ $patient->insurance_expiry_date->isPast() ? 'text-red-600' : '' }}">
                                {{ $patient->insurance_expiry_date->format('d/m/Y') }}
                                @if($patient->insurance_expiry_date->isPast())
                                    <span class="text-xs">(Expirée)</span>
                                @endif
                            </p>
                        </div>
                    @endif
                </div>
                
                @if($patient->insurance_notes)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Notes</label>
                        <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-2 rounded">{{ $patient->insurance_notes }}</p>
                    </div>
                @endif
            @else
                <!-- Vue compacte -->
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-900">{{ $patient->insurance_type }}</p>
                        <p class="text-xs text-gray-500">{{ $patient->insurance_number }}</p>
                    </div>
                    @if($insuranceDetails)
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">{{ number_format($insuranceDetails['coverage_percentage'], 1) }}%</p>
                            <p class="text-xs text-gray-500">de couverture</p>
                        </div>
                    @endif
                </div>
            @endif
        </div>
    @else
        <div class="text-center py-4">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <p class="mt-2 text-sm text-gray-500">Aucune assurance enregistrée</p>
        </div>
    @endif
</div>
