@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Créer un Service Médical
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Ajouter un nouveau service à la clinique
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0">
                <a href="{{ route('admin.services') }}" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Retour
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <form action="{{ route('admin.services.store') }}" method="POST">
                        @csrf

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Name -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nom du service *</span>
                                </label>
                                <input type="text" name="name" value="{{ old('name') }}" 
                                       class="input input-bordered @error('name') input-error @enderror" 
                                       placeholder="Ex: Cardiologie, Pédiatrie..." required>
                                @error('name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Service Code -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Code du service *</span>
                                </label>
                                <input type="text" name="service_code" value="{{ old('service_code') }}" 
                                       class="input input-bordered @error('service_code') input-error @enderror" 
                                       placeholder="Ex: CARD, PEDI, GYNE..." required>
                                @error('service_code')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Price -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Prix de consultation *</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" name="price" value="{{ old('price') }}" 
                                           class="input input-bordered @error('price') input-error @enderror" 
                                           placeholder="0" min="0" step="100" required>
                                    <span class="bg-base-200 px-4 py-3 text-sm">FCFA</span>
                                </div>
                                @error('price')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Statut</span>
                                </label>
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" name="is_active" value="1" 
                                           class="checkbox checkbox-primary" 
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <span class="label-text ml-2">Service actif</span>
                                </label>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-control mt-6">
                            <label class="label">
                                <span class="label-text">Description</span>
                            </label>
                            <textarea name="description" class="textarea textarea-bordered @error('description') textarea-error @enderror" 
                                      placeholder="Description détaillée du service médical...">{{ old('description') }}</textarea>
                            @error('description')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="form-control mt-8">
                            <button type="submit" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Créer le service
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Service Examples -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title">Exemples de Services Médicaux</h3>
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 mt-4">
                        <div class="p-4 border border-base-300 rounded-lg">
                            <h4 class="font-semibold">Cardiologie</h4>
                            <p class="text-sm text-gray-500">Code: CARD</p>
                            <p class="text-sm">Consultation cardiaque, ECG, échographie cardiaque</p>
                        </div>
                        <div class="p-4 border border-base-300 rounded-lg">
                            <h4 class="font-semibold">Pédiatrie</h4>
                            <p class="text-sm text-gray-500">Code: PEDI</p>
                            <p class="text-sm">Soins pour enfants de 0 à 18 ans</p>
                        </div>
                        <div class="p-4 border border-base-300 rounded-lg">
                            <h4 class="font-semibold">Gynécologie</h4>
                            <p class="text-sm text-gray-500">Code: GYNE</p>
                            <p class="text-sm">Santé reproductive féminine</p>
                        </div>
                        <div class="p-4 border border-base-300 rounded-lg">
                            <h4 class="font-semibold">Médecine Générale</h4>
                            <p class="text-sm text-gray-500">Code: GENE</p>
                            <p class="text-sm">Consultation générale, suivi médical</p>
                        </div>
                        <div class="p-4 border border-base-300 rounded-lg">
                            <h4 class="font-semibold">Dermatologie</h4>
                            <p class="text-sm text-gray-500">Code: DERM</p>
                            <p class="text-sm">Soins de la peau et des phanères</p>
                        </div>
                        <div class="p-4 border border-base-300 rounded-lg">
                            <h4 class="font-semibold">Ophtalmologie</h4>
                            <p class="text-sm text-gray-500">Code: OPHT</p>
                            <p class="text-sm">Soins des yeux et de la vision</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
