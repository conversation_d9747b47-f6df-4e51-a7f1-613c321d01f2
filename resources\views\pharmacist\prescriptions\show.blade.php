@extends('layouts.app')

@section('title', '<PERSON>étails Ordonnance - ' . $prescription->prescription_number)

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Ordonnance {{ $prescription->prescription_number }}</h1>
            <p class="text-gray-600 mt-1">Détails et dispensation</p>
        </div>
        <div class="flex space-x-3">
            @if($prescription->status == 'dispensed')
                <a href="{{ route('pharmacist.prescriptions.receipt', $prescription) }}" class="btn btn-outline btn-info">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Voir Reçu
                </a>
            @endif
            <a href="{{ route('pharmacist.prescriptions.index') }}" class="btn btn-ghost">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Retour
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Patient Information -->
        <div class="lg:col-span-1">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-lg mb-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Informations Patient
                    </h2>
                    
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <div class="avatar placeholder">
                                <div class="bg-neutral-focus text-neutral-content rounded-full w-12">
                                    <span class="text-lg">{{ substr($prescription->patient->first_name, 0, 1) }}{{ substr($prescription->patient->last_name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="font-bold text-lg">{{ $prescription->patient->first_name }} {{ $prescription->patient->last_name }}</div>
                                <div class="text-sm text-gray-500">{{ $prescription->patient->patient_number }}</div>
                            </div>
                        </div>
                        
                        <div class="divider my-2"></div>
                        
                        <div class="grid grid-cols-2 gap-2 text-sm">
                            <div>
                                <span class="font-medium">Âge:</span>
                                <span class="ml-1">{{ $prescription->patient->age ?? 'N/A' }} ans</span>
                            </div>
                            <div>
                                <span class="font-medium">Sexe:</span>
                                <span class="ml-1">{{ $prescription->patient->gender == 'M' ? 'Masculin' : 'Féminin' }}</span>
                            </div>
                            <div class="col-span-2">
                                <span class="font-medium">Téléphone:</span>
                                <span class="ml-1">{{ $prescription->patient->phone ?? 'N/A' }}</span>
                            </div>
                            @if($prescription->patient->allergies)
                            <div class="col-span-2">
                                <span class="font-medium text-warning">Allergies:</span>
                                <span class="ml-1 text-warning">{{ $prescription->patient->allergies }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prescription Info -->
            <div class="card bg-base-100 shadow-xl mt-6">
                <div class="card-body">
                    <h2 class="card-title text-lg mb-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Informations Ordonnance
                    </h2>
                    
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="font-medium">Numéro:</span>
                            <span class="ml-1 text-primary font-mono">{{ $prescription->prescription_number }}</span>
                        </div>
                        <div>
                            <span class="font-medium">Date:</span>
                            <span class="ml-1">{{ $prescription->created_at->format('d/m/Y à H:i') }}</span>
                        </div>
                        @if($prescription->appointment && $prescription->appointment->doctor)
                        <div>
                            <span class="font-medium">Médecin:</span>
                            <span class="ml-1">Dr. {{ $prescription->appointment->doctor->first_name }} {{ $prescription->appointment->doctor->last_name }}</span>
                        </div>
                        <div>
                            <span class="font-medium">Spécialité:</span>
                            <span class="ml-1">{{ $prescription->appointment->doctor->specialization }}</span>
                        </div>
                        @endif
                        <div>
                            <span class="font-medium">Diagnostic:</span>
                            <span class="ml-1">{{ $prescription->diagnosis ?? 'Non spécifié' }}</span>
                        </div>
                        @if($prescription->notes)
                        <div>
                            <span class="font-medium">Notes:</span>
                            <span class="ml-1">{{ $prescription->notes }}</span>
                        </div>
                        @endif
                        <div>
                            <span class="font-medium">Statut:</span>
                            @if($prescription->status == 'pending')
                                <span class="badge badge-warning ml-1">En attente</span>
                            @elseif($prescription->status == 'dispensed')
                                <span class="badge badge-success ml-1">Dispensée</span>
                            @elseif($prescription->status == 'cancelled')
                                <span class="badge badge-error ml-1">Annulée</span>
                            @endif
                        </div>
                        @if($prescription->status == 'dispensed' && $prescription->dispensed_at)
                        <div>
                            <span class="font-medium">Dispensée le:</span>
                            <span class="ml-1">{{ $prescription->dispensed_at->format('d/m/Y à H:i') }}</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Medications List -->
        <div class="lg:col-span-2">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-lg mb-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                        </svg>
                        Médicaments Prescrits
                    </h2>
                    
                    @if($prescription->status == 'pending')
                    <form action="{{ route('pharmacist.prescriptions.dispense', $prescription) }}" method="POST" id="dispenseForm">
                        @csrf
                    @endif
                    
                    <!-- Medication Search Section (for reference) -->
                    @if($prescription->status == 'pending')
                    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 class="font-medium text-blue-900 mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Recherche de Médicaments (Référence)
                        </h3>
                        <div class="relative">
                            <input type="text" 
                                   id="medicationSearch" 
                                   placeholder="Rechercher un médicament par nom, code ou principe actif..." 
                                   class="input input-bordered w-full pr-10"
                                   autocomplete="off">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        
                        <!-- Search Results -->
                        <div id="medicationResults" class="hidden mt-2 max-h-60 overflow-y-auto border border-gray-200 rounded-lg bg-white shadow-lg">
                            <!-- Results will be populated by JavaScript -->
                        </div>
                        
                        <p class="text-xs text-blue-700 mt-2">
                            💡 Utilisez cette recherche pour vérifier la disponibilité des médicaments ou trouver des alternatives.
                        </p>
                    </div>
                    @endif
                    
                    <div class="space-y-4">
                        @foreach($prescription->prescriptionItems as $item)
                        <div class="border border-gray-200 rounded-lg p-6 {{ $prescription->status == 'pending' ? 'bg-yellow-50' : 'bg-gray-50' }} hover:shadow-md transition-shadow">
                            <!-- Medication Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="font-bold text-xl text-gray-900 mb-2">{{ $item->medication_name }}</h3>
                                    @if($item->medication_form)
                                        <span class="badge badge-outline badge-primary">{{ $item->medication_form }}</span>
                                    @endif
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-primary">{{ $item->quantity }} unité(s)</div>
                                    <div class="text-sm text-gray-600">{{ number_format($item->unit_price, 0, ',', ' ') }} FCFA/unité</div>
                                    <div class="text-lg font-bold text-success">{{ number_format($item->total_price, 0, ',', ' ') }} FCFA</div>
                                </div>
                            </div>
                            
                            <!-- Prescription Details Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                @if($item->dosage)
                                <div class="bg-white p-3 rounded-lg border">
                                    <div class="flex items-center mb-1">
                                        <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                                        </svg>
                                        <span class="text-xs font-medium text-gray-500 uppercase">Dosage</span>
                                    </div>
                                    <p class="font-medium text-gray-900">{{ $item->dosage }}</p>
                                </div>
                                @endif
                                
                                @if($item->frequency)
                                <div class="bg-white p-3 rounded-lg border">
                                    <div class="flex items-center mb-1">
                                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="text-xs font-medium text-gray-500 uppercase">Fréquence</span>
                                    </div>
                                    <p class="font-medium text-gray-900">{{ $item->frequency }}</p>
                                </div>
                                @endif
                                
                                @if($item->duration)
                                <div class="bg-white p-3 rounded-lg border">
                                    <div class="flex items-center mb-1">
                                        <svg class="w-4 h-4 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span class="text-xs font-medium text-gray-500 uppercase">Durée</span>
                                    </div>
                                    <p class="font-medium text-gray-900">{{ $item->duration }}</p>
                                </div>
                                @endif
                            </div>
                            
                            <!-- Instructions -->
                            @if($item->instructions)
                            <div class="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-amber-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <p class="text-sm font-medium text-amber-800 mb-1">Instructions spéciales:</p>
                                        <p class="text-sm text-amber-700">{{ $item->instructions }}</p>
                                    </div>
                                </div>
                            </div>
                            @endif
                            
                            <!-- Stock and Dispensing Section -->
                            <div class="flex justify-between items-end">
                                
                                <div class="text-right ml-4">
                                    <div class="text-lg font-bold">{{ $item->quantity }} unité(s)</div>
                                    <div class="text-sm text-gray-600">{{ number_format($item->unit_price, 0, ',', ' ') }} FCFA/unité</div>
                                    <div class="text-lg font-bold text-primary">{{ number_format($item->total_price, 0, ',', ' ') }} FCFA</div>
                                    
                                    @if($prescription->status == 'pending')
                                        @php
                                            $availableStock = $stockInfo[$item->medication_id] ?? 0;
                                        @endphp
                                        <div class="mt-2">
                                            @if($availableStock >= $item->quantity)
                                                <span class="badge badge-success">Stock OK ({{ $availableStock }})</span>
                                            @elseif($availableStock > 0)
                                                <span class="badge badge-warning">Stock insuffisant ({{ $availableStock }})</span>
                                            @else
                                                <span class="badge badge-error">Rupture de stock</span>
                                            @endif
                                        </div>
                                        
                                        @if($availableStock > 0)
                                        <div class="mt-2">
                                            <label class="label">
                                                <span class="label-text text-xs">Quantité à dispenser</span>
                                            </label>
                                            <input type="number" 
                                                   name="prescriptionItems[{{ $item->id }}][quantity_dispensed]" 
                                                   value="{{ min($item->quantity, $availableStock) }}"
                                                   max="{{ min($item->quantity, $availableStock) }}"
                                                   min="0"
                                                   class="input input-bordered input-sm w-20"
                                                   required>
                                            <input type="hidden" name="prescriptionItems[{{ $item->id }}][price]" value="{{ $item->unit_price }}">
                                        </div>
                                        @else
                                        <input type="hidden" name="prescriptionItems[{{ $item->id }}][quantity_dispensed]" value="0">
                                        <input type="hidden" name="prescriptionItems[{{ $item->id }}][price]" value="{{ $item->unit_price }}">
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Insurance Section -->
                    @if($prescription->patient->has_insurance)
                    <div class="divider"></div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-3">Informations Assurance</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium">Type:</span> {{ $prescription->patient->insurance_type }}
                            </div>
                            <div>
                                <span class="font-medium">Numéro:</span> {{ $prescription->patient->insurance_number }}
                            </div>
                            <div>
                                <span class="font-medium">Couverture:</span> 
                                @php
                                    $coveragePercentage = \App\Models\InsuranceCoverage::getCoveragePercentage($prescription->patient->insurance_type);
                                @endphp
                                {{ $coveragePercentage }}%
                            </div>
                            <div>
                                <span class="font-medium">Expire le:</span> {{ $prescription->patient->insurance_expiry_date?->format('d/m/Y') }}
                            </div>
                        </div>
                        
                        @if($prescription->patient->insurance_expiry_date && $prescription->patient->insurance_expiry_date->isFuture())
                        <div class="form-control mt-3">
                            <label class="label cursor-pointer justify-start">
                                <input type="checkbox" name="apply_insurance" value="1" class="checkbox checkbox-primary mr-3" id="applyInsurance">
                                <span class="label-text font-medium">Appliquer la réduction d'assurance ({{ $coveragePercentage }}%)</span>
                            </label>
                        </div>
                        @else
                        <div class="alert alert-warning mt-3">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Assurance expirée - Pas de réduction applicable</span>
                        </div>
                        @endif
                    </div>
                    @endif
                    
                    <!-- Total -->
                    <div class="divider"></div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Sous-total:</span>
                                <span id="subtotal">0 FCFA</span>
                            </div>
                            <div class="flex justify-between text-green-600" id="discountRow" style="display: none;">
                                <span>Réduction assurance:</span>
                                <span id="discount">0 FCFA</span>
                            </div>
                            <div class="divider my-2"></div>
                            <div class="flex justify-between items-center text-xl font-bold">
                                <span>Total à payer:</span>
                                <span class="text-primary" id="totalAmount">0 FCFA</span>
                            </div>
                        </div>
                    </div>
                    
                    @if($prescription->status == 'pending')
                    <div class="divider"></div>
                    
                    <!-- Payment Method -->
                    <div class="form-control w-full max-w-xs">
                        <label class="label">
                            <span class="label-text font-medium">Méthode de paiement</span>
                        </label>
                        <select name="payment_method" class="select select-bordered" required>
                            <option value="">Choisir une méthode</option>
                            <option value="cash">Espèces</option>
                            <option value="mobile_money">Mobile Money</option>
                        </select>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="card-actions justify-end mt-6">
                        <button type="button" class="btn btn-outline" onclick="window.history.back()">
                            Annuler
                        </button>
                        <button type="submit" class="btn btn-primary" id="dispenseBtn">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Dispenser l'Ordonnance
                        </button>
                    </div>
                    
                    </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@if($prescription->status == 'pending')
<script>
// Variables globales
@php
    $patientCoverage = 0;
    if ($prescription->patient->has_insurance && $prescription->patient->insurance_expiry_date && $prescription->patient->insurance_expiry_date->isFuture()) {
        $patientCoverage = \App\Models\InsuranceCoverage::getCoveragePercentage($prescription->patient->insurance_type);
    }
@endphp
const insuranceCoverage = {{ $patientCoverage }};

// Fonction pour calculer le total
function calculateTotal() {
    let subtotal = 0;
    
    // Calculer le sous-total
    document.querySelectorAll('input[name^="prescriptionItems"][name$="[quantity_dispensed]"]').forEach(quantityInput => {
        const quantity = parseFloat(quantityInput.value) || 0;
        const itemId = quantityInput.name.match(/\[(\d+)\]/)[1];
        const priceInput = document.querySelector(`input[name="prescriptionItems[${itemId}][price]"]`);
        const price = parseFloat(priceInput.value) || 0;
        
        subtotal += quantity * price;
    });
    
    // Calculer la réduction d'assurance
    const applyInsurance = document.getElementById('applyInsurance')?.checked || false;
    const discount = applyInsurance ? (subtotal * insuranceCoverage / 100) : 0;
    const total = subtotal - discount;
    
    // Mettre à jour l'affichage
    document.getElementById('subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('discount').textContent = formatCurrency(discount);
    document.getElementById('totalAmount').textContent = formatCurrency(total);
    
    // Afficher/masquer la ligne de réduction
    const discountRow = document.getElementById('discountRow');
    if (discount > 0) {
        discountRow.style.display = 'flex';
    } else {
        discountRow.style.display = 'none';
    }
}

// Fonction pour formater la devise
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount) + ' FCFA';
}

// Écouter les changements sur les quantités et prix
document.querySelectorAll('input[name^="prescriptionItems"]').forEach(input => {
    input.addEventListener('input', calculateTotal);
});

// Écouter les changements sur la case d'assurance
if (document.getElementById('applyInsurance')) {
    document.getElementById('applyInsurance').addEventListener('change', calculateTotal);
}

// Calculer le total initial
calculateTotal();

// Fonctionnalité de recherche de médicaments
const medicationSearch = document.getElementById('medicationSearch');
const medicationResults = document.getElementById('medicationResults');

if (medicationSearch && medicationResults) {
    let searchTimeout;
    
    medicationSearch.addEventListener('input', function() {
        const query = this.value.trim();
        
        // Effacer le timeout précédent
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
            medicationResults.classList.add('hidden');
            return;
        }
        
        // Délai pour éviter trop de requêtes
        searchTimeout = setTimeout(() => {
            searchMedications(query);
        }, 300);
    });
    
    // Cacher les résultats quand on clique ailleurs
    document.addEventListener('click', function(e) {
        if (!medicationSearch.contains(e.target) && !medicationResults.contains(e.target)) {
            medicationResults.classList.add('hidden');
        }
    });
}

function searchMedications(query) {
    fetch(`/pharmacist/search-medications?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        displayMedicationResults(data);
    })
    .catch(error => {
        console.error('Erreur lors de la recherche:', error);
        medicationResults.innerHTML = '<div class="p-3 text-red-600">Erreur lors de la recherche</div>';
        medicationResults.classList.remove('hidden');
    });
}

function displayMedicationResults(medications) {
    if (medications.length === 0) {
        medicationResults.innerHTML = '<div class="p-3 text-gray-500">Aucun médicament trouvé</div>';
    } else {
        const html = medications.map(med => `
            <div class="p-3 border-b border-gray-200 hover:bg-gray-50 cursor-pointer" 
                 onclick="selectMedication('${med.name}', '${med.form}', ${med.price}, ${med.stock})">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">${med.name}</div>
                        <div class="text-sm text-gray-600">${med.form || 'Forme non spécifiée'}</div>
                        <div class="text-sm text-blue-600">${formatCurrency(med.price)}</div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm ${
                            med.stock > 10 ? 'text-green-600' : 
                            med.stock > 0 ? 'text-orange-600' : 'text-red-600'
                        }">
                            Stock: ${med.stock}
                        </div>
                        <div class="text-xs text-gray-500">${med.code || ''}</div>
                    </div>
                </div>
            </div>
        `).join('');
        
        medicationResults.innerHTML = html;
    }
    
    medicationResults.classList.remove('hidden');
}

function selectMedication(name, form, price, stock) {
    // Cette fonction peut être utilisée pour des actions futures
    // Pour l'instant, on affiche juste les informations
    const info = `Médicament sélectionné:\n\nNom: ${name}\nForme: ${form || 'Non spécifiée'}\nPrix: ${formatCurrency(price)}\nStock: ${stock} unités`;
    alert(info);
    
    // Cacher les résultats
    medicationResults.classList.add('hidden');
    
    // Optionnel: vider le champ de recherche
    medicationSearch.value = '';
}

// Gestion de la soumission du formulaire
document.getElementById('dispenseForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Vérifier si au moins une quantité > 0
    const quantities = document.querySelectorAll('input[name^="prescriptionItems"][name$="[quantity_dispensed]"]');
    let hasQuantity = false;
    
    quantities.forEach(input => {
        if (parseFloat(input.value) > 0) {
            hasQuantity = true;
        }
    });
    
    if (!hasQuantity) {
        alert('Veuillez spécifier au moins une quantité à dispenser.');
        return;
    }
    
    // Vérifier la méthode de paiement
    const paymentMethod = document.querySelector('select[name="payment_method"]').value;
    if (!paymentMethod) {
        alert('Veuillez sélectionner une méthode de paiement.');
        return;
    }
    
    // Confirmation avec détails
    const total = document.getElementById('totalAmount').textContent;
    const applyInsurance = document.getElementById('applyInsurance')?.checked || false;
    let confirmMessage = `Êtes-vous sûr de vouloir dispenser cette ordonnance ?\n\nTotal à payer: ${total}`;
    
    if (applyInsurance) {
        const discount = document.getElementById('discount').textContent;
        confirmMessage += `\nRéduction assurance: ${discount}`;
    }
    
    confirmMessage += '\n\nCette action est irréversible.';
    
    if (confirm(confirmMessage)) {
        this.submit();
    }
});
</script>
@endif
@endsection