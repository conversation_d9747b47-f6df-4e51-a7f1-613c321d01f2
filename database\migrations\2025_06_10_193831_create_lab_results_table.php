<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lab_results', function (Blueprint $table) {
            $table->id();
            $table->string('result_number')->unique();
            $table->string('prescription_number')->nullable(); // Numéro de prescription groupée
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('restrict');
            $table->foreignId('patient_id')->constrained()->onDelete('restrict');
            $table->foreignId('doctor_id')->constrained()->onDelete('restrict');
            $table->foreignId('lab_test_id')->constrained()->onDelete('restrict');
            $table->text('result')->nullable();
            $table->text('interpretation')->nullable();
            $table->text('clinical_information')->nullable(); // Informations cliniques du médecin
            $table->text('notes')->nullable();
            $table->enum('urgency', ['normal', 'urgent', 'stat'])->default('normal'); // Urgence
            $table->foreignId('performed_by')->nullable()->constrained('users')->onDelete('restrict');
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->enum('status', ['ordered', 'sample_collected', 'processing', 'completed', 'cancelled'])->default('ordered');
            $table->dateTime('ordered_at');
            $table->dateTime('collected_at')->nullable();
            $table->dateTime('completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lab_results');
    }
};