<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $doctorRole = Role::create(['name' => 'doctor']);
        $receptionistRole = Role::create(['name' => 'receptionist']);
        $nurseRole = Role::create(['name' => 'nurse']);
        $pharmacistRole = Role::create(['name' => 'pharmacist']);
        $labTechnicianRole = Role::create(['name' => 'lab_technician']);
        $accountantRole = Role::create(['name' => 'accountant']);
        $patientRole = Role::create(['name' => 'patient']);

        // Create permissions
        // Users
        Permission::create(['name' => 'create users']);
        Permission::create(['name' => 'read users']);
        Permission::create(['name' => 'update users']);
        Permission::create(['name' => 'delete users']);

        // Patients
        Permission::create(['name' => 'create patients']);
        Permission::create(['name' => 'read patients']);
        Permission::create(['name' => 'update patients']);
        Permission::create(['name' => 'delete patients']);

        // Appointments
        Permission::create(['name' => 'create appointments']);
        Permission::create(['name' => 'read appointments']);
        Permission::create(['name' => 'update appointments']);
        Permission::create(['name' => 'delete appointments']);

        // Services
        Permission::create(['name' => 'create services']);
        Permission::create(['name' => 'read services']);
        Permission::create(['name' => 'update services']);
        Permission::create(['name' => 'delete services']);

        // Doctors
        Permission::create(['name' => 'create doctors']);
        Permission::create(['name' => 'read doctors']);
        Permission::create(['name' => 'update doctors']);
        Permission::create(['name' => 'delete doctors']);

        // Payments
        Permission::create(['name' => 'create payments']);
        Permission::create(['name' => 'read payments']);
        Permission::create(['name' => 'update payments']);
        Permission::create(['name' => 'delete payments']);

        // Prescriptions
        Permission::create(['name' => 'create prescriptions']);
        Permission::create(['name' => 'read prescriptions']);
        Permission::create(['name' => 'update prescriptions']);
        Permission::create(['name' => 'delete prescriptions']);

        // Lab Tests
        Permission::create(['name' => 'create lab tests']);
        Permission::create(['name' => 'read lab tests']);
        Permission::create(['name' => 'update lab tests']);
        Permission::create(['name' => 'delete lab tests']);

        // Lab Results
        Permission::create(['name' => 'create lab results']);
        Permission::create(['name' => 'read lab results']);
        Permission::create(['name' => 'update lab results']);
        Permission::create(['name' => 'delete lab results']);

        // Medications
        Permission::create(['name' => 'create medications']);
        Permission::create(['name' => 'read medications']);
        Permission::create(['name' => 'update medications']);
        Permission::create(['name' => 'delete medications']);

        // Medication Inventory
        Permission::create(['name' => 'create medication inventory']);
        Permission::create(['name' => 'read medication inventory']);
        Permission::create(['name' => 'update medication inventory']);
        Permission::create(['name' => 'delete medication inventory']);

        // Vital Signs
        Permission::create(['name' => 'create vital signs']);
        Permission::create(['name' => 'read vital signs']);
        Permission::create(['name' => 'update vital signs']);
        Permission::create(['name' => 'delete vital signs']);

        // Reports
        Permission::create(['name' => 'generate reports']);
        Permission::create(['name' => 'view reports']);

        // Assign permissions to roles
        $adminRole->givePermissionTo(Permission::all());

        $receptionistRole->givePermissionTo([
            'create patients', 'read patients', 'update patients',
            'create appointments', 'read appointments', 'update appointments',
            'read doctors', 'read services',
            'create payments', 'read payments',
        ]);

        $doctorRole->givePermissionTo([
            'read patients', 'update patients',
            'read appointments', 'update appointments',
            'create prescriptions', 'read prescriptions', 'update prescriptions',
            'create lab tests', 'read lab tests',
            'read lab results', 'create lab results',
            'read medications',
            'create vital signs', 'read vital signs', 'update vital signs',
        ]);

        $nurseRole->givePermissionTo([
            'read patients',
            'read appointments',
            'create vital signs', 'read vital signs', 'update vital signs',
            'read prescriptions',
        ]);

        $pharmacistRole->givePermissionTo([
            'read patients',
            'read prescriptions', 'update prescriptions',
            'read medications', 'update medications',
            'create medication inventory', 'read medication inventory', 'update medication inventory',
        ]);

        $labTechnicianRole->givePermissionTo([
            'read patients',
            'read lab tests',
            'create lab results', 'read lab results', 'update lab results',
        ]);

        $accountantRole->givePermissionTo([
            'read patients',
            'read appointments',
            'read payments', 'update payments',
            'generate reports', 'view reports',
        ]);

        $patientRole->givePermissionTo([
            'read appointments',
            'read prescriptions',
            'read lab results',
            'read vital signs',
            'read payments',
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $admin->assignRole('admin');

        // Create a receptionist user
        $receptionist = User::create([
            'name' => 'Receptionist User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $receptionist->assignRole('receptionist');

        // Note: Les utilisateurs médecins sont créés par le DoctorSeeder
        // qui crée à la fois l'utilisateur et son profil médecin associé

        // Create a nurse user
        $nurse = User::create([
            'name' => 'Nurse User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $nurse->assignRole('nurse');

        // Create a pharmacist user
        $pharmacist = User::create([
            'name' => 'Pharmacist User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $pharmacist->assignRole('pharmacist');

        // Create a lab technician user
        $labTechnician = User::create([
            'name' => 'Lab Technician User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $labTechnician->assignRole('lab_technician');

        // Create an accountant user
        $accountant = User::create([
            'name' => 'Accountant User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $accountant->assignRole('accountant');
        
        // Appeler les autres seeders
        $this->call([
            ServiceSeeder::class,
            DoctorSeeder::class,
            LabTestSeeder::class,
            UpdateInsuranceCoverageSeeder::class,
            MedicationInsuranceCoverageSeeder::class,
        ]);
    }
}