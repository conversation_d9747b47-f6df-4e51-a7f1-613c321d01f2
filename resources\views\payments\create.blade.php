@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="container mx-auto p-6">
        <!-- Header avec breadcrumb -->
        <div class="mb-8">
            <div class="breadcrumbs text-sm mb-4">
                <ul>
                    <li><a href="{{ route('receptionist.dashboard') }}" class="text-primary hover:text-primary-focus">🏠 Dashboard</a></li>
                    <li><a href="{{ route('payments.index') }}" class="text-primary hover:text-primary-focus">💰 Paiements</a></li>
                    <li class="text-base-content/70">💳 Nouveau Paiement</li>
                </ul>
            </div>

            <!-- Hero Section -->
            <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content shadow-2xl">
                <div class="card-body text-center py-8">
                    <h1 class="text-4xl font-bold mb-4 text-white">
                        💳 Paiement Consultation
                    </h1>
                    <div class="badge badge-accent badge-lg mb-4">
                        🏥 GlobalCare Solutions
                    </div>
                    <p class="text-white/90 text-lg">
                        Enregistrement du paiement pour une consultation médicale
                    </p>
                </div>
            </div>
        </div>

        <!-- Layout principal -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            <!-- Sidebar Informations -->
            <div class="xl:col-span-1 space-y-6">
                @if($appointment)
                    <!-- Carte Rendez-vous (Pré-sélectionné) -->
                    <div class="card bg-base-100 shadow-2xl border border-primary/20">
                        <div class="card-body">
                            <h3 class="card-title text-primary mb-4">
                                📅 Rendez-vous Sélectionné
                            </h3>

                            <!-- Patient -->
                            <div class="flex items-center space-x-4 mb-6">
                                <div class="avatar placeholder">
                                    <div class="bg-gradient-to-br from-primary to-secondary text-primary-content rounded-full w-16 shadow-lg">
                                        <span class="text-xl font-bold">{{ substr($appointment->patient->first_name, 0, 1) }}{{ substr($appointment->patient->last_name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="font-bold text-lg">{{ $appointment->patient->first_name }} {{ $appointment->patient->last_name }}</div>
                                    <div class="text-sm opacity-70">{{ $appointment->patient->patient_number }}</div>
                                </div>
                            </div>

                            <!-- Détails du RDV -->
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-info/10 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-info/20 rounded-full flex items-center justify-center">
                                            📅
                                        </div>
                                        <div>
                                            <div class="text-sm opacity-70">Date & Heure</div>
                                            <div class="font-semibold">{{ $appointment->appointment_datetime->format('d/m/Y à H:i') }}</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-secondary/10 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-secondary/20 rounded-full flex items-center justify-center">
                                            🏥
                                        </div>
                                        <div>
                                            <div class="text-sm opacity-70">Service</div>
                                            <div class="font-semibold">{{ $appointment->service->name }}</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-accent/10 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center">
                                            👨‍⚕️
                                        </div>
                                        <div>
                                            <div class="text-sm opacity-70">Médecin</div>
                                            <div class="font-semibold">Dr. {{ $appointment->doctor->user->first_name }} {{ $appointment->doctor->user->last_name }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Carte Résumé -->
                <div class="card bg-base-100 shadow-2xl border-2 border-primary">
                    <div class="card-body">
                        <h4 class="card-title justify-center text-primary mb-6">
                            💰 Résumé du Paiement
                        </h4>

                        <div class="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl p-6 text-center border border-primary/20">
                            <div class="text-4xl mb-3">💳</div>
                            <div class="text-base font-bold text-gray-700 mb-2">Montant à payer</div>
                            <div class="text-4xl font-bold text-primary" id="amount-display">
                                @if($appointment)
                                    {{ number_format($appointment->service->price, 0, ',', ' ') }}
                                @else
                                    0
                                @endif
                            </div>
                            <div class="text-xl font-bold text-gray-800">FCFA</div>
                        </div>

                        <div class="flex justify-center mt-6">
                            <div class="badge badge-success badge-lg text-white">
                                ✅ Prêt pour paiement
                            </div>
                        </div>

                        <!-- Informations supplémentaires -->
                        @if($appointment)
                        <div class="mt-4 p-4 bg-info/10 rounded-lg border border-info/30">
                            <div class="text-sm font-bold text-gray-700 mb-1">Service sélectionné</div>
                            <div class="font-bold text-gray-900">{{ $appointment->service->name }}</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Section principale -->
            <div class="xl:col-span-2">
                <div class="card bg-base-100 shadow-2xl border border-secondary/20">
                    <div class="card-body">
                        <h3 class="card-title text-secondary text-xl mb-6">
                            💳 Formulaire de Paiement
                        </h3>

                        <form action="{{ route('payments.store') }}" method="POST" class="space-y-8">
                            @csrf

                            @if($appointment)
                                <input type="hidden" name="appointment_id" value="{{ $appointment->id }}">
                            @else
                                <!-- Sélection du rendez-vous -->
                                <div class="bg-base-200 rounded-xl p-6 space-y-6">
                                    <div class="alert alert-info">
                                        <svg class="w-6 h-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h3 class="font-bold">Sélection du rendez-vous</h3>
                                            <div class="text-xs">Choisissez d'abord le patient, puis son rendez-vous à payer</div>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Patient -->
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text text-lg font-semibold">
                                                    👤 Patient <span class="text-error">*</span>
                                                </span>
                                            </label>
                                            <select name="patient_select" id="patient_select" class="select select-bordered select-primary">
                                                <option value="">Sélectionner un patient...</option>
                                                @foreach($patients as $patient)
                                                    <option value="{{ $patient->id }}">
                                                        {{ $patient->first_name }} {{ $patient->last_name }} - {{ $patient->patient_number }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <!-- Rendez-vous -->
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text text-lg font-semibold">
                                                    📅 Rendez-vous <span class="text-error">*</span>
                                                </span>
                                            </label>
                                            <select name="appointment_id" id="appointment_id" required
                                                class="select select-bordered select-primary @error('appointment_id') select-error @enderror">
                                                <option value="">Sélectionner d'abord un patient...</option>
                                            </select>
                                            @error('appointment_id')
                                                <label class="label">
                                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                                </label>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Détails du paiement -->
                            <div class="bg-base-200 rounded-xl p-6 space-y-6">
                                <div class="flex items-center space-x-3 mb-4">
                                    <div class="w-8 h-8 bg-secondary/20 rounded-full flex items-center justify-center">
                                        💰
                                    </div>
                                    <h4 class="text-xl font-bold text-secondary">Détails du Paiement</h4>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Montant -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text text-lg font-semibold">
                                                💵 Montant <span class="text-error">*</span>
                                            </span>
                                        </label>
                                        <label class="input input-bordered input-primary flex items-center gap-2">
                                            <span class="text-sm font-bold text-gray-700">FCFA</span>
                                            <input type="number" name="amount" id="amount" min="0" step="100"
                                                   value="{{ old('amount', $appointment ? $appointment->service->price : '') }}"
                                                   required class="grow @error('amount') input-error @enderror"
                                                   placeholder="Ex: 25000">
                                        </label>
                                        @error('amount')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- Méthode de paiement -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text text-lg font-semibold">
                                                💳 Méthode de paiement <span class="text-error">*</span>
                                            </span>
                                        </label>
                                        <select name="payment_method" id="payment_method" required
                                            class="select select-bordered select-primary @error('payment_method') select-error @enderror">
                                            <option value="">Sélectionner une méthode...</option>
                                            <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>💵 Espèces</option>
                                            <option value="mobile_money" {{ old('payment_method') == 'mobile_money' ? 'selected' : '' }}>📱 Mobile Money</option>
                                            <option value="card" {{ old('payment_method') == 'card' ? 'selected' : '' }}>💳 Carte bancaire</option>
                                            <option value="insurance" {{ old('payment_method') == 'insurance' ? 'selected' : '' }}>🏥 Assurance</option>
                                            <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>📋 Autre</option>
                                        </select>
                                        @error('payment_method')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Informations complémentaires -->
                            <div class="bg-base-200 rounded-xl p-6 space-y-6">
                                <div class="flex items-center space-x-3 mb-4">
                                    <div class="w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center">
                                        📝
                                    </div>
                                    <h4 class="text-xl font-bold text-accent">Informations Complémentaires</h4>
                                </div>

                                <!-- Référence transaction (conditionnel) -->
                                <div id="reference_field" class="form-control hidden">
                                    <label class="label">
                                        <span class="label-text font-semibold">
                                            🔗 Référence de transaction
                                        </span>
                                    </label>
                                    <input type="text" name="transaction_reference" id="transaction_reference"
                                           value="{{ old('transaction_reference') }}"
                                           placeholder="Ex: OM123456789 ou CARD123456"
                                           class="input input-bordered input-accent @error('transaction_reference') input-error @enderror">
                                    <label class="label">
                                        <span class="label-text-alt font-semibold text-gray-600">Pour Mobile Money ou carte bancaire</span>
                                    </label>
                                    @error('transaction_reference')
                                        <label class="label">
                                            <span class="label-text-alt text-error">{{ $message }}</span>
                                        </label>
                                    @enderror
                                </div>

                                <!-- Notes -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-semibold">
                                            📝 Notes complémentaires
                                        </span>
                                    </label>
                                    <textarea name="notes" id="notes" rows="3"
                                              placeholder="Remarques particulières sur ce paiement..."
                                              class="textarea textarea-bordered textarea-accent">{{ old('notes') }}</textarea>
                                </div>
                            </div>

                            <!-- Récapitulatif final -->
                            <div class="alert alert-info">
                                <svg class="w-6 h-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h3 class="font-bold">Récapitulatif</h3>
                                    <div class="text-sm" id="payment-summary">
                                        @if($appointment)
                                            Patient: <strong>{{ $appointment->patient->first_name }} {{ $appointment->patient->last_name }}</strong><br>
                                            Service: <strong>{{ $appointment->service->name }}</strong> •
                                            Montant: <strong>{{ number_format($appointment->service->price, 0, ',', ' ') }} FCFA</strong>
                                        @else
                                            Veuillez sélectionner un rendez-vous pour voir le récapitulatif
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex flex-col sm:flex-row gap-4 justify-between pt-6">
                                <a href="{{ route('payments.index') }}" class="btn btn-ghost btn-lg">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                                    </svg>
                                    Annuler
                                </a>

                                <button type="submit" class="btn btn-primary btn-lg btn-wide">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                    💰 Enregistrer le Paiement
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Gestion des méthodes de paiement
    const paymentMethodSelect = document.getElementById('payment_method');
    const referenceField = document.getElementById('reference_field');
    const amountInput = document.getElementById('amount');
    const amountDisplay = document.getElementById('amount-display');

    // Affichage conditionnel du champ référence
    if (paymentMethodSelect) {
        paymentMethodSelect.addEventListener('change', function() {
            if (this.value === 'mobile_money' || this.value === 'card' || this.value === 'insurance') {
                referenceField.classList.remove('hidden');

                // Mise à jour du placeholder
                const referenceInput = document.getElementById('transaction_reference');
                if (this.value === 'mobile_money') {
                    referenceInput.placeholder = 'Ex: OM123456789 ou WAVE123456';
                } else if (this.value === 'card') {
                    referenceInput.placeholder = 'Ex: CARD123456789';
                } else if (this.value === 'insurance') {
                    referenceInput.placeholder = 'Ex: ASSUR123456789';
                }
            } else {
                referenceField.classList.add('hidden');
            }
        });

        // Trigger change event on load
        if (paymentMethodSelect.value) {
            paymentMethodSelect.dispatchEvent(new Event('change'));
        }
    }

    // Mise à jour du montant affiché
    if (amountInput && amountDisplay) {
        amountInput.addEventListener('input', function() {
            const value = parseInt(this.value) || 0;
            amountDisplay.textContent = value.toLocaleString('fr-FR');
        });
    }

    // Gestion de la sélection de patient/rendez-vous
    const patientSelect = document.getElementById('patient_select');
    const appointmentSelect = document.getElementById('appointment_id');
    const paymentSummary = document.getElementById('payment-summary');

    if (patientSelect && appointmentSelect) {
        patientSelect.addEventListener('change', function() {
            if (this.value) {
                // Fetch appointments for selected patient
                fetch(`/api/patients/${this.value}/appointments`)
                    .then(response => response.json())
                    .then(data => {
                        // Clear current options
                        appointmentSelect.innerHTML = '';

                        // Add default option
                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.textContent = 'Sélectionner un rendez-vous...';
                        appointmentSelect.appendChild(defaultOption);

                        // Add appointment options
                        data.forEach(appointment => {
                            if (!appointment.payment) {
                                const option = document.createElement('option');
                                option.value = appointment.id;

                                const date = new Date(appointment.appointment_datetime);
                                const formattedDate = date.toLocaleDateString('fr-FR');
                                const formattedTime = date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });

                                option.textContent = `${formattedDate} ${formattedTime} - ${appointment.service.name}`;
                                option.dataset.amount = appointment.service.price;
                                option.dataset.patientName = `${appointment.patient.first_name} ${appointment.patient.last_name}`;
                                option.dataset.serviceName = appointment.service.name;
                                appointmentSelect.appendChild(option);
                            }
                        });
                    })
                    .catch(error => console.error('Error fetching appointments:', error));
            } else {
                // Reset appointment select
                appointmentSelect.innerHTML = '<option value="">Sélectionner d\'abord un patient...</option>';
                if (paymentSummary) {
                    paymentSummary.innerHTML = 'Veuillez sélectionner un rendez-vous pour voir le récapitulatif';
                }
            }
        });

        // Update amount and summary when appointment is selected
        appointmentSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];

            if (selectedOption && selectedOption.dataset.amount) {
                // Update amount
                amountInput.value = selectedOption.dataset.amount;
                if (amountDisplay) {
                    amountDisplay.textContent = parseInt(selectedOption.dataset.amount).toLocaleString('fr-FR');
                }

                // Update summary
                if (paymentSummary) {
                    paymentSummary.innerHTML = `
                        Patient: <strong>${selectedOption.dataset.patientName}</strong><br>
                        Service: <strong>${selectedOption.dataset.serviceName}</strong> •
                        Montant: <strong>${parseInt(selectedOption.dataset.amount).toLocaleString('fr-FR')} FCFA</strong>
                    `;
                }
            } else {
                if (paymentSummary) {
                    paymentSummary.innerHTML = 'Veuillez sélectionner un rendez-vous pour voir le récapitulatif';
                }
            }
        });
    }

    // Validation du formulaire avec modal de confirmation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const amount = amountInput.value;
            const paymentMethod = paymentMethodSelect.value;

            if (!amount || amount <= 0) {
                alert('⚠️ Veuillez saisir un montant valide.');
                amountInput.focus();
                return false;
            }

            if (!paymentMethod) {
                alert('⚠️ Veuillez sélectionner une méthode de paiement.');
                paymentMethodSelect.focus();
                return false;
            }

            // Modal de confirmation
            const modal = document.createElement('div');
            modal.className = 'modal modal-open';
            modal.innerHTML = `
                <div class="modal-box">
                    <h3 class="font-bold text-lg">💰 Confirmer le Paiement</h3>
                    <div class="py-4">
                        <div class="stats shadow w-full">
                            <div class="stat">
                                <div class="stat-title">Montant</div>
                                <div class="stat-value text-primary">${parseInt(amount).toLocaleString('fr-FR')} FCFA</div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <strong>Méthode:</strong> ${paymentMethodSelect.options[paymentMethodSelect.selectedIndex].text}
                        </div>
                    </div>
                    <div class="modal-action">
                        <button type="button" class="btn btn-ghost" onclick="this.closest('.modal').remove()">
                            Annuler
                        </button>
                        <button type="button" class="btn btn-primary" onclick="document.querySelector('form').submit()">
                            ✅ Confirmer
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        });
    }
});
</script>

<style>
    /* Animations personnalisées */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
</style>
@endpush
@endsection 