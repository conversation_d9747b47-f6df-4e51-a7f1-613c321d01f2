@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Tableau de bord Administrateur
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Bienvenue {{ auth()->user()->name }}. Vue d'ensemble de la clinique.
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
                <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                    </svg>
                    Nouvel utilisateur
                </a>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <!-- Total Patients -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-figure text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="stat-title">Total Patients</div>
                    <div class="stat-value text-primary">{{ number_format($stats['total_patients']) }}</div>
                </div>
            </div>

            <!-- Total Users -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-figure text-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                    </div>
                    <div class="stat-title">Personnel Total</div>
                    <div class="stat-value text-secondary">{{ number_format($stats['total_users']) }}</div>
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-figure text-success">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <div class="stat-title">Revenus du mois</div>
                    <div class="stat-value text-success">{{ number_format($stats['total_revenue_this_month']) }} FCFA</div>
                </div>
            </div>

            <!-- Today's Appointments -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-figure text-info">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="stat-title">RDV Aujourd'hui</div>
                    <div class="stat-value text-info">{{ number_format($stats['total_appointments_today']) }}</div>
                </div>
            </div>
        </div>

        <!-- Additional Stats Row -->
        <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <!-- Total Doctors -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Médecins</div>
                    <div class="stat-value">{{ number_format($stats['total_doctors']) }}</div>
                </div>
            </div>

            <!-- Today's Revenue -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Revenus Aujourd'hui</div>
                    <div class="stat-value">{{ number_format($stats['total_revenue_today']) }} FCFA</div>
                </div>
            </div>

            <!-- Pending Lab Results -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Résultats Lab En Attente</div>
                    <div class="stat-value">{{ number_format($stats['pending_lab_results']) }}</div>
                </div>
            </div>

            <!-- Low Stock Medications -->
            <div class="stats shadow">
                <div class="stat">
                    <div class="stat-title">Stock Bas</div>
                    <div class="stat-value text-warning">{{ number_format($stats['low_stock_medications']) }}</div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="mt-8 grid grid-cols-1 gap-5 lg:grid-cols-2">
            <!-- Users by Role Chart -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title">Répartition du Personnel</h3>
                    <div class="h-64 flex items-center justify-center">
                        @if($usersByRole->count() > 0)
                            <div class="w-full">
                                @foreach($usersByRole as $role => $count)
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="capitalize">{{ ucfirst($role) }}</span>
                                        <span class="badge badge-primary">{{ $count }}</span>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center">
                                <p class="text-gray-500">Aucune donnée disponible</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Appointments by Status -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title">Statut des Rendez-vous</h3>
                    <div class="h-64 flex items-center justify-center">
                        @if($appointmentsByStatus->count() > 0)
                            <div class="w-full">
                                @foreach($appointmentsByStatus as $status => $count)
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="capitalize">{{ ucfirst($status) }}</span>
                                        <span class="badge badge-secondary">{{ $count }}</span>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center">
                                <p class="text-gray-500">Aucune donnée disponible</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Management Actions -->
        <div class="mt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Gestion</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <!-- Gestion Utilisateurs -->
                <a href="{{ route('admin.users') }}" class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
                    <div class="card-body">
                        <div class="flex items-center">
                            <div class="avatar placeholder mr-4">
                                <div class="bg-primary text-primary-content rounded-full w-12">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="card-title text-lg">Gestion Utilisateurs</h3>
                                <p class="text-sm opacity-70">Gérer le personnel et les rôles</p>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Services Médicaux -->
                <a href="{{ route('admin.services') }}" class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
                    <div class="card-body">
                        <div class="flex items-center">
                            <div class="avatar placeholder mr-4">
                                <div class="bg-success text-success-content rounded-full w-12">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="card-title text-lg">Services Médicaux</h3>
                                <p class="text-sm opacity-70">Configurer les services et tarifs</p>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Médicaments -->
                <a href="{{ route('admin.medications.index') }}" class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
                    <div class="card-body">
                        <div class="flex items-center">
                            <div class="avatar placeholder mr-4">
                                <div class="bg-warning text-warning-content rounded-full w-12">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="card-title text-lg">Médicaments</h3>
                                <p class="text-sm opacity-70">Gérer les médicaments et stock</p>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Inventaire -->
                <a href="{{ route('admin.medications.inventory') }}" class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
                    <div class="card-body">
                        <div class="flex items-center">
                            <div class="avatar placeholder mr-4">
                                <div class="bg-info text-info-content rounded-full w-12">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="card-title text-lg">Inventaire</h3>
                                <p class="text-sm opacity-70">Gérer les stocks de médicaments</p>
                            </div>
                        </div>
                    </div>
                </a>
                
                <!-- Tests de Laboratoire -->
                <a href="{{ route('admin.lab-tests') }}" class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
                    <div class="card-body">
                        <div class="flex items-center">
                            <div class="avatar placeholder mr-4">
                                <div class="bg-accent text-accent-content rounded-full w-12">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="card-title text-lg">Tests de Laboratoire</h3>
                                <p class="text-sm opacity-70">Gérer les tests disponibles</p>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
