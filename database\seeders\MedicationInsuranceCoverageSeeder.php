<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Medication;
use App\Models\MedicationInsuranceCoverage;

class MedicationInsuranceCoverageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer quelques médicaments pour les exemples
        $medications = Medication::take(10)->get();

        if ($medications->isEmpty()) {
            $this->command->warn('Aucun médicament trouvé. Veuillez d\'abord exécuter le seeder des médicaments.');
            return;
        }

        // Types d'assurance
        $insuranceTypes = ['AMO', 'CNOPS', 'CNSS', 'RAMED', 'Privée', 'Mutuelle'];

        // Exemples de couvertures pour différents types de médicaments
        $coverageExamples = [
            // Médicaments essentiels - couverture élevée
            'essential' => [
                'AMO' => 80.00,
                'CNOPS' => 85.00,
                'CNSS' => 80.00,
                'RAMED' => 95.00,
                'Privée' => 70.00,
                'Mutuelle' => 60.00,
            ],
            // Médicaments génériques - couverture standard
            'generic' => [
                'AMO' => 70.00,
                'CNOPS' => 75.00,
                'CNSS' => 70.00,
                'RAMED' => 90.00,
                'Privée' => 60.00,
                'Mutuelle' => 50.00,
            ],
            // Médicaments de marque - couverture réduite
            'brand' => [
                'AMO' => 50.00,
                'CNOPS' => 60.00,
                'CNSS' => 50.00,
                'RAMED' => 70.00,
                'Privée' => 40.00,
                'Mutuelle' => 30.00,
            ],
        ];

        foreach ($medications as $index => $medication) {
            // Déterminer le type de couverture basé sur l'index
            $coverageType = match($index % 3) {
                0 => 'essential',
                1 => 'generic',
                2 => 'brand',
            };

            $coverages = $coverageExamples[$coverageType];

            foreach ($coverages as $insuranceType => $percentage) {
                // Ajouter quelques variations pour rendre les données plus réalistes
                $conditions = null;
                $minimumAmount = null;
                $maximumCoverageAmount = null;

                // Conditions spéciales pour certains types
                if ($insuranceType === 'RAMED') {
                    $conditions = 'Réservé aux bénéficiaires du RAMED avec carte valide';
                }

                if ($insuranceType === 'Privée') {
                    $minimumAmount = 50.00; // Montant minimum pour bénéficier de la couverture
                    $maximumCoverageAmount = 500.00; // Plafond de remboursement
                }

                MedicationInsuranceCoverage::updateOrCreate(
                    [
                        'medication_id' => $medication->id,
                        'insurance_type' => $insuranceType,
                    ],
                    [
                        'coverage_percentage' => $percentage,
                        'minimum_amount' => $minimumAmount,
                        'maximum_coverage_amount' => $maximumCoverageAmount,
                        'valid_from' => now()->subMonths(6),
                        'valid_until' => now()->addYears(2),
                        'is_active' => true,
                        'conditions' => $conditions,
                        'notes' => "Couverture automatique pour {$medication->name}",
                        'created_by' => 1, // Admin user
                        'updated_by' => 1,
                    ]
                );
            }
        }

        // Ajouter quelques exemples spéciaux
        $this->addSpecialCoverageExamples();

        $this->command->info('Couvertures d\'assurance des médicaments créées avec succès.');
    }

    /**
     * Ajouter des exemples de couvertures spéciales
     */
    private function addSpecialCoverageExamples(): void
    {
        $medications = Medication::take(3)->get();

        foreach ($medications as $medication) {
            // Exemple : Médicament non couvert par certaines assurances
            MedicationInsuranceCoverage::updateOrCreate(
                [
                    'medication_id' => $medication->id,
                    'insurance_type' => 'Mutuelle',
                ],
                [
                    'coverage_percentage' => 0.00,
                    'is_active' => false,
                    'conditions' => 'Médicament non couvert par cette assurance',
                    'notes' => 'Exclusion spécifique',
                    'created_by' => 1,
                    'updated_by' => 1,
                ]
            );

            // Exemple : Couverture avec conditions spéciales
            MedicationInsuranceCoverage::updateOrCreate(
                [
                    'medication_id' => $medication->id,
                    'insurance_type' => 'CNOPS',
                ],
                [
                    'coverage_percentage' => 90.00,
                    'minimum_amount' => 100.00,
                    'maximum_coverage_amount' => 1000.00,
                    'valid_from' => now(),
                    'valid_until' => now()->addYear(),
                    'is_active' => true,
                    'conditions' => 'Prescription médicale obligatoire + accord préalable pour montants > 500 DH',
                    'notes' => 'Couverture spéciale avec conditions',
                    'created_by' => 1,
                    'updated_by' => 1,
                ]
            );
        }
    }
}
