<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('medication_inventories', function (Blueprint $table) {
            $table->decimal('selling_price', 10, 2)->default(0)->after('purchase_price');
            $table->integer('current_stock')->default(0)->after('selling_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('medication_inventories', function (Blueprint $table) {
            $table->dropColumn(['selling_price', 'current_stock']);
        });
    }
};