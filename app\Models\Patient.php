<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Patient extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'patient_number',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'phone_number',
        'email',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'medical_history',
        'allergies',
        'blood_group',
        'has_insurance',
        'insurance_type',
        'insurance_number',
        'insurance_expiry_date',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'has_insurance' => 'boolean',
        'insurance_coverage_percentage' => 'decimal:2',
        'insurance_expiry_date' => 'date',
    ];

    /**
     * Get the user associated with the patient.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the appointments for the patient.
     */
    public function appointments(): Has<PERSON>any
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get the prescriptions for the patient.
     */
    public function prescriptions(): HasMany
    {
        return $this->hasMany(Prescription::class);
    }

    /**
     * Get the payments for the patient.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the vital signs for the patient.
     */
    public function vitalSigns(): HasMany
    {
        return $this->hasMany(VitalSign::class);
    }

    /**
     * Get the lab results for the patient.
     */
    public function labResults(): HasMany
    {
        return $this->hasMany(LabResult::class);
    }

    /**
     * Get the full name of the patient.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get the age of the patient.
     */
    public function getAge(): int
    {
        if (!$this->date_of_birth) {
            return 0;
        }
        
        return $this->date_of_birth->age;
    }

    public function getInitialsAttribute(): string
    {
        return strtoupper($this->first_name[0] . $this->last_name[0]);
    }
}