<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Doctor;
use App\Models\Service;
use App\Models\Patient;
use App\Models\Medication;
use App\Models\MedicationInventory;
use App\Models\Appointment;
use App\Models\Payment;
use App\Models\LabResult;
use App\Models\LabTest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function __construct()
    {
        // Les middlewares sont maintenant gérés dans les routes
    }

    /**
     * Dashboard admin avec statistiques avancées
     */
    public function dashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'total_patients' => Patient::count(),
            'total_doctors' => Doctor::count(),
            'total_appointments_today' => Appointment::whereDate('appointment_date', today())->count(),
            'total_appointments_this_month' => Appointment::whereMonth('appointment_date', now()->month)->count(),
            'total_revenue_today' => Payment::whereDate('created_at', today())->sum('amount'),
            'total_revenue_this_month' => Payment::whereMonth('created_at', now()->month)->sum('amount'),
            'pending_lab_results' => LabResult::where('status', 'ordered')->count(),
            'low_stock_medications' => MedicationInventory::whereRaw('quantity <= 10')->count(),
        ];

        // Statistiques par rôle
        $usersByRole = User::join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->selectRaw('roles.name as role, COUNT(*) as count')
            ->groupBy('roles.name')
            ->pluck('count', 'role');

        // Rendez-vous par statut
        $appointmentsByStatus = Appointment::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');

        // Revenus des 7 derniers jours
        $revenueChart = Payment::selectRaw('DATE(created_at) as date, SUM(amount) as total')
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.dashboard', compact('stats', 'usersByRole', 'appointmentsByStatus', 'revenueChart'));
    }

    /**
     * Gestion des utilisateurs - Index
     */
    public function users()
    {
        $users = User::with('roles', 'doctor.service')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $roles = Role::all();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Créer un nouvel utilisateur
     */
    public function createUser()
    {
        $roles = Role::all();
        $services = Service::where('is_active', true)->get();

        return view('admin.users.create', compact('roles', 'services'));
    }

    /**
     * Stocker un nouvel utilisateur
     */
    public function storeUser(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'role' => 'required|exists:roles,name',
            // Champs spécifiques aux médecins
            'service_id' => 'nullable|exists:services,id',
            'specialization' => 'nullable|string|max:255',
            'qualification' => 'nullable|string|max:255',
            'license_number' => 'nullable|string|max:100',
            'biography' => 'nullable|string',
            'working_hours_start' => 'nullable|date_format:H:i',
            'working_hours_end' => 'nullable|date_format:H:i',
            'working_days' => 'nullable|array',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'phone_number' => $validated['phone_number'] ?? null,
            'address' => $validated['address'] ?? null,
        ]);

        $user->assignRole($validated['role']);

        // Si c'est un médecin, créer le profil médecin
        if ($validated['role'] === 'doctor') {
            Doctor::create([
                'user_id' => $user->id,
                'service_id' => $validated['service_id'],
                'specialization' => $validated['specialization'],
                'qualification' => $validated['qualification'],
                'license_number' => $validated['license_number'],
                'biography' => $validated['biography'],
                'is_active' => true,
                'working_hours_start' => $validated['working_hours_start'] ? Carbon::createFromFormat('H:i', $validated['working_hours_start']) : null,
                'working_hours_end' => $validated['working_hours_end'] ? Carbon::createFromFormat('H:i', $validated['working_hours_end']) : null,
                'working_days' => $validated['working_days'] ?? [],
            ]);
        }

        return redirect()->route('admin.users')->with('success', 'Utilisateur créé avec succès.');
    }

    /**
     * Modifier un utilisateur
     */
    public function editUser(User $user)
    {
        $roles = Role::all();
        $services = Service::where('is_active', true)->get();

        return view('admin.users.edit', compact('user', 'roles', 'services'));
    }

    /**
     * Mettre à jour un utilisateur
     */
    public function updateUser(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'role' => 'required|exists:roles,name',
            // Champs spécifiques aux médecins
            'service_id' => 'nullable|exists:services,id',
            'specialization' => 'nullable|string|max:255',
            'qualification' => 'nullable|string|max:255',
            'license_number' => 'nullable|string|max:100',
            'biography' => 'nullable|string',
            'working_hours_start' => 'nullable|date_format:H:i',
            'working_hours_end' => 'nullable|date_format:H:i',
            'working_days' => 'nullable|array',
            'is_active' => 'nullable|boolean',
        ]);

        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone_number' => $validated['phone_number'] ?? null,
            'address' => $validated['address'] ?? null,
        ];

        if (!empty($validated['password'])) {
            $updateData['password'] = Hash::make($validated['password']);
        }

        $user->update($updateData);

        // Mettre à jour le rôle
        $user->syncRoles([$validated['role']]);

        // Gérer le profil médecin
        if ($validated['role'] === 'doctor') {
            $doctorData = [
                'service_id' => $validated['service_id'],
                'specialization' => $validated['specialization'],
                'qualification' => $validated['qualification'],
                'license_number' => $validated['license_number'],
                'biography' => $validated['biography'],
                'is_active' => $validated['is_active'] ?? true,
                'working_hours_start' => $validated['working_hours_start'] ? Carbon::createFromFormat('H:i', $validated['working_hours_start']) : null,
                'working_hours_end' => $validated['working_hours_end'] ? Carbon::createFromFormat('H:i', $validated['working_hours_end']) : null,
                'working_days' => $validated['working_days'] ?? [],
            ];

            if ($user->doctor) {
                $user->doctor->update($doctorData);
            } else {
                Doctor::create(array_merge($doctorData, ['user_id' => $user->id]));
            }
        } else {
            // Si ce n'est plus un médecin, supprimer le profil médecin
            if ($user->doctor) {
                $user->doctor->delete();
            }
        }

        return redirect()->route('admin.users')->with('success', 'Utilisateur mis à jour avec succès.');
    }

    /**
     * Supprimer un utilisateur
     */
    public function deleteUser(User $user)
    {
        // Vérifier qu'on ne supprime pas le dernier admin
        if ($user->hasRole('admin') && User::role('admin')->count() <= 1) {
            return redirect()->route('admin.users')->with('error', 'Impossible de supprimer le dernier administrateur.');
        }

        $user->delete();

        return redirect()->route('admin.users')->with('success', 'Utilisateur supprimé avec succès.');
    }

    /**
     * Gestion des services - Index
     */
    public function services()
    {
        $services = Service::withCount('doctors', 'appointments')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.services.index', compact('services'));
    }

    /**
     * Créer un nouveau service
     */
    public function createService()
    {
        return view('admin.services.create');
    }

    /**
     * Stocker un nouveau service
     */
    public function storeService(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'service_code' => 'required|string|max:50|unique:services',
            'is_active' => 'boolean',
        ]);

        Service::create($validated);

        return redirect()->route('admin.services')->with('success', 'Service créé avec succès.');
    }

    /**
     * Modifier un service
     */
    public function editService(Service $service)
    {
        return view('admin.services.edit', compact('service'));
    }

    /**
     * Mettre à jour un service
     */
    public function updateService(Request $request, Service $service)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'service_code' => ['required', 'string', 'max:50', Rule::unique('services')->ignore($service->id)],
            'is_active' => 'boolean',
        ]);

        $service->update($validated);

        return redirect()->route('admin.services')->with('success', 'Service mis à jour avec succès.');
    }

    /**
     * Supprimer un service
     */
    public function deleteService(Service $service)
    {
        // Vérifier s'il y a des médecins ou rendez-vous associés
        if ($service->doctors()->count() > 0 || $service->appointments()->count() > 0) {
            return redirect()->route('admin.services')->with('error', 'Impossible de supprimer un service avec des médecins ou rendez-vous associés.');
        }

        $service->delete();

        return redirect()->route('admin.services')->with('success', 'Service supprimé avec succès.');
    }

    /**
     * Gestion des médicaments - Index
     */
    public function medications()
    {
        $medications = Medication::with('inventories')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.medications.index', compact('medications'));
    }

    /**
     * Créer un nouveau médicament
     */
    public function createMedication()
    {
        return view('admin.medications.create');
    }

    /**
     * Stocker un nouveau médicament
     */
    public function storeMedication(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'generic_name' => 'nullable|string|max:255',
            'code' => 'required|string|max:50|unique:medications',
            'description' => 'nullable|string',
            'manufacturer' => 'nullable|string|max:255',
            'unit_price' => 'required|numeric|min:0',
            'dosage_form' => 'nullable|string|max:100',
            'strength' => 'nullable|string|max:100',
            'prescription_required' => 'boolean',
            'is_active' => 'boolean',
        ]);

        Medication::create($validated);

        return redirect()->route('admin.medications')->with('success', 'Médicament créé avec succès.');
    }

    /**
     * Modifier un médicament
     */
    public function editMedication(Medication $medication)
    {
        return view('admin.medications.edit', compact('medication'));
    }

    /**
     * Mettre à jour un médicament
     */
    public function updateMedication(Request $request, Medication $medication)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'generic_name' => 'nullable|string|max:255',
            'code' => ['required', 'string', 'max:50', Rule::unique('medications')->ignore($medication->id)],
            'description' => 'nullable|string',
            'manufacturer' => 'nullable|string|max:255',
            'unit_price' => 'required|numeric|min:0',
            'dosage_form' => 'nullable|string|max:100',
            'strength' => 'nullable|string|max:100',
            'prescription_required' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $medication->update($validated);

        return redirect()->route('admin.medications')->with('success', 'Médicament mis à jour avec succès.');
    }

    /**
     * Supprimer un médicament
     */
    public function deleteMedication(Medication $medication)
    {
        // Vérifier s'il y a des prescriptions associées
        if ($medication->prescriptionItems()->count() > 0) {
            return redirect()->route('admin.medications')->with('error', 'Impossible de supprimer un médicament avec des prescriptions associées.');
        }

        $medication->delete();

        return redirect()->route('admin.medications')->with('success', 'Médicament supprimé avec succès.');
    }

    /**
     * Gestion de l'inventaire des médicaments
     */
    public function medicationInventory()
    {
        $inventories = MedicationInventory::with('medication', 'addedBy')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $lowStockMedications = MedicationInventory::with('medication')
            ->whereRaw('quantity <= 10')
            ->get();

        return view('admin.medications.inventory', compact('inventories', 'lowStockMedications'));
    }

    /**
     * Ajouter du stock
     */
    public function addMedicationStock(Request $request)
    {
        $validated = $request->validate([
            'medication_id' => 'required|exists:medications,id',
            'quantity' => 'required|integer|min:1',
            'batch_number' => 'nullable|string|max:100',
            'expiry_date' => 'nullable|date|after:today',
            'purchase_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        MedicationInventory::create(array_merge($validated, [
            'current_stock' => $validated['quantity'], // Initialiser le stock actuel avec la quantité
            'added_by' => Auth::id(),
            'added_at' => now(),
        ]));

        return redirect()->route('admin.medications.inventory')->with('success', 'Stock ajouté avec succès.');
    }

    /**
     * Gestion des tests de laboratoire - Index
     */
    public function labTests()
    {
        $labTests = LabTest::orderBy('category')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.lab-tests.index', compact('labTests'));
    }

    /**
     * Créer un nouveau test de laboratoire
     */
    public function createLabTest()
    {
        $categories = ['Hématologie', 'Biochimie', 'Microbiologie', 'Immunologie', 'Autre'];
        return view('admin.lab-tests.create', compact('categories'));
    }

    /**
     * Stocker un nouveau test de laboratoire
     */
    public function storeLabTest(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:100',
            'test_code' => 'required|string|max:50|unique:lab_tests',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'normal_range' => 'nullable|string|max:255',
            'unit' => 'nullable|string|max:50',
            'sort_order' => 'nullable|integer',
            'is_active' => 'boolean',
        ]);

        LabTest::create($validated);

        return redirect()->route('admin.lab-tests')->with('success', 'Test de laboratoire créé avec succès.');
    }

    /**
     * Modifier un test de laboratoire
     */
    public function editLabTest(LabTest $labTest)
    {
        $categories = ['Hématologie', 'Biochimie', 'Microbiologie', 'Immunologie', 'Autre'];
        return view('admin.lab-tests.edit', compact('labTest', 'categories'));
    }

    /**
     * Mettre à jour un test de laboratoire
     */
    public function updateLabTest(Request $request, LabTest $labTest)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:100',
            'test_code' => ['required', 'string', 'max:50', Rule::unique('lab_tests')->ignore($labTest->id)],
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'normal_range' => 'nullable|string|max:255',
            'unit' => 'nullable|string|max:50',
            'sort_order' => 'nullable|integer',
            'is_active' => 'boolean',
        ]);

        $labTest->update($validated);

        return redirect()->route('admin.lab-tests')->with('success', 'Test de laboratoire mis à jour avec succès.');
    }

    /**
     * Supprimer un test de laboratoire
     */
    public function deleteLabTest(LabTest $labTest)
    {
        // Vérifier s'il y a des résultats associés
        if ($labTest->labResults()->count() > 0) {
            return redirect()->route('admin.lab-tests')->with('error', 'Impossible de supprimer un test avec des résultats associés.');
        }

        $labTest->delete();

        return redirect()->route('admin.lab-tests')->with('success', 'Test de laboratoire supprimé avec succès.');
    }
}
