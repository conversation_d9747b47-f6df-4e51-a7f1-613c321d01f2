@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center">
                <a href="{{ route('patients.index') }}" class="btn btn-circle btn-ghost">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
                <div class="ml-4">
                    <h2 class="text-2xl font-bold">Nouveau Patient</h2>
                    <p class="text-base-content/70">Enregistrer un nouveau patient dans le système</p>
                </div>
            </div>
        </div>

        <!-- Form -->
        <div class="card bg-base-100 shadow-xl">
            <form action="{{ route('patients.store') }}" method="POST">
                @csrf
                
                <div class="card-body space-y-8">
                    <!-- Personal Information -->
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="badge badge-primary p-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-bold">Informations personnelles</h3>
                                <p class="text-base-content/70">Données d'identité du patient</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- First Name -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Prénom *</span>
                                </div>
                                <input type="text" name="first_name" value="{{ old('first_name') }}" required
                                       placeholder="Entrez le prénom"
                                       class="input input-bordered @error('first_name') input-error @enderror" />
                                @error('first_name')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                                @enderror
                            </label>

                            <!-- Last Name -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Nom de famille *</span>
                                </div>
                                <input type="text" name="last_name" value="{{ old('last_name') }}" required
                                       placeholder="Entrez le nom de famille"
                                       class="input input-bordered @error('last_name') input-error @enderror" />
                                @error('last_name')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                                @enderror
                            </label>

                            <!-- Date of Birth -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Date de naissance *</span>
                                </div>
                                <input type="date" name="date_of_birth" value="{{ old('date_of_birth') }}" required
                                       class="input input-bordered @error('date_of_birth') input-error @enderror" />
                                @error('date_of_birth')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                                @enderror
                            </label>

                            <!-- Gender -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Genre *</span>
                                </div>
                                <select name="gender" required class="select select-bordered @error('gender') select-error @enderror">
                                    <option value="">Sélectionner le genre...</option>
                                    <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>Homme</option>
                                    <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>Femme</option>
                                    <option value="other" {{ old('gender') == 'other' ? 'selected' : '' }}>Autre</option>
                                </select>
                                @error('gender')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                                @enderror
                            </label>

                            <!-- Blood Group -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Groupe sanguin</span>
                                </div>
                                <select name="blood_group" class="select select-bordered">
                                    <option value="">Non spécifié</option>
                                    <option value="A+" {{ old('blood_group') == 'A+' ? 'selected' : '' }}>A+</option>
                                    <option value="A-" {{ old('blood_group') == 'A-' ? 'selected' : '' }}>A-</option>
                                    <option value="B+" {{ old('blood_group') == 'B+' ? 'selected' : '' }}>B+</option>
                                    <option value="B-" {{ old('blood_group') == 'B-' ? 'selected' : '' }}>B-</option>
                                    <option value="AB+" {{ old('blood_group') == 'AB+' ? 'selected' : '' }}>AB+</option>
                                    <option value="AB-" {{ old('blood_group') == 'AB-' ? 'selected' : '' }}>AB-</option>
                                    <option value="O+" {{ old('blood_group') == 'O+' ? 'selected' : '' }}>O+</option>
                                    <option value="O-" {{ old('blood_group') == 'O-' ? 'selected' : '' }}>O-</option>
                                </select>
                            </label>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="badge badge-success p-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-bold">Informations de contact</h3>
                                <p class="text-base-content/70">Coordonnées pour joindre le patient</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Phone Number -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Numéro de téléphone *</span>
                                </div>
                                <input type="tel" name="phone_number" value="{{ old('phone_number') }}" required
                                       placeholder="Ex: +223 70 12 34 56"
                                       class="input input-bordered @error('phone_number') input-error @enderror" />
                                @error('phone_number')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                                @enderror
                            </label>

                            <!-- Email -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Email</span>
                                </div>
                                <input type="email" name="email" value="{{ old('email') }}"
                                       placeholder="<EMAIL>"
                                       class="input input-bordered @error('email') input-error @enderror" />
                                @error('email')
                                <div class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </div>
                                @enderror
                            </label>

                            <!-- Address -->
                            <label class="form-control col-span-full">
                                <div class="label">
                                    <span class="label-text">Adresse</span>
                                </div>
                                <textarea name="address" class="textarea textarea-bordered h-24" 
                                          placeholder="Adresse complète du patient...">{{ old('address') }}</textarea>
                            </label>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="badge badge-error p-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-bold">Contact d'urgence</h3>
                                <p class="text-base-content/70">Personne à contacter en cas d'urgence</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Emergency Contact Name -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Nom du contact</span>
                                </div>
                                <input type="text" name="emergency_contact_name" value="{{ old('emergency_contact_name') }}"
                                       placeholder="Nom du contact d'urgence"
                                       class="input input-bordered" />
                            </label>

                            <!-- Emergency Contact Phone -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text">Téléphone du contact</span>
                                </div>
                                <input type="tel" name="emergency_contact_phone" value="{{ old('emergency_contact_phone') }}"
                                       placeholder="Numéro du contact d'urgence"
                                       class="input input-bordered" />
                            </label>
                        </div>
                    </div>

                    <!-- Medical Information -->
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="badge badge-info p-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-bold">Informations médicales</h3>
                                <p class="text-base-content/70">Antécédents et données médicales importantes</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6">
                            <!-- Medical History -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text font-medium">Antécédents médicaux</span>
                                    <span class="label-text-alt text-base-content/60">Optionnel</span>
                                </div>
                                <textarea name="medical_history" 
                                          class="textarea textarea-bordered resize-none focus:textarea-primary" 
                                          rows="4"
                                          placeholder="Maladies chroniques, opérations antérieures, traitements en cours, hospitalisations...">{{ old('medical_history') }}</textarea>
                                <div class="label">
                                    <span class="label-text-alt text-base-content/50">Décrivez les antécédents médicaux importants</span>
                                </div>
                            </label>

                            <!-- Allergies -->
                            <label class="form-control">
                                <div class="label">
                                    <span class="label-text font-medium">Allergies connues</span>
                                    <span class="label-text-alt text-base-content/60">Optionnel</span>
                                </div>
                                <textarea name="allergies" 
                                          class="textarea textarea-bordered resize-none focus:textarea-primary" 
                                          rows="3"
                                          placeholder="Allergies médicamenteuses, alimentaires, environnementales...">{{ old('allergies') }}</textarea>
                                <div class="label">
                                    <span class="label-text-alt text-base-content/50">Listez toutes les allergies connues</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Insurance Information -->
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="badge badge-success p-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-bold">Informations d'assurance</h3>
                                <p class="text-base-content/70">Couverture médicale et AMO</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Has Insurance -->
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-4">
                                    <input type="checkbox" 
                                           name="has_insurance" 
                                           value="1"
                                           class="checkbox checkbox-primary" 
                                           {{ old('has_insurance') ? 'checked' : '' }}
                                           onchange="toggleInsuranceFields(this.checked)">
                                    <span class="label-text font-medium">Patient couvert par une assurance</span>
                                </label>
                            </div>

                            <div></div>

                            <!-- Insurance Type -->
                            <label class="form-control insurance-field" style="{{ old('has_insurance') ? '' : 'display: none;' }}">
                                <div class="label">
                                    <span class="label-text font-medium">Type d'assurance</span>
                                    <span class="label-text-alt text-error">*</span>
                                </div>
                                <select name="insurance_type" class="select select-bordered focus:select-primary">
                                    <option value="">Sélectionner le type</option>
                                    <option value="AMO" {{ old('insurance_type') == 'AMO' ? 'selected' : '' }}>AMO (Assurance Maladie Obligatoire)</option>
                                    <option value="CNOPS" {{ old('insurance_type') == 'CNOPS' ? 'selected' : '' }}>CNOPS</option>
                                    <option value="CNSS" {{ old('insurance_type') == 'CNSS' ? 'selected' : '' }}>CNSS</option>
                                    <option value="RAMED" {{ old('insurance_type') == 'RAMED' ? 'selected' : '' }}>RAMED</option>
                                    <option value="Privée" {{ old('insurance_type') == 'Privée' ? 'selected' : '' }}>Assurance Privée</option>
                                </select>
                            </label>

                            <!-- Insurance Number -->
                            <label class="form-control insurance-field" style="{{ old('has_insurance') ? '' : 'display: none;' }}">
                                <div class="label">
                                    <span class="label-text font-medium">Numéro d'assurance</span>
                                    <span class="label-text-alt text-error">*</span>
                                </div>
                                <input type="text" 
                                       name="insurance_number" 
                                       value="{{ old('insurance_number') }}"
                                       class="input input-bordered focus:input-primary" 
                                       placeholder="Ex: 123456789">
                            </label>



                            <!-- Insurance Expiry Date -->
                            <label class="form-control insurance-field" style="{{ old('has_insurance') ? '' : 'display: none;' }}">
                                <div class="label">
                                    <span class="label-text font-medium">Date d'expiration</span>
                                    <span class="label-text-alt text-base-content/60">Optionnel</span>
                                </div>
                                <input type="date" 
                                       name="insurance_expiry_date" 
                                       value="{{ old('insurance_expiry_date') }}"
                                       class="input input-bordered focus:input-primary">
                                <div class="label">
                                    <span class="label-text-alt text-base-content/50">Date de fin de validité de l'assurance</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card-actions justify-end bg-base-200 px-6 py-4">
                    <a href="{{ route('patients.index') }}" class="btn btn-ghost">Annuler</a>
                    <button type="submit" class="btn btn-primary">Enregistrer Patient</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
