@extends('layouts.app')

@section('content')
<div class="container mx-auto p-6">
    <!-- Header avec breadcrumb -->
    <div class="mb-8">
        <div class="breadcrumbs text-sm">
            <ul>
                <li><a href="{{ route('receptionist.dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('payments.index') }}">Paiements</a></li>
                <li>Paiement Analyses</li>
            </ul>
        </div>
        
        <div class="flex items-center justify-between mt-4">
            <div>
                <h1 class="text-3xl font-bold">✅ Paiement Analyses Confirmé</h1>
                <div class="mt-2">
                    <div class="badge badge-success badge-lg">
                        {{ $labPayment->status_label }}
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('payments.lab.receipt', $labPayment) }}" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Télécharger Reçu
                </a>
                <a href="{{ route('receptionist.dashboard') }}" class="btn btn-ghost">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Retour Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Informations de paiement -->
        <div class="lg:col-span-2">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-primary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        Détails du Paiement
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <div class="space-y-4">
                            <div>
                                <div class="text-sm opacity-70">Numéro de reçu</div>
                                <div class="font-bold text-lg">{{ $labPayment->payment_number }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm opacity-70">Prescription N°</div>
                                <div class="font-semibold">{{ $labPayment->prescription_number }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm opacity-70">Méthode de paiement</div>
                                <div class="badge badge-outline">{{ $labPayment->payment_method_label }}</div>
                            </div>
                            
                            @if($labPayment->transaction_reference)
                            <div>
                                <div class="text-sm opacity-70">Référence transaction</div>
                                <div class="font-mono text-sm">{{ $labPayment->transaction_reference }}</div>
                            </div>
                            @endif
                        </div>
                        
                        <div class="space-y-4">
                            <div>
                                <div class="text-sm opacity-70">Date de paiement</div>
                                <div class="font-semibold">{{ $labPayment->payment_date->format('d/m/Y à H:i') }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm opacity-70">Reçu par</div>
                                <div class="font-semibold">{{ $labPayment->receiver->name }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm opacity-70">Montant total</div>
                                <div class="text-2xl font-bold text-success">{{ number_format($labPayment->total_amount, 0, ',', ' ') }} FCFA</div>
                            </div>
                        </div>
                    </div>
                    
                    @if($labPayment->notes)
                    <div class="mt-6">
                        <div class="text-sm opacity-70 mb-2">Notes</div>
                        <div class="bg-base-200 p-3 rounded-lg">{{ $labPayment->notes }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Analyses payées -->
            <div class="card bg-base-100 shadow-xl mt-6">
                <div class="card-body">
                    <h3 class="card-title text-secondary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Analyses Payées
                    </h3>
                    
                    <div class="space-y-3 mt-4">
                        @foreach($labPayment->lab_tests as $test)
                            <div class="card bg-base-200 shadow-sm">
                                <div class="card-body p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-semibold">{{ $test['name'] }}</h4>
                                            <div class="badge badge-outline badge-sm">{{ $test['code'] }}</div>
                                            @if(isset($test['urgency']) && $test['urgency'] !== 'normal')
                                                <div class="badge 
                                                    @if($test['urgency'] === 'urgent') badge-warning
                                                    @elseif($test['urgency'] === 'stat') badge-error
                                                    @endif badge-sm ml-2">
                                                    {{ strtoupper($test['urgency']) }}
                                                </div>
                                            @endif
                                        </div>
                                        <div class="text-right">
                                            <div class="font-bold text-primary">{{ number_format($test['price'], 0, ',', ' ') }} FCFA</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <div class="divider"></div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold">Total</span>
                        <span class="text-2xl font-bold text-success">{{ number_format($labPayment->total_amount, 0, ',', ' ') }} FCFA</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informations patient et bon de travail -->
        <div class="lg:col-span-1">
            <!-- Patient -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title text-primary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        Patient
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <div class="avatar placeholder">
                                <div class="bg-primary text-primary-content rounded-full w-12">
                                    <span>{{ substr($labPayment->patient->first_name, 0, 1) }}{{ substr($labPayment->patient->last_name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="font-bold">{{ $labPayment->patient->first_name }} {{ $labPayment->patient->last_name }}</div>
                                <div class="text-sm opacity-70">{{ $labPayment->patient->phone_number }}</div>
                            </div>
                        </div>
                        
                        @if($labPayment->patient->date_of_birth)
                        <div>
                            <div class="text-sm opacity-70">Âge</div>
                            <div class="font-semibold">{{ $labPayment->patient->getAge() }} ans</div>
                        </div>
                        @endif
                        
                        @if($labPayment->patient->patient_number)
                        <div>
                            <div class="text-sm opacity-70">N° Patient</div>
                            <div class="font-semibold">{{ $labPayment->patient->patient_number }}</div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Bon de travail -->
            @if($labPayment->workOrder)
            <div class="card bg-gradient-to-br from-secondary to-accent text-secondary-content shadow-xl mt-6">
                <div class="card-body">
                    <h3 class="card-title">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Bon de Travail
                    </h3>
                    
                    <div class="space-y-3">
                        <div>
                            <div class="text-sm opacity-70">Numéro</div>
                            <div class="font-bold">{{ $labPayment->workOrder->work_order_number }}</div>
                        </div>
                        
                        <div>
                            <div class="text-sm opacity-70">Statut</div>
                            <div class="badge badge-outline">{{ $labPayment->workOrder->status_label }}</div>
                        </div>
                        
                        <div>
                            <div class="text-sm opacity-70">Priorité</div>
                            <div class="badge 
                                @if($labPayment->workOrder->priority === 'urgent') badge-warning
                                @elseif($labPayment->workOrder->priority === 'stat') badge-error
                                @else badge-info
                                @endif">
                                {{ $labPayment->workOrder->priority_label }}
                            </div>
                        </div>
                        
                        @if($labPayment->workOrder->assigned_to)
                        <div>
                            <div class="text-sm opacity-70">Assigné à</div>
                            <div class="font-semibold">{{ $labPayment->workOrder->assignedTechnician->name }}</div>
                        </div>
                        @endif
                    </div>
                    
                    <div class="card-actions justify-end mt-4">
                        <div class="badge badge-success">Généré automatiquement</div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Instructions -->
            <div class="alert alert-info mt-6">
                <svg class="w-6 h-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h3 class="font-bold">Instructions pour le patient</h3>
                    <div class="text-xs">
                        1. Présentez-vous au laboratoire avec ce reçu<br>
                        2. Le technicien effectuera les prélèvements<br>
                        3. Les résultats seront disponibles selon les délais
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
