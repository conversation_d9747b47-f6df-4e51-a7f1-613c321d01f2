<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lab_work_orders', function (Blueprint $table) {
            $table->id();
            $table->string('work_order_number')->unique(); // Numéro bon de travail
            $table->foreignId('lab_payment_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('restrict');
            $table->string('prescription_number'); // Référence prescription médecin
            $table->json('lab_tests_details'); // Détails des analyses à effectuer
            $table->text('clinical_information')->nullable(); // Infos cliniques du médecin
            $table->enum('priority', ['normal', 'urgent', 'stat'])->default('normal');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'validated', 'cancelled'])->default('pending');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null'); // Technicien assigné
            $table->timestamp('received_at')->nullable(); // Quand le patient est arrivé au labo
            $table->timestamp('started_at')->nullable(); // Début des analyses
            $table->timestamp('completed_at')->nullable(); // Fin des analyses
            $table->timestamp('validated_at')->nullable(); // Validation finale
            $table->foreignId('validated_by')->nullable()->constrained('users')->onDelete('set null'); // Qui a validé
            $table->text('technician_notes')->nullable();
            $table->timestamps();

            // Index pour recherche et performance
            $table->index(['status', 'priority']);
            $table->index(['assigned_to', 'status']);
            $table->index('prescription_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lab_work_orders');
    }
};
