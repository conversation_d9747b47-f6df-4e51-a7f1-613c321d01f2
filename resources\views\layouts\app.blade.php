<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'GlobalCare Solutions' }} - Gestion Clinique</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="min-h-screen bg-base-100">
    <div class="drawer">
        <input id="main-drawer" type="checkbox" class="drawer-toggle" />
        
        <div class="drawer-content">
            @auth
                <!-- Navigation -->
                <div class="navbar bg-base-100 shadow-lg">
                    <div class="navbar-start">
                        <label for="main-drawer" class="btn btn-square btn-ghost drawer-button lg:hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-5 h-5 stroke-current"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>
                        </label>
                        <div class="flex-shrink-0">
                            <h1 class="text-xl font-bold text-primary">
                                <span class="text-primary">Global</span><span class="text-secondary">Care</span>
                            </h1>
                        </div>
                    </div>
                    
                    <div class="navbar-center hidden lg:flex">
                        <div class="tabs tabs-boxed bg-base-200">
                            @hasrole('admin')
                                <a href="{{ route('admin.dashboard') }}"
                                   class="tab {{ request()->routeIs('admin.*') ? 'tab-active' : '' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                        <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Administration
                                </a>
                            @endhasrole

                            @hasrole('admin|receptionist')
                                <a href="{{ route('receptionist.dashboard') }}"
                                   class="tab {{ request()->routeIs('receptionist.dashboard') ? 'tab-active' : '' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                    </svg>
                                    Accueil
                                </a>
                                <a href="{{ route('patients.index') }}"
                                   class="tab {{ request()->routeIs('patients.*') ? 'tab-active' : '' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                                    </svg>
                                    Patients
                                </a>
                            @endhasrole
                            
                            @hasrole('admin|doctor')
                                <a href="{{ route('doctor.dashboard') }}"
                                   class="tab {{ request()->routeIs('doctor.*') ? 'tab-active' : '' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                                    </svg>
                                    Consultations
                                </a>
                            @endhasrole
                            
                            @hasrole('admin|pharmacist')
                                <a href="{{ route('pharmacist.dashboard') }}"
                                   class="tab {{ request()->routeIs('pharmacist.*') ? 'tab-active' : '' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                    </svg>
                                    Pharmacie
                                </a>
                            @endhasrole
                            
                            @hasrole('admin|lab_technician')
                                <a href="{{ route('lab-technician.dashboard') }}"
                                   class="tab {{ request()->routeIs('lab-technician.*') || request()->routeIs('lab.*') ? 'tab-active' : '' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7 4.414v3.758a1 1 0 01-.293.707l-4 4C.817 14.769 2.156 18 4.828 18h10.344c2.672 0 4.011-3.231 2.122-5.121l-4-4A1 1 0 0113 8.172V4.414l.707-.707A1 1 0 0013 2H7zm2 6.172V4h2v4.172a3 3 0 00.879 2.12l1.027 1.028a4 4 0 00-2.171.102l-.47.156a4 4 0 01-2.53 0l-.563-.187a1.993 1.993 0 00-.114-.035l1.063-1.063A3 3 0 009 8.172z" clip-rule="evenodd" />
                                    </svg>
                                    Laboratoire
                                </a>
                            @endhasrole
                        </div>
                    </div>
                    
                    <div class="navbar-end">
                        <div class="dropdown dropdown-end">
                            <div tabindex="0" role="button" class="btn btn-ghost gap-2">
                                <div class="avatar placeholder">
                                    <div class="bg-neutral text-neutral-content rounded-full w-8">
                                        <span class="text-xs">{{ substr(auth()->user()->name, 0, 2) }}</span>
                                    </div>
                                </div>
                                <span class="hidden md:inline">{{ auth()->user()->name }}</span>
                                <div class="badge badge-primary">{{ ucfirst(auth()->user()->getRoleNames()->first()) }}</div>
                            </div>
                            <ul tabindex="0" class="menu dropdown-content bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                                <li>
                                    <a href="#" class="justify-between">
                                        Profil
                                        <span class="badge">Nouveau</span>
                                    </a>
                                </li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                                        @csrf
                                        <button type="submit" class="w-full text-left">
                                            Déconnexion
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            @endauth

            <!-- Page Content -->
            <main class="p-4 lg:p-8">
                @yield('content')
            </main>
        </div>
        
        <!-- Drawer Side -->
        <div class="drawer-side">
            <label for="main-drawer" class="drawer-overlay"></label>
            <ul class="menu p-4 w-80 h-full bg-base-200">
                @hasrole('admin')
                    <li class="menu-title">
                        <span>Administration</span>
                    </li>
                    <li>
                        <a href="{{ route('admin.dashboard') }}"
                           class="{{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.users') }}"
                           class="{{ request()->routeIs('admin.users*') ? 'active' : '' }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                            </svg>
                            Utilisateurs
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.services') }}"
                           class="{{ request()->routeIs('admin.services*') ? 'active' : '' }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                            Services
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.medications') }}"
                           class="{{ request()->routeIs('admin.medications*') ? 'active' : '' }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                            Médicaments
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.lab-tests') }}"
                           class="{{ request()->routeIs('admin.lab-tests*') ? 'active' : '' }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                            Tests de Laboratoire
                        </a>
                    </li>
                    <div class="divider"></div>
                @endhasrole

                @hasrole('admin|receptionist')
                    <li>
                        <a href="{{ route('receptionist.dashboard') }}"
                           class="{{ request()->routeIs('receptionist.dashboard') ? 'active' : '' }}">
                            Accueil
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('patients.index') }}"
                           class="{{ request()->routeIs('patients.*') ? 'active' : '' }}">
                            Patients
                        </a>
                    </li>
                @endhasrole
                
                @hasrole('admin|doctor')
                    <li>
                        <a href="{{ route('doctor.dashboard') }}"
                           class="{{ request()->routeIs('doctor.*') ? 'active' : '' }}">
                            Consultations
                        </a>
                    </li>
                @endhasrole
                
                @hasrole('admin|pharmacist')
                    <li>
                        <a href="{{ route('pharmacist.dashboard') }}"
                           class="{{ request()->routeIs('pharmacist.*') ? 'active' : '' }}">
                            Pharmacie
                        </a>
                    </li>
                @endhasrole
                
                @hasrole('admin|lab_technician')
                    <li>
                        <a href="{{ route('lab-technician.dashboard') }}"
                           class="{{ request()->routeIs('lab-technician.*') || request()->routeIs('lab.*') ? 'active' : '' }}">
                            Laboratoire
                        </a>
                    </li>
                @endhasrole
            </ul>
        </div>
    </div>

    <!-- Notifications -->
    @if(session('success'))
        <div id="notification" class="toast toast-top toast-end z-50">
            <div class="alert alert-success">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                <span>{{ session('success') }}</span>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div id="notification" class="toast toast-top toast-end z-50">
            <div class="alert alert-error">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                <span>{{ session('error') }}</span>
            </div>
        </div>
    @endif

    <script>
        // Auto-hide notifications with animation
        setTimeout(() => {
            const notification = document.getElementById('notification');
            if (notification) {
                notification.classList.add('animate-fade-out');
                setTimeout(() => {
                    notification.remove();
                }, 500);
            }
        }, 5000);

        // Fix dropdown behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                const dropdowns = document.querySelectorAll('.dropdown');
                dropdowns.forEach(dropdown => {
                    if (!dropdown.contains(event.target)) {
                        const details = dropdown.querySelector('details');
                        if (details) {
                            details.removeAttribute('open');
                        }
                    }
                });
            });
        });
    </script>

    <!-- Custom Scripts -->
    @stack('scripts')
</body>
</html>