<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('prescription_items', function (Blueprint $table) {
            // Rendre medication_id nullable pour permettre la saisie manuelle
            $table->foreignId('medication_id')->nullable()->change();
            
            // Ajouter les champs pour la saisie manuelle des médicaments
            $table->string('medication_name')->nullable()->after('medication_id');
            $table->string('medication_form')->nullable()->after('medication_name');
            
            // Rendre quantity nullable car pas toujours nécessaire
            $table->integer('quantity')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('prescription_items', function (Blueprint $table) {
            // Supprimer les nouveaux champs
            $table->dropColumn(['medication_name', 'medication_form']);
            
            // Remettre medication_id comme non nullable
            $table->foreignId('medication_id')->nullable(false)->change();
            
            // Remettre quantity comme non nullable
            $table->integer('quantity')->nullable(false)->change();
        });
    }
};
