<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ConsultationController;
use App\Http\Controllers\LabTechnicianController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\PharmacistController;
use App\Http\Controllers\MedicationInsuranceController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Test route pour Daisy UI
Route::get('/daisy-test', function () {
    return view('daisy-test');
})->name('daisy-test');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Dashboard routes with role-based redirection
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Role-specific dashboards
    Route::get('/admin/dashboard', [DashboardController::class, 'admin'])->name('admin.dashboard');
    Route::get('/receptionist/dashboard', [DashboardController::class, 'receptionist'])->name('receptionist.dashboard');
    Route::get('/doctor/dashboard', [DashboardController::class, 'doctor'])->name('doctor.dashboard');
    Route::get('/nurse/dashboard', [DashboardController::class, 'nurse'])->name('nurse.dashboard');
    Route::get('/pharmacist/dashboard', [DashboardController::class, 'pharmacist'])->name('pharmacist.dashboard');
    Route::get('/lab-technician/dashboard', [DashboardController::class, 'labTechnician'])->name('lab-technician.dashboard');
    Route::get('/accountant/dashboard', [DashboardController::class, 'accountant'])->name('accountant.dashboard');

    // Patient Management Routes
    Route::resource('patients', PatientController::class);
    Route::get('/patients/search/ajax', [PatientController::class, 'search'])->name('patients.search');

    // Appointment Management Routes
    Route::resource('appointments', AppointmentController::class);
    Route::get('/appointments/slots/available', [AppointmentController::class, 'getAvailableSlots'])->name('appointments.available-slots');

    // Payment Management Routes
    Route::resource('payments', PaymentController::class);
    Route::get('/payments/{payment}/receipt', [PaymentController::class, 'showReceipt'])->name('payments.receipt');
    Route::get('/payments/{payment}/download', [PaymentController::class, 'downloadReceipt'])->name('payments.download');
    Route::post('/payments/{payment}/refund', [PaymentController::class, 'refund'])->name('payments.refund');
    Route::get('/appointments/{appointment}/payment', [PaymentController::class, 'getByAppointment'])->name('payments.by-appointment');

    // Lab Payment Routes (Réceptionniste)
    Route::get('/payments/lab/create', [PaymentController::class, 'createLabPayment'])->name('payments.lab.create');
    Route::post('/payments/lab/store', [PaymentController::class, 'storeLabPayment'])->name('payments.lab.store');
    Route::get('/payments/lab/{labPayment}', [PaymentController::class, 'showLabPayment'])->name('payments.lab.show');
    Route::get('/payments/lab/{labPayment}/receipt', [PaymentController::class, 'downloadLabReceipt'])->name('payments.lab.receipt');

    // Lab Technician Routes
    Route::middleware(['role:lab_technician|admin'])->prefix('lab')->name('lab.')->group(function () {
        Route::get('/search', [LabTechnicianController::class, 'searchPatient'])->name('search');
        Route::get('/work-orders', [LabTechnicianController::class, 'indexWorkOrders'])->name('work-orders.index');
        Route::get('/work-orders/{workOrder}', [LabTechnicianController::class, 'showWorkOrder'])->name('work-orders.show');
        Route::post('/work-orders/{workOrder}/start', [LabTechnicianController::class, 'startWorkOrder'])->name('work-orders.start');

        Route::get('/work-orders/{workOrder}/samples/create', [LabTechnicianController::class, 'createSamples'])->name('samples.create');
        Route::post('/work-orders/{workOrder}/samples', [LabTechnicianController::class, 'storeSamples'])->name('samples.store');

        Route::get('/work-orders/{workOrder}/results/create', [LabTechnicianController::class, 'createResults'])->name('results.create');
        Route::post('/work-orders/{workOrder}/results', [LabTechnicianController::class, 'storeResults'])->name('results.store');
        Route::get('/work-orders/{workOrder}/results', [LabTechnicianController::class, 'showResults'])->name('results.show');
        Route::get('/work-orders/{workOrder}/pdf', [LabTechnicianController::class, 'downloadPdf'])->name('results.pdf');
    });
    
    // Pharmacist Routes (Module Pharmacien)
    Route::middleware('role:pharmacist|admin')->prefix('pharmacist')->name('pharmacist.')->group(function () {
        Route::get('/dashboard', [PharmacistController::class, 'dashboard'])->name('dashboard');
        
        // Prescription management
        Route::get('/prescriptions', [PharmacistController::class, 'prescriptions'])->name('prescriptions.index');
        Route::get('/prescriptions/{prescription}', [PharmacistController::class, 'showPrescription'])->name('prescriptions.show');
        Route::post('/prescriptions/{prescription}/dispense', [PharmacistController::class, 'dispensePrescription'])->name('prescriptions.dispense');
        Route::get('/prescriptions/{prescription}/receipt', [PharmacistController::class, 'showReceipt'])->name('prescriptions.receipt');
        Route::get('/prescriptions/{prescription}/receipt/download', [PharmacistController::class, 'downloadReceipt'])->name('prescriptions.receipt.download');
        
        Route::get('/search-patient', [PharmacistController::class, 'searchPatient'])->name('search-patient');
        Route::get('/search-medications', [PharmacistController::class, 'searchMedications'])->name('search-medications');
        
        // Cart system routes
        Route::get('/cart', [PharmacistController::class, 'cart'])->name('cart');
        Route::get('/search-prescriptions', [PharmacistController::class, 'searchPrescriptions'])->name('search-prescriptions');
        Route::get('/prescription-medications/{prescription}', [PharmacistController::class, 'getPrescriptionMedications'])->name('prescription-medications');
        Route::post('/process-cart-sale', [PharmacistController::class, 'processCartSale'])->name('process-cart-sale');
        
        // Sales management
        Route::get('/sales', [PharmacistController::class, 'salesList'])->name('sales.index');
        Route::get('/sales/{sale}/receipt', [PharmacistController::class, 'showSaleReceipt'])->name('sales.receipt');
        Route::get('/sales/{sale}/receipt/download', [PharmacistController::class, 'downloadSaleReceipt'])->name('sales.receipt.download');
        
        // Sales reports
        Route::get('/sales-report', [PharmacistController::class, 'salesReport'])->name('sales-report');
     });
    
    // Doctor's Consultation Routes (Module Médecin)
    Route::middleware('role:doctor|admin')->prefix('doctor')->name('doctor.')->group(function () {
        // Consultations listing
        Route::get('/consultations', [ConsultationController::class, 'index'])->name('consultations.index');
        
        // Consultation history
        Route::get('/consultations/history', [ConsultationController::class, 'history'])->name('consultations.history');

        // Show consultation details
        Route::get('/consultations/{appointment}', [ConsultationController::class, 'show'])->name('consultations.show');

        // Update consultation notes & diagnosis
        Route::put('/consultations/{appointment}', [ConsultationController::class, 'update'])->name('consultations.update');

        // Create prescription
        Route::post('/consultations/{appointment}/prescriptions', [ConsultationController::class, 'createPrescription'])->name('consultations.prescriptions');

        // Order lab tests
        Route::post('/consultations/{appointment}/lab-tests', [ConsultationController::class, 'orderLabTests'])->name('consultations.lab-tests');

        // Record vital signs
        Route::post('/consultations/{appointment}/vital-signs', [ConsultationController::class, 'recordVitalSigns'])->name('consultations.vital-signs');

        // Generate prescription PDF
        Route::get('/prescriptions/{prescription}/pdf', [ConsultationController::class, 'generatePrescriptionPDF'])->name('prescriptions.pdf');

        // Generate lab tests prescription PDF
        Route::get('/consultations/{appointment}/lab-tests/pdf', [ConsultationController::class, 'generateLabTestsPDF'])->name('consultations.lab-tests.pdf');
    });

    // Admin Routes (Module Admin)
    Route::middleware('role:admin')->prefix('admin')->name('admin.')->group(function () {
        // Dashboard admin
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // Gestion des utilisateurs
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::get('/users/create', [AdminController::class, 'createUser'])->name('users.create');
        Route::post('/users', [AdminController::class, 'storeUser'])->name('users.store');
        Route::get('/users/{user}/edit', [AdminController::class, 'editUser'])->name('users.edit');
        Route::put('/users/{user}', [AdminController::class, 'updateUser'])->name('users.update');
        Route::delete('/users/{user}', [AdminController::class, 'deleteUser'])->name('users.delete');

        // Gestion des services
        Route::get('/services', [AdminController::class, 'services'])->name('services');
        Route::get('/services/create', [AdminController::class, 'createService'])->name('services.create');
        Route::post('/services', [AdminController::class, 'storeService'])->name('services.store');
        Route::get('/services/{service}/edit', [AdminController::class, 'editService'])->name('services.edit');
        Route::put('/services/{service}', [AdminController::class, 'updateService'])->name('services.update');
        Route::delete('/services/{service}', [AdminController::class, 'deleteService'])->name('services.delete');

        // Gestion des médicaments
        Route::get('/medications', [AdminController::class, 'medications'])->name('medications.index');
        Route::get('/medications/create', [AdminController::class, 'createMedication'])->name('medications.create');
        Route::post('/medications', [AdminController::class, 'storeMedication'])->name('medications.store');
        Route::get('/medications/{medication}/edit', [AdminController::class, 'editMedication'])->name('medications.edit');
        Route::put('/medications/{medication}', [AdminController::class, 'updateMedication'])->name('medications.update');
        Route::delete('/medications/{medication}', [AdminController::class, 'deleteMedication'])->name('medications.delete');

        // Gestion des assurances des médicaments
        Route::prefix('medications/{medication}/insurance')->name('medications.insurance.')->group(function () {
            Route::get('/', [MedicationInsuranceController::class, 'index'])->name('index');
            Route::get('/create', [MedicationInsuranceController::class, 'create'])->name('create');
            Route::post('/', [MedicationInsuranceController::class, 'store'])->name('store');
            Route::get('/{coverage}/edit', [MedicationInsuranceController::class, 'edit'])->name('edit');
            Route::put('/{coverage}', [MedicationInsuranceController::class, 'update'])->name('update');
            Route::delete('/{coverage}', [MedicationInsuranceController::class, 'destroy'])->name('destroy');
            Route::get('/{insuranceType}/coverage', [MedicationInsuranceController::class, 'getCoverage'])->name('coverage');
        });

        // Mise à jour en lot des assurances
        Route::post('/medications/insurance/bulk-update', [MedicationInsuranceController::class, 'bulkUpdate'])->name('medications.insurance.bulk-update');

        // Gestion de l'inventaire des médicaments
        Route::get('/medications/inventory', [AdminController::class, 'medicationInventory'])->name('medications.inventory');
        Route::post('/medications/inventory', [AdminController::class, 'addMedicationStock'])->name('medications.inventory.add');
        
        // Gestion des tests de laboratoire
        Route::get('/lab-tests', [AdminController::class, 'labTests'])->name('lab-tests');
        Route::get('/lab-tests/create', [AdminController::class, 'createLabTest'])->name('lab-tests.create');
        Route::post('/lab-tests', [AdminController::class, 'storeLabTest'])->name('lab-tests.store');
        Route::get('/lab-tests/{labTest}/edit', [AdminController::class, 'editLabTest'])->name('lab-tests.edit');
        Route::put('/lab-tests/{labTest}', [AdminController::class, 'updateLabTest'])->name('lab-tests.update');
        Route::delete('/lab-tests/{labTest}', [AdminController::class, 'deleteLabTest'])->name('lab-tests.delete');
    });
});