<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LabTest extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'sort_order',
        'test_code',
        'description',
        'price',
        'normal_range',
        'unit',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the lab results for this test.
     */
    public function labResults(): HasMany
    {
        return $this->hasMany(LabResult::class);
    }
}