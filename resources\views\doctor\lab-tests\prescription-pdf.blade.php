<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prescription d'Analyses - {{ $prescriptionNumber }}</title>
    <style>
        body {
            font-family: 'Deja<PERSON>u Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .clinic-name {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .clinic-info {
            font-size: 11px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        
        .prescription-title {
            font-size: 18px;
            font-weight: bold;
            color: #dc2626;
            margin-top: 15px;
        }
        
        .prescription-number {
            font-size: 14px;
            color: #374151;
            font-weight: bold;
        }
        
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .patient-info, .doctor-info {
            width: 48%;
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .info-row {
            margin-bottom: 5px;
        }
        
        .label {
            font-weight: bold;
            color: #374151;
        }
        
        .tests-section {
            margin-top: 30px;
        }
        
        .tests-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .tests-table th {
            background: #3b82f6;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
        }
        
        .tests-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: top;
        }
        
        .tests-table tr:nth-child(even) {
            background: #f8fafc;
        }
        
        .urgency-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .urgency-normal {
            background: #dcfce7;
            color: #166534;
        }
        
        .urgency-urgent {
            background: #fed7aa;
            color: #9a3412;
        }
        
        .urgency-stat {
            background: #fecaca;
            color: #991b1b;
        }
        
        .clinical-info {
            background: #fffbeb;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .clinical-info-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 8px;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            text-align: center;
            font-size: 10px;
            color: #6b7280;
        }
        
        .signature-section {
            margin-top: 40px;
            text-align: right;
        }
        
        .signature-box {
            border: 1px solid #d1d5db;
            width: 200px;
            height: 80px;
            margin-left: auto;
            margin-top: 10px;
            text-align: center;
            padding-top: 60px;
            font-size: 10px;
            color: #6b7280;
        }
        
        .date-time {
            font-size: 11px;
            color: #6b7280;
            margin-top: 10px;
        }
        
        @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="clinic-name">GlobalCare Solutions</div>
        <div class="clinic-info">
            Centre Médical Moderne • Bamako, Mali<br>
            Tél: +223 XX XX XX XX • Email: <EMAIL>
        </div>
        <div class="prescription-title">PRESCRIPTION D'ANALYSES DE LABORATOIRE</div>
        <div class="prescription-number">N° {{ $prescriptionNumber }}</div>
    </div>

    <!-- Patient and Doctor Info -->
    <div class="info-section">
        <div class="patient-info">
            <div class="section-title">INFORMATIONS PATIENT</div>
            <div class="info-row">
                <span class="label">Nom complet:</span> {{ $patient->first_name }} {{ $patient->last_name }}
            </div>
            <div class="info-row">
                <span class="label">Date de naissance:</span> {{ $patient->date_of_birth ? $patient->date_of_birth->format('d/m/Y') : 'Non renseignée' }}
            </div>
            <div class="info-row">
                <span class="label">Âge:</span> {{ $patient->getAge() }} ans
            </div>
            <div class="info-row">
                <span class="label">Sexe:</span> {{ $patient->gender }}
            </div>
            <div class="info-row">
                <span class="label">Téléphone:</span> {{ $patient->phone_number }}
            </div>
            @if($patient->patient_number)
            <div class="info-row">
                <span class="label">N° Patient:</span> {{ $patient->patient_number }}
            </div>
            @endif
        </div>

        <div class="doctor-info">
            <div class="section-title">MÉDECIN PRESCRIPTEUR</div>
            <div class="info-row">
                <span class="label">Dr:</span> {{ $doctor->user->name }}
            </div>
            <div class="info-row">
                <span class="label">Spécialité:</span> {{ $doctor->specialization ?? 'Médecine générale' }}
            </div>
            <div class="info-row">
                <span class="label">Service:</span> {{ $doctor->service->name ?? 'Non spécifié' }}
            </div>
            @if($doctor->license_number)
            <div class="info-row">
                <span class="label">N° Licence:</span> {{ $doctor->license_number }}
            </div>
            @endif
            <div class="info-row">
                <span class="label">Date:</span> {{ now()->format('d/m/Y à H:i') }}
            </div>
        </div>
    </div>

    <!-- Clinical Information -->
    @if($labResults->first()->clinical_information)
    <div class="clinical-info">
        <div class="clinical-info-title">INFORMATIONS CLINIQUES</div>
        <div>{{ $labResults->first()->clinical_information }}</div>
    </div>
    @endif

    <!-- Tests Table -->
    <div class="tests-section">
        <div class="section-title">ANALYSES DEMANDÉES</div>
        
        <table class="tests-table">
            <thead>
                <tr>
                    <th style="width: 15%">Code</th>
                    <th style="width: 35%">Analyse</th>
                    <th style="width: 30%">Description</th>
                    <th style="width: 10%">Urgence</th>
                    <th style="width: 10%">Prix</th>
                </tr>
            </thead>
            <tbody>
                @foreach($labResults as $result)
                <tr>
                    <td><strong>{{ $result->labTest->test_code }}</strong></td>
                    <td><strong>{{ $result->labTest->name }}</strong></td>
                    <td>{{ $result->labTest->description ?? '-' }}</td>
                    <td>
                        <span class="urgency-badge urgency-{{ $result->urgency }}">
                            {{ ucfirst($result->urgency) }}
                        </span>
                    </td>
                    <td>{{ number_format($result->labTest->price, 0, ',', ' ') }} FCFA</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        
        <div style="margin-top: 15px; text-align: right; font-weight: bold; font-size: 14px;">
            <span class="label">Total estimé:</span> 
            {{ number_format($labResults->sum(function($result) { return $result->labTest->price; }), 0, ',', ' ') }} FCFA
        </div>
    </div>

    <!-- Additional Notes -->
    @if($labResults->first()->notes)
    <div class="clinical-info" style="background: #f0f9ff; border-color: #0ea5e9;">
        <div class="clinical-info-title" style="color: #0c4a6e;">NOTES COMPLÉMENTAIRES</div>
        <div>{{ $labResults->first()->notes }}</div>
    </div>
    @endif

    <!-- Instructions -->
    <div style="margin-top: 30px; background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #10b981;">
        <div style="font-weight: bold; color: #065f46; margin-bottom: 8px;">INSTRUCTIONS POUR LE PATIENT</div>
        <ul style="margin: 0; padding-left: 20px; color: #374151;">
            <li>Présentez-vous au laboratoire avec cette prescription</li>
            <li>Respectez les conditions de prélèvement si nécessaires (à jeun, etc.)</li>
            <li>Apportez une pièce d'identité</li>
            <li>Les résultats seront disponibles selon les délais du laboratoire</li>
            @if($labResults->where('urgency', 'urgent')->count() > 0)
            <li><strong>Analyses urgentes:</strong> Résultats prioritaires</li>
            @endif
            @if($labResults->where('urgency', 'stat')->count() > 0)
            <li><strong>Analyses STAT:</strong> Résultats en urgence absolue</li>
            @endif
        </ul>
    </div>

    <!-- Signature -->
    <div class="signature-section">
        <div style="font-weight: bold; margin-bottom: 5px;">Signature et cachet du médecin</div>
        <div class="signature-box">
            Dr {{ $doctor->user->name }}
        </div>
        <div class="date-time">
            Bamako, le {{ now()->format('d/m/Y à H:i') }}
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div>Ce document est généré électroniquement par GlobalCare Solutions</div>
        <div>Prescription N° {{ $prescriptionNumber }} - {{ now()->format('d/m/Y H:i:s') }}</div>
    </div>
</body>
</html>
