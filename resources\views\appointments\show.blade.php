@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center">
                <a href="{{ route('appointments.index') }}" class="mr-4 text-gray-400 hover:text-gray-600">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
                <div>
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                        Détails du Rendez-vous
                    </h2>
                    <p class="mt-1 text-sm text-gray-500">
                        {{ $appointment->appointment_datetime->format('d/m/Y à H:i') }} • 
                        {{ $appointment->service->name }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="bg-white shadow-xl rounded-xl border border-gray-100 overflow-hidden">
            <!-- Status Banner -->
            @if($appointment->status == 'scheduled')
                <div class="bg-blue-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Planifié
                    </div>
                    <div class="flex space-x-2">
                        @if(!$appointment->payment)
                            <a href="{{ route('payments.create', ['appointment_id' => $appointment->id]) }}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                </svg>
                                Payer
                            </a>
                        @endif
                    </div>
                </div>
            @elseif($appointment->status == 'confirmed')
                <div class="bg-green-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Confirmé
                    </div>
                </div>
            @elseif($appointment->status == 'completed')
                <div class="bg-purple-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Terminé
                    </div>
                </div>
            @elseif($appointment->status == 'cancelled')
                <div class="bg-red-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Annulé
                    </div>
                </div>
            @endif

            <!-- Appointment Details -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div>
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Patient</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold">
                                            <a href="{{ route('patients.show', $appointment->patient) }}" class="text-indigo-600 hover:text-indigo-900">
                                                {{ $appointment->patient->first_name }} {{ $appointment->patient->last_name }}
                                            </a>
                                        </h4>
                                        <p class="text-sm text-gray-500">{{ $appointment->patient->patient_number }}</p>
                                    </div>
                                </div>
                                <div class="mt-4 text-sm text-gray-600">
                                    <div class="flex items-center mb-2">
                                        <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                        </svg>
                                        {{ $appointment->patient->phone_number }}
                                    </div>
                                    @if($appointment->patient->email)
                                        <div class="flex items-center mb-2">
                                            <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                            {{ $appointment->patient->email }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Médecin</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold">Dr. {{ $appointment->doctor->user->first_name }} {{ $appointment->doctor->user->last_name }}</h4>
                                        <p class="text-sm text-gray-500">{{ $appointment->doctor->specialization }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Right Column -->
                    <div>
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Détails du service</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center mb-4">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold">{{ $appointment->service->name }}</h4>
                                        <p class="text-sm text-gray-500">{{ number_format($appointment->service->price, 0, ',', ' ') }} FCFA</p>
                                    </div>
                                </div>
                                @if($appointment->service->description)
                                    <div class="text-sm text-gray-600">
                                        <p>{{ $appointment->service->description }}</p>
                                    </div>
                                @endif
                                <div class="mt-4 flex items-center">
                                    <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-sm text-gray-600">Durée estimée: {{ $appointment->service->duration }} min</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Informations complémentaires</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-1">Date et heure</h4>
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        <span class="text-gray-900">{{ $appointment->appointment_datetime->format('d/m/Y à H:i') }}</span>
                                    </div>
                                </div>
                                
                                @if($appointment->reason)
                                    <div class="mb-4">
                                        <h4 class="text-sm font-medium text-gray-700 mb-1">Motif de consultation</h4>
                                        <p class="text-gray-900">{{ $appointment->reason }}</p>
                                    </div>
                                @endif
                                
                                @if($appointment->notes)
                                    <div class="mb-4">
                                        <h4 class="text-sm font-medium text-gray-700 mb-1">Notes</h4>
                                        <p class="text-gray-900">{{ $appointment->notes }}</p>
                                    </div>
                                @endif
                                
                                @if($appointment->is_followup)
                                    <div class="flex items-center text-sm text-blue-600">
                                        <svg class="h-5 w-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        Rendez-vous de suivi
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Info (if exists) -->
                @if($appointment->payment)
                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Informations de paiement</h3>
                        <div class="bg-green-50 border border-green-100 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold text-gray-900">Payé</h4>
                                        <p class="text-sm text-gray-600">
                                            Facture #{{ $appointment->payment->invoice_number }} • 
                                            {{ $appointment->payment->payment_date->format('d/m/Y') }}
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-900">
                                        {{ number_format($appointment->payment->amount, 0, ',', ' ') }} FCFA
                                    </div>
                                    <p class="text-sm text-gray-600">
                                        {{ $appointment->payment->payment_method == 'cash' ? 'Espèces' : 
                                        ($appointment->payment->payment_method == 'mobile_money' ? 'Mobile Money' : 
                                        ($appointment->payment->payment_method == 'card' ? 'Carte' : 
                                        ($appointment->payment->payment_method == 'insurance' ? 'Assurance' : 'Autre'))) }}
                                    </p>
                                </div>
                            </div>
                            <div class="mt-4 flex justify-end">
                                <a href="{{ route('payments.receipt', $appointment->payment) }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200">
                                    <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                    Voir le reçu
                                </a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Actions -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 flex justify-between">
                <div>
                    @if($appointment->status != 'cancelled' && $appointment->status != 'completed')
                        <button type="button" onclick="confirmCancel()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200">
                            <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            Annuler
                        </button>
                    @endif
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('appointments.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                        Retour
                    </a>
                    <a href="{{ route('appointments.edit', $appointment) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                        Modifier
                    </a>
                </div>
            </div>
        </div>

        <!-- Cancel Form (hidden) -->
        <form id="cancel-form" action="{{ route('appointments.update', $appointment) }}" method="POST" class="hidden">
            @csrf
            @method('PUT')
            <input type="hidden" name="status" value="cancelled">
        </form>
    </div>
</div>

<script>
function confirmCancel() {
    if (confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) {
        document.getElementById('cancel-form').submit();
    }
}
</script>
@endsection 