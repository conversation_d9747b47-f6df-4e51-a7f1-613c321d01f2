<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lab_payments', function (Blueprint $table) {
            // Montant original avant assurance
            $table->decimal('original_amount', 10, 2)
                  ->nullable()
                  ->after('total_amount')
                  ->comment('Montant original avant application de l\'assurance');
            
            // Informations d'assurance
            $table->boolean('insurance_applied')
                  ->default(false)
                  ->after('original_amount')
                  ->comment('Si l\'assurance a été appliquée');
            
            $table->string('insurance_type')
                  ->nullable()
                  ->after('insurance_applied')
                  ->comment('Type d\'assurance utilisé');
            
            $table->string('insurance_number')
                  ->nullable()
                  ->after('insurance_type')
                  ->comment('Numéro d\'assurance du patient');
            
            $table->decimal('insurance_coverage_percentage', 5, 2)
                  ->nullable()
                  ->after('insurance_number')
                  ->comment('Pourcentage de couverture appliqué');
            
            $table->decimal('insurance_discount', 10, 2)
                  ->default(0)
                  ->after('insurance_coverage_percentage')
                  ->comment('Montant de la réduction d\'assurance');
            
            // Détails par test (JSON pour stocker les détails de couverture par test)
            $table->json('insurance_details')
                  ->nullable()
                  ->after('insurance_discount')
                  ->comment('Détails de couverture par test de laboratoire');
            
            // Notes supplémentaires
            $table->text('insurance_notes')
                  ->nullable()
                  ->after('insurance_details')
                  ->comment('Notes sur l\'application de l\'assurance');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lab_payments', function (Blueprint $table) {
            $table->dropColumn([
                'original_amount',
                'insurance_applied',
                'insurance_type',
                'insurance_number',
                'insurance_coverage_percentage',
                'insurance_discount',
                'insurance_details',
                'insurance_notes'
            ]);
        });
    }
};
