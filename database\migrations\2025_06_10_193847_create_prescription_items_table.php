<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prescription_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prescription_id')->constrained()->onDelete('cascade');
            $table->foreignId('medication_id')->constrained()->onDelete('restrict');
            $table->string('dosage'); // e.g., "1 tablet"
            $table->string('frequency'); // e.g., "3 times a day"
            $table->string('duration'); // e.g., "7 days"
            $table->integer('quantity');
            $table->text('instructions')->nullable();
            $table->enum('status', ['prescribed', 'dispensed', 'cancelled'])->default('prescribed');
            $table->foreignId('dispensed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('dispensed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prescription_items');
    }
};