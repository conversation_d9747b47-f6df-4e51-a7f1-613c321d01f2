@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Modifier le Médicament
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Modifier les informations de {{ $medication->name }}
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0">
                <a href="{{ route('admin.medications') }}" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Retour
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <form action="{{ route('admin.medications.update', $medication) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Name -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nom commercial *</span>
                                </label>
                                <input type="text" name="name" value="{{ old('name', $medication->name) }}" 
                                       class="input input-bordered @error('name') input-error @enderror" 
                                       placeholder="Ex: Paracétamol, Amoxicilline..." required>
                                @error('name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Generic Name -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nom générique</span>
                                </label>
                                <input type="text" name="generic_name" value="{{ old('generic_name', $medication->generic_name) }}" 
                                       class="input input-bordered @error('generic_name') input-error @enderror" 
                                       placeholder="Dénomination commune internationale">
                                @error('generic_name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Code -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Code médicament *</span>
                                </label>
                                <input type="text" name="code" value="{{ old('code', $medication->code) }}" 
                                       class="input input-bordered @error('code') input-error @enderror" 
                                       placeholder="Ex: PARA500, AMOX250..." required>
                                @error('code')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Unit Price -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Prix unitaire *</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" name="unit_price" value="{{ old('unit_price', $medication->unit_price) }}" 
                                           class="input input-bordered @error('unit_price') input-error @enderror" 
                                           placeholder="0" min="0" step="10" required>
                                    <span class="bg-base-200 px-4 py-3 text-sm">FCFA</span>
                                </div>
                                @error('unit_price')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Dosage Form -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Forme pharmaceutique</span>
                                </label>
                                <select name="dosage_form" class="select select-bordered @error('dosage_form') select-error @enderror">
                                    <option value="">Sélectionner une forme</option>
                                    <option value="comprimé" {{ old('dosage_form', $medication->dosage_form) == 'comprimé' ? 'selected' : '' }}>Comprimé</option>
                                    <option value="gélule" {{ old('dosage_form', $medication->dosage_form) == 'gélule' ? 'selected' : '' }}>Gélule</option>
                                    <option value="sirop" {{ old('dosage_form', $medication->dosage_form) == 'sirop' ? 'selected' : '' }}>Sirop</option>
                                    <option value="injection" {{ old('dosage_form', $medication->dosage_form) == 'injection' ? 'selected' : '' }}>Injection</option>
                                    <option value="pommade" {{ old('dosage_form', $medication->dosage_form) == 'pommade' ? 'selected' : '' }}>Pommade</option>
                                    <option value="suppositoire" {{ old('dosage_form', $medication->dosage_form) == 'suppositoire' ? 'selected' : '' }}>Suppositoire</option>
                                    <option value="gouttes" {{ old('dosage_form', $medication->dosage_form) == 'gouttes' ? 'selected' : '' }}>Gouttes</option>
                                    <option value="spray" {{ old('dosage_form', $medication->dosage_form) == 'spray' ? 'selected' : '' }}>Spray</option>
                                </select>
                                @error('dosage_form')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Strength -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Dosage/Concentration</span>
                                </label>
                                <input type="text" name="strength" value="{{ old('strength', $medication->strength) }}" 
                                       class="input input-bordered @error('strength') input-error @enderror" 
                                       placeholder="Ex: 500mg, 250mg/5ml, 1%...">
                                @error('strength')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>
                        </div>

                        <!-- Manufacturer -->
                        <div class="form-control mt-6">
                            <label class="label">
                                <span class="label-text">Fabricant</span>
                            </label>
                            <input type="text" name="manufacturer" value="{{ old('manufacturer', $medication->manufacturer) }}" 
                                   class="input input-bordered @error('manufacturer') input-error @enderror" 
                                   placeholder="Nom du laboratoire pharmaceutique">
                            @error('manufacturer')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="form-control mt-6">
                            <label class="label">
                                <span class="label-text">Description</span>
                            </label>
                            <textarea name="description" class="textarea textarea-bordered @error('description') textarea-error @enderror" 
                                      placeholder="Indications, contre-indications, effets secondaires...">{{ old('description', $medication->description) }}</textarea>
                            @error('description')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Options -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 mt-6">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Prescription</span>
                                </label>
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" name="prescription_required" value="1" 
                                           class="checkbox checkbox-warning" 
                                           {{ old('prescription_required', $medication->prescription_required) ? 'checked' : '' }}>
                                    <span class="label-text ml-2">Prescription médicale requise</span>
                                </label>
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Statut</span>
                                </label>
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" name="is_active" value="1" 
                                           class="checkbox checkbox-primary" 
                                           {{ old('is_active', $medication->is_active) ? 'checked' : '' }}>
                                    <span class="label-text ml-2">Médicament actif</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-control mt-8">
                            <button type="submit" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Mettre à jour
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Medication Statistics -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title">Statistiques du Médicament</h3>
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-3 mt-4">
                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-figure text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                            </div>
                            <div class="stat-title">Stock Total</div>
                            <div class="stat-value text-primary">{{ $medication->inventories->sum('quantity') }}</div>
                            <div class="stat-desc">unités en stock</div>
                        </div>

                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-figure text-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <div class="stat-title">Prescriptions</div>
                            <div class="stat-value text-secondary">{{ $medication->prescriptionItems()->count() }}</div>
                            <div class="stat-desc">fois prescrit</div>
                        </div>

                        <div class="stat bg-base-200 rounded-lg">
                            <div class="stat-figure text-accent">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                            </div>
                            <div class="stat-title">Valeur Stock</div>
                            <div class="stat-value text-accent">{{ number_format($medication->inventories->sum('quantity') * $medication->unit_price) }}</div>
                            <div class="stat-desc">FCFA</div>
                        </div>
                    </div>

                    @if($medication->inventories->count() > 0)
                        <div class="mt-6">
                            <h4 class="font-semibold mb-3">Détail des stocks :</h4>
                            <div class="overflow-x-auto">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Lot</th>
                                            <th>Quantité</th>
                                            <th>Expiration</th>
                                            <th>Ajouté le</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($medication->inventories->take(5) as $inventory)
                                            <tr>
                                                <td>{{ $inventory->batch_number ?: 'N/A' }}</td>
                                                <td>{{ $inventory->quantity }}</td>
                                                <td>
                                                    @if($inventory->expiry_date)
                                                        {{ $inventory->expiry_date->format('d/m/Y') }}
                                                        @if($inventory->expiry_date->isPast())
                                                            <span class="badge badge-error badge-xs">Expiré</span>
                                                        @elseif($inventory->expiry_date->diffInDays() <= 30)
                                                            <span class="badge badge-warning badge-xs">Bientôt</span>
                                                        @endif
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>{{ $inventory->created_at->format('d/m/Y') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
