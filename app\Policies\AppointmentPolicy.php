<?php

namespace App\Policies;

use App\Models\Appointment;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AppointmentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the appointment.
     */
    public function view(User $user, Appointment $appointment)
    {
        // Le médecin ne peut voir que ses propres rendez-vous
        if ($user->hasRole('doctor')) {
            return $user->doctor && $appointment->doctor_id === $user->doctor->id;
        }
        
        // Les admins, les réceptionnistes peuvent voir tous les rendez-vous
        return $user->hasAnyRole(['admin', 'receptionist']);
    }

    /**
     * Determine whether the user can update the appointment.
     */
    public function update(User $user, Appointment $appointment)
    {
        // Le médecin ne peut mettre à jour que ses propres rendez-vous
        if ($user->hasRole('doctor')) {
            return $user->doctor && $appointment->doctor_id === $user->doctor->id;
        }
        
        // Les admins, les réceptionnistes peuvent mettre à jour tous les rendez-vous
        return $user->hasAnyRole(['admin', 'receptionist']);
    }
}