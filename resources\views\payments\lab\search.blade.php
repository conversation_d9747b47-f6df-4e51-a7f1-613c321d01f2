@extends('layouts.app')

@section('content')
<div class="container mx-auto p-6">
    <!-- Header avec breadcrumb -->
    <div class="mb-8">
        <div class="breadcrumbs text-sm">
            <ul>
                <li><a href="{{ route('receptionist.dashboard') }}">Dashboard</a></li>
                <li><a href="{{ route('payments.index') }}">Paiements</a></li>
                <li>Recherche Prescription</li>
            </ul>
        </div>
        
        <div class="flex items-center justify-between mt-4">
            <div>
                <h1 class="text-3xl font-bold">🔍 Rechercher Prescription d'Analyses</h1>
                <p class="text-base-content/70 mt-2">
                    Saisissez le numéro de prescription pour procéder au paiement des analyses
                </p>
            </div>
            <a href="{{ route('receptionist.dashboard') }}" class="btn btn-ghost">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Retour
            </a>
        </div>
    </div>

    <div class="max-w-2xl mx-auto">
        <!-- Formulaire de recherche -->
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-primary justify-center">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.611L5 14.5"/>
                    </svg>
                    Paiement Analyses de Laboratoire
                </h2>
                
                @if(isset($error))
                    <div class="alert alert-error mb-6">
                        <svg class="w-6 h-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>{{ $error }}</span>
                    </div>
                @endif

                <form action="{{ route('payments.lab.create') }}" method="GET" class="space-y-6">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text text-lg font-semibold">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                Numéro de Prescription
                            </span>
                        </label>
                        <input 
                            type="text" 
                            name="prescription_number" 
                            value="{{ $prescriptionNumber ?? '' }}"
                            placeholder="Ex: LAB-PRESC-20250615-0001" 
                            class="input input-bordered input-primary input-lg text-center font-mono"
                            required
                            autofocus>
                        <label class="label">
                            <span class="label-text-alt">
                                Le numéro de prescription se trouve sur le document remis par le médecin
                            </span>
                        </label>
                    </div>

                    <div class="card-actions justify-center">
                        <button type="submit" class="btn btn-primary btn-lg btn-wide">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                            Rechercher Prescription
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card bg-base-200 shadow-sm mt-6">
            <div class="card-body">
                <h3 class="card-title text-secondary">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Comment procéder ?
                </h3>
                
                <div class="steps steps-vertical lg:steps-horizontal">
                    <div class="step step-primary">
                        <div class="text-left">
                            <div class="font-semibold">1. Prescription du médecin</div>
                            <div class="text-sm opacity-70">Le patient apporte la prescription d'analyses</div>
                        </div>
                    </div>
                    <div class="step step-primary">
                        <div class="text-left">
                            <div class="font-semibold">2. Saisir le numéro</div>
                            <div class="text-sm opacity-70">Tapez le numéro de prescription ci-dessus</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="text-left">
                            <div class="font-semibold">3. Paiement</div>
                            <div class="text-sm opacity-70">Enregistrer le paiement du patient</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="text-left">
                            <div class="font-semibold">4. Laboratoire</div>
                            <div class="text-sm opacity-70">Le patient va au laboratoire avec le reçu</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exemples de numéros -->
        <div class="alert alert-info mt-6">
            <svg class="w-6 h-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h3 class="font-bold">Format du numéro de prescription</h3>
                <div class="text-sm mt-1">
                    <strong>Format :</strong> LAB-PRESC-AAAAMMJJ-NNNN<br>
                    <strong>Exemple :</strong> LAB-PRESC-20250615-0001<br>
                    <em>Ce numéro est généré automatiquement par le système lors de la prescription du médecin</em>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
            <a href="{{ route('patients.index') }}" class="btn btn-outline btn-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                Rechercher Patient
            </a>
            
            <a href="{{ route('payments.index') }}" class="btn btn-outline btn-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                Historique Paiements
            </a>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-focus sur le champ de recherche
        const searchInput = document.querySelector('input[name="prescription_number"]');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }

        // Validation du format en temps réel
        searchInput.addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            
            // Auto-format si l'utilisateur tape juste les chiffres
            if (/^\d{8}-?\d{0,4}$/.test(value.replace('LAB-PRESC-', ''))) {
                if (!value.startsWith('LAB-PRESC-')) {
                    value = 'LAB-PRESC-' + value;
                    e.target.value = value;
                }
            }
        });

        // Validation avant soumission
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const prescriptionNumber = searchInput.value.trim();
            
            if (!prescriptionNumber) {
                e.preventDefault();
                alert('Veuillez saisir un numéro de prescription.');
                searchInput.focus();
                return false;
            }
            
            if (!prescriptionNumber.match(/^LAB-PRESC-\d{8}-\d{4}$/)) {
                e.preventDefault();
                alert('Format de prescription invalide. Exemple: LAB-PRESC-20250615-0001');
                searchInput.focus();
                searchInput.select();
                return false;
            }
        });
    });
</script>
@endpush
@endsection
