<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vital_signs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('recorded_by')->constrained('users')->onDelete('restrict');
            $table->timestamp('recorded_at')->nullable();
            $table->decimal('temperature', 5, 2)->nullable(); // in Celsius
            $table->integer('blood_pressure_systolic')->nullable(); // systolic pressure
            $table->integer('blood_pressure_diastolic')->nullable(); // diastolic pressure
            $table->integer('heart_rate')->nullable(); // beats per minute
            $table->integer('respiratory_rate')->nullable(); // breaths per minute
            $table->decimal('weight', 5, 2)->nullable(); // in kg
            $table->decimal('height', 5, 2)->nullable(); // in cm
            $table->decimal('bmi', 5, 2)->nullable(); // Body Mass Index
            $table->integer('oxygen_saturation')->nullable(); // percentage
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vital_signs');
    }
};