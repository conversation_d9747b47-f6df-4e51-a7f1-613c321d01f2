@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center">
                <a href="{{ route('payments.index') }}" class="mr-4 text-gray-400 hover:text-gray-600">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
                <div>
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                        Détails du Paiement
                    </h2>
                    <p class="mt-1 text-sm text-gray-500">
                        Facture #{{ $payment->invoice_number }} • {{ $payment->payment_date->format('d/m/Y') }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="bg-white shadow-xl rounded-xl border border-gray-100 overflow-hidden mb-8">
            <!-- Status Banner -->
            @if($payment->status == 'completed')
                <div class="bg-green-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Payé
                    </div>
                </div>
            @elseif($payment->status == 'pending')
                <div class="bg-yellow-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        En attente
                    </div>
                </div>
            @elseif($payment->status == 'failed')
                <div class="bg-red-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        Échoué
                    </div>
                </div>
            @elseif($payment->status == 'refunded')
                <div class="bg-gray-500 text-white px-6 py-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                        </svg>
                        Remboursé
                    </div>
                </div>
            @endif

            <!-- Payment Details -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div>
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Patient</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold">
                                            <a href="{{ route('patients.show', $payment->appointment->patient) }}" class="text-indigo-600 hover:text-indigo-900">
                                                {{ $payment->appointment->patient->first_name }} {{ $payment->appointment->patient->last_name }}
                                            </a>
                                        </h4>
                                        <p class="text-sm text-gray-500">{{ $payment->appointment->patient->patient_number }}</p>
                                    </div>
                                </div>
                                <div class="mt-4 text-sm text-gray-600">
                                    <div class="flex items-center mb-2">
                                        <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                        </svg>
                                        {{ $payment->appointment->patient->phone_number }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rendez-vous associé</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <a href="{{ route('appointments.show', $payment->appointment) }}" class="text-indigo-600 hover:text-indigo-900 font-semibold">
                                                Rendez-vous du {{ $payment->appointment->appointment_datetime->format('d/m/Y') }}
                                            </a>
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        {{ $payment->appointment->appointment_datetime->format('H:i') }}
                                    </div>
                                </div>
                                <div class="mt-2 space-y-2 text-sm">
                                    <div>
                                        <span class="text-gray-600">Service:</span>
                                        <span class="font-medium text-gray-900 ml-1">{{ $payment->appointment->service->name }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Médecin:</span>
                                        <span class="font-medium text-gray-900 ml-1">Dr. {{ $payment->appointment->doctor->user->first_name }} {{ $payment->appointment->doctor->user->last_name }}</span>
                                    </div>
                                    @if($payment->appointment->reason)
                                        <div>
                                            <span class="text-gray-600">Motif:</span>
                                            <span class="font-medium text-gray-900 ml-1">{{ $payment->appointment->reason }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div>
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Détails du paiement</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex justify-between items-center mb-4">
                                    <div class="text-sm font-medium text-gray-500">Montant total</div>
                                    <div class="text-2xl font-bold text-gray-900">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</div>
                                </div>
                                <div class="border-t border-gray-200 my-4"></div>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <div class="text-sm text-gray-600">Méthode de paiement</div>
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $payment->payment_method == 'cash' ? 'Espèces' : 
                                            ($payment->payment_method == 'mobile_money' ? 'Mobile Money' : 
                                            ($payment->payment_method == 'card' ? 'Carte bancaire' : 
                                            ($payment->payment_method == 'insurance' ? 'Assurance' : 'Autre'))) }}
                                        </div>
                                    </div>
                                    <div class="flex justify-between">
                                        <div class="text-sm text-gray-600">Date de paiement</div>
                                        <div class="text-sm font-medium text-gray-900">{{ $payment->payment_date->format('d/m/Y H:i') }}</div>
                                    </div>
                                    <div class="flex justify-between">
                                        <div class="text-sm text-gray-600">Statut</div>
                                        <div class="text-sm font-medium">
                                            @if($payment->status == 'completed')
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Payé
                                                </span>
                                            @elseif($payment->status == 'pending')
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    En attente
                                                </span>
                                            @elseif($payment->status == 'failed')
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Échoué
                                                </span>
                                            @elseif($payment->status == 'refunded')
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                    Remboursé
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    @if($payment->transaction_reference)
                                        <div class="flex justify-between">
                                            <div class="text-sm text-gray-600">Référence</div>
                                            <div class="text-sm font-medium text-gray-900">{{ $payment->transaction_reference }}</div>
                                        </div>
                                    @endif
                                    <div class="flex justify-between">
                                        <div class="text-sm text-gray-600">Reçu par</div>
                                        <div class="text-sm font-medium text-gray-900">{{ $payment->receiver->name }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($payment->notes)
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Notes</h3>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <p class="text-sm text-gray-600">{{ $payment->notes }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 flex justify-between">
                <div>
                    @if($payment->status == 'completed')
                        <button type="button" onclick="confirmRefund()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200">
                            <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                            </svg>
                            Rembourser
                        </button>
                    @endif
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('payments.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                        Retour
                    </a>
                    <a href="{{ route('payments.receipt', $payment) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                        <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Voir le reçu
                    </a>
                    <a href="{{ route('payments.download', $payment) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700">
                        <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Télécharger PDF
                    </a>
                </div>
            </div>
        </div>

        <!-- Refund Form (hidden) -->
        @if($payment->status == 'completed')
            <form id="refund-form" action="{{ route('payments.refund', $payment) }}" method="POST" class="hidden">
                @csrf
            </form>
        @endif
    </div>
</div>

<script>
function confirmRefund() {
    if (confirm('Êtes-vous sûr de vouloir rembourser ce paiement ? Cette action est irréversible.')) {
        document.getElementById('refund-form').submit();
    }
}
</script>
@endsection 