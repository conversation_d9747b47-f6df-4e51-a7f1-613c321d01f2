<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LabTest;

class LabTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Hématologie
        $this->createLabTests([
            [
                'name' => 'Numération Formule Sanguine (NFS)',
                'category' => 'Hématologie',
                'test_code' => 'HEM-001',
                'description' => 'Analyse complète des cellules sanguines',
                'price' => 5000,
                'normal_range' => null,
                'unit' => null,
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Groupe Sanguin + Rhésus',
                'category' => 'Hématologie',
                'test_code' => 'HEM-002',
                'description' => 'Détermination du groupe sanguin et facteur rhésus',
                'price' => 3000,
                'normal_range' => null,
                'unit' => null,
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Taux de Prothrombine (TP)',
                'category' => 'Hématologie',
                'test_code' => 'HEM-003',
                'description' => 'Mesure du temps de coagulation',
                'price' => 4000,
                'normal_range' => '70-100%',
                'unit' => '%',
                'sort_order' => 3,
                'is_active' => true,
            ],
        ]);

        // Biochimie
        $this->createLabTests([
            [
                'name' => 'Glycémie à jeun',
                'category' => 'Biochimie',
                'test_code' => 'BIO-001',
                'description' => 'Mesure du taux de glucose dans le sang',
                'price' => 2500,
                'normal_range' => '0.7-1.1',
                'unit' => 'g/L',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Créatinine',
                'category' => 'Biochimie',
                'test_code' => 'BIO-002',
                'description' => 'Évaluation de la fonction rénale',
                'price' => 3000,
                'normal_range' => '6-12',
                'unit' => 'mg/L',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Bilan Lipidique',
                'category' => 'Biochimie',
                'test_code' => 'BIO-003',
                'description' => 'Cholestérol total, HDL, LDL, Triglycérides',
                'price' => 8000,
                'normal_range' => null,
                'unit' => null,
                'sort_order' => 3,
                'is_active' => true,
            ],
        ]);

        // Urologie
        $this->createLabTests([
            [
                'name' => 'Analyse d\'urine (ECBU)',
                'category' => 'Urologie',
                'test_code' => 'URO-001',
                'description' => 'Examen cytobactériologique des urines',
                'price' => 4500,
                'normal_range' => null,
                'unit' => null,
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Protéinurie de 24h',
                'category' => 'Urologie',
                'test_code' => 'URO-002',
                'description' => 'Mesure des protéines dans les urines sur 24h',
                'price' => 5000,
                'normal_range' => '<150',
                'unit' => 'mg/24h',
                'sort_order' => 2,
                'is_active' => true,
            ],
        ]);

        // Parasitologie
        $this->createLabTests([
            [
                'name' => 'Goutte Épaisse',
                'category' => 'Parasitologie',
                'test_code' => 'PARA-001',
                'description' => 'Recherche de paludisme',
                'price' => 3000,
                'normal_range' => 'Négatif',
                'unit' => null,
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Selles POK',
                'category' => 'Parasitologie',
                'test_code' => 'PARA-002',
                'description' => 'Recherche de sang dans les selles',
                'price' => 2500,
                'normal_range' => 'Négatif',
                'unit' => null,
                'sort_order' => 2,
                'is_active' => true,
            ],
        ]);
    }

    /**
     * Create multiple lab tests.
     */
    private function createLabTests(array $tests): void
    {
        foreach ($tests as $test) {
            LabTest::updateOrCreate(
                ['test_code' => $test['test_code']],
                $test
            );
        }
    }
}