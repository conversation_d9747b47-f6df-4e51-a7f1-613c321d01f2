<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MedicationInventory extends Model
{
    use HasFactory;

    protected $fillable = [
        'medication_id',
        'quantity',
        'batch_number',
        'expiry_date',
        'purchase_price',
        'selling_price',
        'current_stock',
        'added_by',
        'added_at',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'expiry_date' => 'date',
        'purchase_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'current_stock' => 'integer',
        'added_at' => 'datetime',
    ];

    /**
     * Get the medication associated with the inventory.
     */
    public function medication(): BelongsTo
    {
        return $this->belongsTo(Medication::class);
    }

    /**
     * Get the user who added the inventory.
     */
    public function addedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_by');
    }
}