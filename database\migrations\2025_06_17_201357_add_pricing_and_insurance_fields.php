<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter les champs de prix aux prescription_items
        Schema::table('prescription_items', function (Blueprint $table) {
            $table->decimal('unit_price', 10, 2)->default(0)->after('quantity');
            $table->decimal('total_price', 10, 2)->default(0)->after('unit_price');
            $table->decimal('insurance_discount', 10, 2)->default(0)->after('total_price');
            $table->decimal('final_price', 10, 2)->default(0)->after('insurance_discount');
            $table->integer('quantity_dispensed')->default(0)->after('final_price');
        });

        // Ajouter les champs d'assurance aux patients
        Schema::table('patients', function (Blueprint $table) {
            $table->boolean('has_insurance')->default(false)->after('blood_group');
            $table->string('insurance_type')->nullable()->after('has_insurance'); // AMO, CNSS, etc.
            $table->string('insurance_number')->nullable()->after('insurance_type');
            $table->decimal('insurance_coverage_percentage', 5, 2)->default(0)->after('insurance_number'); // 0-100%
            $table->date('insurance_expiry_date')->nullable()->after('insurance_coverage_percentage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('prescription_items', function (Blueprint $table) {
            $table->dropColumn([
                'unit_price',
                'total_price', 
                'insurance_discount',
                'final_price',
                'quantity_dispensed'
            ]);
        });

        Schema::table('patients', function (Blueprint $table) {
            $table->dropColumn([
                'has_insurance',
                'insurance_type',
                'insurance_number',
                'insurance_coverage_percentage',
                'insurance_expiry_date'
            ]);
        });
    }
};
