<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'appointment_id',
        'patient_id',
        'amount',
        'original_amount',
        'insurance_applied',
        'insurance_type',
        'insurance_number',
        'insurance_coverage_percentage',
        'insurance_discount',
        'insurance_notes',
        'payment_method',
        'transaction_reference',
        'status',
        'received_by',
        'notes',
        'payment_date',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'original_amount' => 'decimal:2',
        'insurance_applied' => 'boolean',
        'insurance_coverage_percentage' => 'decimal:2',
        'insurance_discount' => 'decimal:2',
        'payment_date' => 'datetime',
    ];

    /**
     * Get the appointment associated with the payment.
     */
    public function appointment(): BelongsTo
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the patient associated with the payment.
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the user who received the payment.
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Calculate insurance discount based on patient's insurance
     */
    public function calculateInsuranceDiscount(Patient $patient, float $originalAmount): array
    {
        if (!$patient->hasValidInsurance()) {
            return [
                'discount' => 0,
                'final_amount' => $originalAmount,
                'coverage_percentage' => 0,
                'insurance_type' => null,
            ];
        }

        $coveragePercentage = $patient->getInsuranceCoveragePercentage();
        $discount = $originalAmount * ($coveragePercentage / 100);
        $finalAmount = $originalAmount - $discount;

        return [
            'discount' => $discount,
            'final_amount' => $finalAmount,
            'coverage_percentage' => $coveragePercentage,
            'insurance_type' => $patient->insurance_type,
            'insurance_number' => $patient->insurance_number,
        ];
    }

    /**
     * Apply insurance to this payment
     */
    public function applyInsurance(Patient $patient, float $originalAmount): void
    {
        $insuranceData = $this->calculateInsuranceDiscount($patient, $originalAmount);

        $this->update([
            'original_amount' => $originalAmount,
            'amount' => $insuranceData['final_amount'],
            'insurance_applied' => $insuranceData['discount'] > 0,
            'insurance_type' => $insuranceData['insurance_type'],
            'insurance_number' => $insuranceData['insurance_number'],
            'insurance_coverage_percentage' => $insuranceData['coverage_percentage'],
            'insurance_discount' => $insuranceData['discount'],
        ]);
    }

    /**
     * Get insurance summary
     */
    public function getInsuranceSummary(): array
    {
        return [
            'applied' => $this->insurance_applied,
            'type' => $this->insurance_type,
            'number' => $this->insurance_number,
            'coverage_percentage' => $this->insurance_coverage_percentage,
            'original_amount' => $this->original_amount,
            'discount' => $this->insurance_discount,
            'final_amount' => $this->amount,
            'savings' => $this->insurance_discount,
        ];
    }
}