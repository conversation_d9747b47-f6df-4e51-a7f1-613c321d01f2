@props(['patient' => null, 'insuranceTypes' => []])

@php
    $availableTypes = $insuranceTypes ?: [
        'AMO' => 'AMO - Assurance Maladie Obligatoire',
        'CNOPS' => 'CNOPS - Caisse Nationale des Organismes de Prévoyance Sociale',
        'CNSS' => 'CNSS - Caisse Nationale de Sécurité Sociale',
        'RAMED' => 'RAMED - Régime d\'Assistance Médicale',
        'Privée' => 'Assurance Privée',
        'Mutuelle' => 'Mutuelle Complémentaire',
        'Assurance_Internationale' => 'Assurance Internationale'
    ];
@endphp

<div class="space-y-6">
    <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Informations d'Assurance</h3>
        
        <!-- Has Insurance Checkbox -->
        <div class="mb-4">
            <label class="flex items-center">
                <input type="checkbox" 
                       name="has_insurance" 
                       value="1" 
                       {{ old('has_insurance', $patient?->has_insurance) ? 'checked' : '' }}
                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                       id="has_insurance_checkbox">
                <span class="ml-2 text-sm text-gray-700">Le patient a une assurance</span>
            </label>
        </div>

        <!-- Insurance Fields (hidden by default if no insurance) -->
        <div id="insurance_fields" class="{{ old('has_insurance', $patient?->has_insurance) ? '' : 'hidden' }} space-y-4">
            
            <!-- Insurance Type -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="insurance_type" class="block text-sm font-medium text-gray-700">Type d'assurance *</label>
                    <select name="insurance_type" 
                            id="insurance_type" 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            required>
                        <option value="">Sélectionner un type</option>
                        @foreach($availableTypes as $value => $label)
                            <option value="{{ $value }}" 
                                    {{ old('insurance_type', $patient?->insurance_type) === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('insurance_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Insurance Name -->
                <div>
                    <label for="insurance_name" class="block text-sm font-medium text-gray-700">Nom de l'assurance</label>
                    <input type="text" 
                           name="insurance_name" 
                           id="insurance_name" 
                           value="{{ old('insurance_name', $patient?->insurance_name) }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           placeholder="Ex: CNSS Rabat, Assurance XYZ">
                    @error('insurance_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Insurance Number and NINA -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="insurance_number" class="block text-sm font-medium text-gray-700">Numéro d'assurance *</label>
                    <input type="text" 
                           name="insurance_number" 
                           id="insurance_number" 
                           value="{{ old('insurance_number', $patient?->insurance_number) }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           placeholder="Numéro de carte d'assurance"
                           required>
                    @error('insurance_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="nina" class="block text-sm font-medium text-gray-700">NINA (optionnel)</label>
                    <input type="text" 
                           name="nina" 
                           id="nina" 
                           value="{{ old('nina', $patient?->nina) }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           placeholder="Numéro d'Identification National d'Assurance">
                    @error('nina')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Custom Coverage Percentage (for variable insurance) -->
            <div id="custom_coverage_field" class="hidden">
                <label for="custom_coverage_percentage" class="block text-sm font-medium text-gray-700">
                    Pourcentage de couverture personnalisé
                    <span class="text-xs text-gray-500">(pour assurance variable)</span>
                </label>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <input type="number" 
                           name="custom_coverage_percentage" 
                           id="custom_coverage_percentage" 
                           value="{{ old('custom_coverage_percentage', $patient?->custom_coverage_percentage) }}"
                           class="block w-full pr-12 rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           placeholder="0"
                           min="0"
                           max="100"
                           step="0.1">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 sm:text-sm">%</span>
                    </div>
                </div>
                @error('custom_coverage_percentage')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Insurance Dates -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="insurance_start_date" class="block text-sm font-medium text-gray-700">Date de début</label>
                    <input type="date" 
                           name="insurance_start_date" 
                           id="insurance_start_date" 
                           value="{{ old('insurance_start_date', $patient?->insurance_start_date?->format('Y-m-d')) }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    @error('insurance_start_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="insurance_expiry_date" class="block text-sm font-medium text-gray-700">Date d'expiration</label>
                    <input type="date" 
                           name="insurance_expiry_date" 
                           id="insurance_expiry_date" 
                           value="{{ old('insurance_expiry_date', $patient?->insurance_expiry_date?->format('Y-m-d')) }}"
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    @error('insurance_expiry_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Insurance Notes -->
            <div>
                <label for="insurance_notes" class="block text-sm font-medium text-gray-700">Notes sur l'assurance</label>
                <textarea name="insurance_notes" 
                          id="insurance_notes" 
                          rows="3"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          placeholder="Notes ou conditions particulières...">{{ old('insurance_notes', $patient?->insurance_notes) }}</textarea>
                @error('insurance_notes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const hasInsuranceCheckbox = document.getElementById('has_insurance_checkbox');
    const insuranceFields = document.getElementById('insurance_fields');
    const insuranceTypeSelect = document.getElementById('insurance_type');
    const customCoverageField = document.getElementById('custom_coverage_field');

    // Toggle insurance fields visibility
    hasInsuranceCheckbox.addEventListener('change', function() {
        if (this.checked) {
            insuranceFields.classList.remove('hidden');
        } else {
            insuranceFields.classList.add('hidden');
        }
    });

    // Show/hide custom coverage field based on insurance type
    insuranceTypeSelect.addEventListener('change', function() {
        // Types d'assurance variable qui nécessitent un pourcentage personnalisé
        const variableTypes = ['Privée', 'Mutuelle', 'Assurance_Internationale'];
        
        if (variableTypes.includes(this.value)) {
            customCoverageField.classList.remove('hidden');
        } else {
            customCoverageField.classList.add('hidden');
        }
    });

    // Trigger change event on page load to set initial state
    insuranceTypeSelect.dispatchEvent(new Event('change'));
});
</script>
