<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('insurance_coverage', function (Blueprint $table) {
            // Ajouter le type d'assurance (fixe ou variable)
            $table->enum('coverage_type', ['fixed', 'variable'])
                  ->default('fixed')
                  ->after('insurance_type');
            
            // Rendre le pourcentage nullable pour les assurances variables
            $table->decimal('coverage_percentage', 5, 2)
                  ->nullable()
                  ->change();
            
            // Ajouter des champs pour une meilleure gestion
            $table->decimal('min_coverage_percentage', 5, 2)
                  ->nullable()
                  ->after('coverage_percentage')
                  ->comment('Pourcentage minimum pour assurance variable');
            
            $table->decimal('max_coverage_percentage', 5, 2)
                  ->nullable()
                  ->after('min_coverage_percentage')
                  ->comment('Pourcentage maximum pour assurance variable');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('insurance_coverage', function (Blueprint $table) {
            $table->dropColumn([
                'coverage_type',
                'min_coverage_percentage',
                'max_coverage_percentage'
            ]);
            
            // Remettre coverage_percentage comme non-nullable
            $table->decimal('coverage_percentage', 5, 2)
                  ->nullable(false)
                  ->change();
        });
    }
};
