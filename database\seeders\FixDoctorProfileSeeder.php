<?php

namespace Database\Seeders;

use App\Models\Doctor;
use App\Models\Service;
use App\Models\User;
use Illuminate\Database\Seeder;

class FixDoctorProfileSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Trouver l'utilisateur <EMAIL> qui n'a pas de profil médecin
        $doctorUser = User::where('email', '<EMAIL>')->first();
        
        if ($doctorUser && !$doctorUser->doctor) {
            // Récupérer un service par défaut (médecine générale)
            $generalService = Service::where('service_code', 'CONS-GEN')->first();
            
            if ($generalService) {
                // Créer le profil médecin
                Doctor::create([
                    'user_id' => $doctorUser->id,
                    'service_id' => $generalService->id,
                    'specialization' => 'Médecine Générale',
                    'qualification' => 'Doctorat en Médecine',
                    'license_number' => 'MG-2025-0001',
                    'biography' => 'Médecin généraliste avec une expérience en soins primaires.',
                    'is_active' => true,
                    'working_hours_start' => '08:00:00',
                    'working_hours_end' => '16:00:00',
                    'working_days' => ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'],
                ]);
                
                echo "Profil médecin créé pour l'utilisateur <EMAIL>\n";
            } else {
                echo "Service de médecine générale non trouvé. Veuillez d'abord exécuter le ServiceSeeder.\n";
            }
        } else {
            echo "Utilisateur <EMAIL> non trouvé ou profil médecin déjà existant.\n";
        }
    }
}