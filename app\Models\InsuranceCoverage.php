<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InsuranceCoverage extends Model
{
    protected $table = 'insurance_coverage';
    
    protected $fillable = [
        'insurance_type',
        'coverage_percentage',
        'description',
        'is_active',
    ];
    
    protected $casts = [
        'coverage_percentage' => 'decimal:2',
        'is_active' => 'boolean',
    ];
    
    /**
     * Get coverage percentage for a specific insurance type
     */
    public static function getCoveragePercentage(string $insuranceType): float
    {
        $coverage = self::where('insurance_type', $insuranceType)
                       ->where('is_active', true)
                       ->first();
                       
        return $coverage ? $coverage->coverage_percentage : 0;
    }
    
    /**
     * Get all active insurance types with their coverage
     */
    public static function getActiveCoverages(): array
    {
        return self::where('is_active', true)
                  ->pluck('coverage_percentage', 'insurance_type')
                  ->toArray();
    }
}
