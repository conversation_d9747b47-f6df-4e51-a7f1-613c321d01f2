<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu de Vente #{{ str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .clinic-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .clinic-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .receipt-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-top: 15px;
        }
        
        .receipt-number {
            background: white;
            color: #2563eb;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            float: right;
            margin-top: -60px;
            margin-right: 20px;
        }
        
        .receipt-number .label {
            font-size: 10px;
            font-weight: bold;
        }
        
        .receipt-number .number {
            font-size: 18px;
            font-weight: bold;
        }
        
        .info-section {
            display: table;
            width: 100%;
            margin-bottom: 25px;
        }
        
        .info-left, .info-right {
            display: table-cell;
            width: 48%;
            vertical-align: top;
            padding: 15px;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .info-left {
            margin-right: 2%;
        }
        
        .info-title {
            font-size: 14px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 12px;
            text-align: center;
            background-color: #dbeafe;
            padding: 8px;
            border-radius: 6px;
        }
        
        .info-item {
            margin-bottom: 8px;
            font-size: 12px;
            display: table;
            width: 100%;
        }
        
        .info-label {
            display: table-cell;
            font-weight: bold;
            color: #374151;
            width: 40%;
            padding-right: 10px;
        }
        
        .info-value {
            display: table-cell;
            color: #1f2937;
            width: 60%;
        }
        
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 9px;
            font-weight: 500;
        }
        
        .badge-blue {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .badge-green {
            background: #dcfce7;
            color: #166534;
        }
        
        .badge-gray {
            background: #f3f4f6;
            color: #374151;
        }
        
        .badge-purple {
            background: #f3e8ff;
            color: #7c3aed;
        }
        
        .medications-section {
            margin-bottom: 25px;
        }
        
        .medications-title {
            font-size: 16px;
            font-weight: bold;
            color: #7c2d12;
            margin-bottom: 15px;
            text-align: center;
            background-color: #fed7aa;
            padding: 10px;
            border-radius: 8px;
        }
        
        .medications-section h3 {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .medications-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
            border: 1px solid #d1d5db;
        }
        
        .medications-table th {
            background-color: #f1f5f9;
            color: #1e40af;
            font-weight: bold;
            padding: 12px 8px;
            text-align: left;
            border: 1px solid #cbd5e1;
        }
        
        .medications-table td {
            padding: 10px 8px;
            border: 1px solid #e2e8f0;
            vertical-align: top;
            background-color: #fafafa;
        }
        
        .medications-table tr:nth-child(even) td {
            background-color: #f8fafc;
        }
        
        .medications-table .text-right {
            text-align: right;
        }
        
        .medication-name {
            font-weight: bold;
            color: #1f2937;
        }
        
        .medication-dosage {
            color: #6b7280;
            font-size: 9px;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-section {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .totals-table {
            width: 100%;
            max-width: 300px;
            margin-left: auto;
        }
        
        .totals-table td {
            padding: 5px 0;
            font-size: 11px;
        }
        
        .totals-table .label {
            color: #6b7280;
        }
        
        .totals-table .value {
            text-align: right;
            font-weight: 500;
        }
        
        .total-row {
            border-top: 1px solid #d1d5db;
            padding-top: 8px !important;
        }
        
        .total-row .label {
            font-size: 14px;
            font-weight: bold;
            color: #1f2937;
        }
        
        .total-row .value {
            font-size: 14px;
            font-weight: bold;
            color: #2563eb;
        }
        
        .footer {
            background: #eff6ff;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dbeafe;
        }
        
        .footer .thank-you {
            font-size: 12px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .footer .notice {
            font-size: 9px;
            color: #3730a3;
            margin-bottom: 10px;
        }
        
        .footer .generated {
            font-size: 8px;
            color: #6366f1;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="clinic-name">CLINIQUE MÉDICALE PRIVÉE</div>
            <div class="clinic-info">
                Adresse: [Adresse de la clinique]<br>
                Téléphone: [Numéro de téléphone]<br>
                Email: [Email de contact]
            </div>
            <div class="receipt-title">REÇU DE VENTE PHARMACIE</div>
            <div style="margin-top: 10px; font-size: 16px; font-weight: bold; color: #2563eb;">
                N° {{ str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}
            </div>
        </div>

        <!-- Information Section -->
        <div class="info-section">
            <!-- Patient Information -->
            <div class="info-left">
                <div class="info-title">INFORMATIONS PATIENT</div>
                <div class="info-item">
                    <div class="info-label">Nom complet:</div>
                    <div class="info-value">{{ $sale->patient->first_name }} {{ $sale->patient->last_name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">N° Patient:</div>
                    <div class="info-value">{{ $sale->patient->patient_number }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Téléphone:</div>
                    <div class="info-value">{{ $sale->patient->phone ?? 'N/A' }}</div>
                </div>
                @if($sale->patient->date_of_birth)
                <div class="info-item">
                    <div class="info-label">Date de naissance:</div>
                    <div class="info-value">{{ $sale->patient->date_of_birth->format('d/m/Y') }}</div>
                </div>
                @endif
            </div>

            <!-- Sale Information -->
            <div class="info-right">
                <div class="info-title">INFORMATIONS VENTE</div>
                <div class="info-item">
                    <div class="info-label">Date de vente:</div>
                    <div class="info-value">{{ $sale->dispensed_at->format('d/m/Y à H:i') }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Type de vente:</div>
                    <div class="info-value">
                        @if($sale->sale_type == 'prescription')
                            <span class="badge badge-blue">Prescription</span>
                        @else
                            <span class="badge badge-green">Vente Directe</span>
                        @endif
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">Mode de paiement:</div>
                    <div class="info-value">
                        @if($sale->payment_method == 'cash')
                            <span class="badge badge-gray">Espèces</span>
                        @else
                            <span class="badge badge-purple">Mobile Money</span>
                        @endif
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">Pharmacien:</div>
                    <div class="info-value">{{ $sale->pharmacist->first_name ?? 'N/A' }} {{ $sale->pharmacist->last_name ?? '' }}</div>
                </div>
                @if($sale->prescription_id)
                <div class="info-item">
                    <div class="info-label">N° Prescription:</div>
                    <div class="info-value">#{{ str_pad($sale->prescription_id, 6, '0', STR_PAD_LEFT) }}</div>
                </div>
                @endif
            </div>
        </div>

        <!-- Medications Section -->
        <div class="medications-section">
            <div class="medications-title">MÉDICAMENTS VENDUS</div>
            <table class="medications-table">
                <thead>
                    <tr>
                        <th>Médicament</th>
                        <th>Lot</th>
                        <th>Expiration</th>
                        <th class="text-right">Qté</th>
                        <th class="text-right">Prix Unit.</th>
                        <th class="text-right">Sous-total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($sale->saleItems as $item)
                        <tr>
                            <td>
                                <div class="medication-name">{{ $item->medicationInventory->medication->name }}</div>
                                <div class="medication-dosage">{{ $item->medicationInventory->medication->dosage }}</div>
                            </td>
                            <td>{{ $item->medicationInventory->batch_number }}</td>
                            <td>{{ $item->medicationInventory->expiry_date->format('d/m/Y') }}</td>
                            <td class="text-right">{{ $item->quantity }}</td>
                            <td class="text-right">{{ number_format($item->unit_price, 0, ',', ' ') }} FCFA</td>
                            <td class="text-right">{{ number_format($item->subtotal, 0, ',', ' ') }} FCFA</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">Sous-total:</td>
                    <td class="value">{{ number_format($sale->saleItems->sum('subtotal'), 0, ',', ' ') }} FCFA</td>
                </tr>
                @if($sale->total_discount > 0)
                <tr>
                    <td class="label">Remise:</td>
                    <td class="value" style="color: #059669;">-{{ number_format($sale->total_discount, 0, ',', ' ') }} FCFA</td>
                </tr>
                @endif
                <tr class="total-row">
                    <td class="label">Total à payer:</td>
                    <td class="value">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                </tr>
                <tr>
                    <td class="label">Montant payé:</td>
                    <td class="value" style="color: #059669;">{{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA</td>
                </tr>
                @if($sale->amount_paid > $sale->total_amount)
                <tr>
                    <td class="label">Monnaie rendue:</td>
                    <td class="value" style="color: #2563eb;">{{ number_format($sale->amount_paid - $sale->total_amount, 0, ',', ' ') }} FCFA</td>
                </tr>
                @endif
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="thank-you">Merci pour votre confiance !</div>
            <div class="notice">
                Ce reçu fait foi de paiement. Conservez-le précieusement.<br>
                Pour toute réclamation, veuillez présenter ce reçu.
            </div>
            <div class="generated">
                Reçu généré le {{ now()->format('d/m/Y à H:i:s') }}<br>
                Système de Gestion Clinique - Version 1.0
            </div>
        </div>
    </div>
</body>
</html>