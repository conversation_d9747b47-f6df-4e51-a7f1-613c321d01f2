<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lab_samples', function (Blueprint $table) {
            $table->id();
            $table->string('sample_code')->unique(); // Code-barres échantillon
            $table->foreignId('lab_work_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('lab_result_id')->nullable()->constrained()->onDelete('cascade'); // Lien vers le résultat (nullable)
            $table->foreignId('patient_id')->constrained()->onDelete('restrict');
            $table->enum('sample_type', ['blood', 'urine', 'stool', 'saliva', 'swab', 'other'])->default('blood');
            $table->string('container_type')->nullable(); // Type de tube/contenant
            $table->decimal('volume', 8, 2)->nullable(); // Volume en mL
            $table->enum('status', ['collected', 'processing', 'analyzed', 'discarded'])->default('collected');
            $table->timestamp('collected_at'); // Moment du prélèvement
            $table->foreignId('collected_by')->constrained('users')->onDelete('restrict'); // Qui a prélevé
            $table->timestamp('analyzed_at')->nullable(); // Moment de l'analyse
            $table->foreignId('analyzed_by')->nullable()->constrained('users')->onDelete('set null'); // Qui a analysé
            $table->text('collection_notes')->nullable(); // Notes sur le prélèvement
            $table->text('storage_conditions')->nullable(); // Conditions de stockage
            $table->timestamp('expiry_date')->nullable(); // Date limite d'analyse
            $table->timestamps();

            // Index pour traçabilité
            $table->index(['status', 'collected_at']);
            $table->index(['patient_id', 'collected_at']);
            $table->index('sample_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lab_samples');
    }
};
