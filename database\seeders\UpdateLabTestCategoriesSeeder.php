<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\LabTest;

class UpdateLabTestCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categoriesMapping = [
            // Hématologie
            'NFS001' => ['category' => 'Hématologie', 'sort_order' => 1],
            'VS001' => ['category' => 'Hématologie', 'sort_order' => 2],
            'TC001' => ['category' => 'Hématologie', 'sort_order' => 3],

            // Biochimie
            'GLY001' => ['category' => 'Biochimie', 'sort_order' => 1],
            'CREA001' => ['category' => 'Biochimie', 'sort_order' => 2],
            'UREE001' => ['category' => 'Biochimie', 'sort_order' => 3],
            'TRANS001' => ['category' => 'Biochimie', 'sort_order' => 4],
            'CHOL001' => ['category' => 'Biochimie', 'sort_order' => 5],
            'TG001' => ['category' => 'Biochimie', 'sort_order' => 6],
            'CRP001' => ['category' => 'Biochimie', 'sort_order' => 7],
            'PCT001' => ['category' => 'Biochimie', 'sort_order' => 8],

            // Urologie
            'ECBU001' => ['category' => 'Urologie', 'sort_order' => 1],
            'BU001' => ['category' => 'Urologie', 'sort_order' => 2],

            // Parasitologie
            'EPS001' => ['category' => 'Parasitologie', 'sort_order' => 1],
            'GE001' => ['category' => 'Parasitologie', 'sort_order' => 2],

            // Hormonologie
            'HCG001' => ['category' => 'Hormonologie', 'sort_order' => 1],

            // Immunologie/Sérologie
            'VIH001' => ['category' => 'Immunologie', 'sort_order' => 1],
            'HBV001' => ['category' => 'Immunologie', 'sort_order' => 2],
        ];

        foreach ($categoriesMapping as $testCode => $data) {
            LabTest::where('test_code', $testCode)->update($data);
        }
    }
}
