<?php

namespace App\Providers;

use App\Models\Appointment;
use App\Policies\AppointmentPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Appointment::class => AppointmentPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Définir les gates pour les rôles
        Gate::define('admin', function ($user) {
            return $user->hasRole('admin');
        });

        Gate::define('doctor', function ($user) {
            return $user->hasRole('doctor');
        });

        Gate::define('receptionist', function ($user) {
            return $user->hasRole('receptionist');
        });

        Gate::define('nurse', function ($user) {
            return $user->hasRole('nurse');
        });

        Gate::define('pharmacist', function ($user) {
            return $user->hasRole('pharmacist');
        });

        Gate::define('lab_technician', function ($user) {
            return $user->hasRole('lab_technician');
        });

        Gate::define('accountant', function ($user) {
            return $user->hasRole('accountant');
        });
    }
}