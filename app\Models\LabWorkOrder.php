<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LabWorkOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'work_order_number',
        'lab_payment_id',
        'patient_id',
        'prescription_number',
        'lab_tests_details',
        'clinical_information',
        'priority',
        'status',
        'assigned_to',
        'received_at',
        'started_at',
        'completed_at',
        'validated_at',
        'validated_by',
        'technician_notes',
    ];

    protected $casts = [
        'lab_tests_details' => 'array',
        'received_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'validated_at' => 'datetime',
    ];

    /**
     * Get the lab payment associated with this work order.
     */
    public function labPayment(): BelongsTo
    {
        return $this->belongsTo(LabPayment::class);
    }

    /**
     * Get the patient associated with this work order.
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the technician assigned to this work order.
     */
    public function assignedTechnician(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who validated this work order.
     */
    public function validator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    /**
     * Get the samples associated with this work order.
     */
    public function samples(): HasMany
    {
        return $this->hasMany(LabSample::class);
    }

    /**
     * Get the lab results associated with this work order.
     */
    public function labResults(): HasMany
    {
        return $this->hasMany(LabResult::class, 'prescription_number', 'prescription_number');
    }

    /**
     * Generate a unique work order number.
     */
    public static function generateWorkOrderNumber(): string
    {
        $date = now()->format('Ymd');
        $count = self::whereDate('created_at', today())->count() + 1;
        return 'WO-' . $date . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get priority label.
     */
    public function getPriorityLabelAttribute(): string
    {
        return match($this->priority) {
            'normal' => 'Normal',
            'urgent' => 'Urgent',
            'stat' => 'STAT',
            default => ucfirst($this->priority),
        };
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'in_progress' => 'En cours',
            'completed' => 'Terminé',
            'validated' => 'Validé',
            'cancelled' => 'Annulé',
            default => ucfirst($this->status),
        };
    }

    /**
     * Check if work order can be started.
     */
    public function canBeStarted(): bool
    {
        return $this->status === 'pending' && $this->received_at !== null;
    }

    /**
     * Check if work order can be completed.
     */
    public function canBeCompleted(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if work order can be validated.
     */
    public function canBeValidated(): bool
    {
        return $this->status === 'completed';
    }
}
