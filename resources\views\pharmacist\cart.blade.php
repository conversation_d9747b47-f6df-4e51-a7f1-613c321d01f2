@extends('layouts.app')

@section('title', 'Panier de Médicaments')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Panier de Médicaments</h1>
            <p class="text-gray-600 mt-1">Système de vente indépendant avec gestion d'assurance par médicament</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('pharmacist.prescriptions.index') }}" class="btn btn-outline btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Ordonnances
            </a>
            <a href="{{ route('pharmacist.cart') }}" class="btn btn-outline btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                Vente Directe
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column: Prescription Reference & Medication Search -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Prescription Reference -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-lg mb-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Référence Ordonnance
                    </h2>
                    
                    <!-- Prescription Search -->
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text">Rechercher une ordonnance</span>
                        </label>
                        <input type="text" 
                               id="prescriptionSearch" 
                               placeholder="Numéro d'ordonnance ou nom du patient" 
                               class="input input-bordered w-full">
                        <div id="prescriptionResults" class="hidden mt-2 max-h-60 overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-lg"></div>
                    </div>
                    
                    <!-- Selected Prescription -->
                    <div id="selectedPrescription" class="hidden mt-4 p-4 bg-blue-50 rounded-lg">
                        <h3 class="font-medium text-blue-900">Ordonnance sélectionnée</h3>
                        <div id="prescriptionDetails" class="text-sm text-blue-700 mt-2"></div>
                        <button type="button" onclick="clearPrescriptionSelection()" class="btn btn-sm btn-outline btn-error mt-2">
                            Effacer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Patient Selection -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-lg mb-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Patient
                    </h2>
                    
                    <!-- Patient Search -->
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text">Rechercher un patient</span>
                        </label>
                        <input type="text" 
                               id="patientSearch" 
                               placeholder="Nom, prénom ou numéro patient" 
                               class="input input-bordered w-full">
                        <div id="patientResults" class="hidden mt-2 max-h-60 overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-lg"></div>
                    </div>
                    
                    <!-- Selected Patient -->
                    <div id="selectedPatient" class="hidden mt-4 p-4 bg-green-50 rounded-lg">
                        <h3 class="font-medium text-green-900">Patient sélectionné</h3>
                        <div id="patientDetails" class="text-sm text-green-700 mt-2"></div>
                        <div id="insuranceInfo" class="text-sm text-green-600 mt-1"></div>
                        <button type="button" onclick="clearPatientSelection()" class="btn btn-sm btn-outline btn-error mt-2">
                            Effacer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Medication Search -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-lg mb-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                        </svg>
                        Recherche de Médicaments
                    </h2>
                    
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text">Rechercher un médicament</span>
                        </label>
                        <input type="text" 
                               id="medicationSearch" 
                               placeholder="Nom, principe actif, code..." 
                               class="input input-bordered w-full">
                        <div id="medicationResults" class="hidden mt-2 max-h-60 overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-lg"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Cart -->
        <div class="lg:col-span-2">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h2 class="card-title text-lg mb-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                        Panier (<span id="cartItemCount">0</span> articles)
                    </h2>
                    
                    <!-- Cart Items -->
                    <div id="cartItems" class="space-y-4 mb-6">
                        <div class="text-center text-gray-500 py-8">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                            </svg>
                            <p>Votre panier est vide</p>
                            <p class="text-sm">Recherchez des médicaments pour commencer</p>
                        </div>
                    </div>
                    
                    <!-- Cart Summary -->
                    <div id="cartSummary" class="hidden">
                        <div class="divider"></div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span>Sous-total:</span>
                                    <span id="subtotal">0 FCFA</span>
                                </div>
                                <div class="flex justify-between text-green-600">
                                    <span>Remise assurance:</span>
                                    <span id="insuranceDiscount">0 FCFA</span>
                                </div>
                                <div class="flex justify-between text-lg font-bold">
                                    <span>Total à payer:</span>
                                    <span id="totalAmount">0 FCFA</span>
                                </div>
                            </div>
                            
                            <!-- Payment Method -->
                            <div class="form-control w-full max-w-xs mt-4">
                                <label class="label">
                                    <span class="label-text font-medium">Méthode de paiement</span>
                                </label>
                                <select id="paymentMethod" class="select select-bordered">
                                    <option value="">Choisir une méthode</option>
                                    <option value="cash">Espèces</option>
                                    <option value="mobile_money">Mobile Money</option>
                                </select>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex justify-between mt-6">
                                <button type="button" onclick="clearCart()" class="btn btn-outline btn-error">
                                    Vider le panier
                                </button>
                                <button type="button" onclick="processPayment()" id="processPaymentBtn" class="btn btn-primary" disabled>
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Finaliser la vente
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedPatient = null;
let selectedPrescription = null;
let cart = [];
let insuranceCoverages = @json(App\Models\InsuranceCoverage::getActiveCoverages());

// Patient search functionality
document.getElementById('patientSearch').addEventListener('input', function() {
    const query = this.value.trim();
    if (query.length >= 2) {
        searchPatients(query);
    } else {
        hidePatientResults();
    }
});

function searchPatients(query) {
    fetch(`{{ route('pharmacist.search-patient') }}?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displayPatientResults(data);
        })
        .catch(error => {
            console.error('Error searching patients:', error);
        });
}

function displayPatientResults(patients) {
    const resultsDiv = document.getElementById('patientResults');
    
    if (patients.length === 0) {
        resultsDiv.innerHTML = '<div class="p-3 text-gray-500">Aucun patient trouvé</div>';
        resultsDiv.classList.remove('hidden');
        return;
    }
    
    let html = '';
    patients.forEach(patient => {
        html += `
            <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100" 
                 onclick="selectPatient(${patient.id}, '${patient.first_name}', '${patient.last_name}', '${patient.patient_number}', ${patient.has_insurance}, '${patient.insurance_type || ''}', '${patient.insurance_expiry_date || ''}')">  
                <div class="font-medium">${patient.first_name} ${patient.last_name}</div>
                <div class="text-sm text-gray-500">${patient.patient_number}</div>
                ${patient.has_insurance ? `<div class="text-xs text-green-600">Assurance: ${patient.insurance_type}</div>` : ''}
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
    resultsDiv.classList.remove('hidden');
}

function selectPatient(id, firstName, lastName, patientNumber, hasInsurance, insuranceType, insuranceExpiry) {
    selectedPatient = {
        id: id,
        firstName: firstName,
        lastName: lastName,
        patientNumber: patientNumber,
        hasInsurance: hasInsurance,
        insuranceType: insuranceType,
        insuranceExpiry: insuranceExpiry
    };
    
    document.getElementById('patientDetails').innerHTML = `
        <strong>${firstName} ${lastName}</strong><br>
        N° Patient: ${patientNumber}
    `;
    
    if (hasInsurance && insuranceType && insuranceExpiry) {
        const expiryDate = new Date(insuranceExpiry);
        const isValid = expiryDate > new Date();
        document.getElementById('insuranceInfo').innerHTML = `
            Assurance: ${insuranceType} ${isValid ? '(Valide)' : '(Expirée)'}
        `;
    } else {
        document.getElementById('insuranceInfo').innerHTML = 'Pas d\'assurance';
    }
    
    document.getElementById('selectedPatient').classList.remove('hidden');
    document.getElementById('patientSearch').value = `${firstName} ${lastName}`;
    hidePatientResults();
    updateCart();
}

function clearPatientSelection() {
    selectedPatient = null;
    document.getElementById('selectedPatient').classList.add('hidden');
    document.getElementById('patientSearch').value = '';
    updateCart();
}

function hidePatientResults() {
    document.getElementById('patientResults').classList.add('hidden');
}

// Prescription search functionality
document.getElementById('prescriptionSearch').addEventListener('input', function() {
    const query = this.value.trim();
    if (query.length >= 2) {
        searchPrescriptions(query);
    } else {
        hidePrescriptionResults();
    }
});

function searchPrescriptions(query) {
    fetch(`/pharmacist/search-prescriptions?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displayPrescriptionResults(data);
        })
        .catch(error => {
            console.error('Error searching prescriptions:', error);
        });
}

function displayPrescriptionResults(prescriptions) {
    const resultsDiv = document.getElementById('prescriptionResults');
    
    if (prescriptions.length === 0) {
        resultsDiv.innerHTML = '<div class="p-3 text-gray-500">Aucune ordonnance trouvée</div>';
        resultsDiv.classList.remove('hidden');
        return;
    }
    
    let html = '';
    prescriptions.forEach(prescription => {
        html += `
            <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100" 
                 onclick="selectPrescription(${prescription.id}, '${prescription.prescription_number}', '${prescription.patient.first_name}', '${prescription.patient.last_name}', '${prescription.appointment.doctor.first_name} ${prescription.appointment.doctor.last_name}')">
                <div class="font-medium">Ordonnance #${prescription.prescription_number}</div>
                <div class="text-sm text-gray-500">Patient: ${prescription.patient.first_name} ${prescription.patient.last_name}</div>
                <div class="text-sm text-gray-500">Médecin: Dr. ${prescription.appointment.doctor.first_name} ${prescription.appointment.doctor.last_name}</div>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
    resultsDiv.classList.remove('hidden');
}

function selectPrescription(id, prescriptionNumber, patientFirstName, patientLastName, doctorName) {
    selectedPrescription = {
        id: id,
        prescriptionNumber: prescriptionNumber,
        patientFirstName: patientFirstName,
        patientLastName: patientLastName,
        doctorName: doctorName
    };
    
    document.getElementById('prescriptionDetails').innerHTML = `
        <strong>Ordonnance #${prescriptionNumber}</strong><br>
        Patient: ${patientFirstName} ${patientLastName}<br>
        Médecin: Dr. ${doctorName}
    `;
    
    document.getElementById('selectedPrescription').classList.remove('hidden');
    document.getElementById('prescriptionSearch').value = `#${prescriptionNumber}`;
    hidePrescriptionResults();
    
    // Load prescription medications
    loadPrescriptionMedications(id);
}

function clearPrescriptionSelection() {
    selectedPrescription = null;
    document.getElementById('selectedPrescription').classList.add('hidden');
    document.getElementById('prescriptionSearch').value = '';
}

function hidePrescriptionResults() {
    document.getElementById('prescriptionResults').classList.add('hidden');
}

function loadPrescriptionMedications(prescriptionId) {
    fetch(`/pharmacist/prescription-medications/${prescriptionId}`)
        .then(response => response.json())
        .then(data => {
            // Add prescription medications to cart with suggested quantities
            data.forEach(item => {
                addToCart({
                    medicationId: item.medication_id,
                    name: item.medication_name || item.manual_medication_name,
                    form: item.medication_form || item.manual_medication_form,
                    price: item.unit_price,
                    stock: item.stock || 0,
                    quantity: item.quantity,
                    insuranceCoverage: 0, // Default, user can modify
                    fromPrescription: true
                });
            });
        })
        .catch(error => {
            console.error('Error loading prescription medications:', error);
        });
}

// Medication search functionality
document.getElementById('medicationSearch').addEventListener('input', function() {
    const query = this.value.trim();
    if (query.length >= 2) {
        searchMedications(query);
    } else {
        hideMedicationResults();
    }
});

function searchMedications(query) {
    fetch(`{{ route('pharmacist.search-medications') }}?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displayMedicationResults(data);
        })
        .catch(error => {
            console.error('Error searching medications:', error);
        });
}

function displayMedicationResults(medications) {
    const resultsDiv = document.getElementById('medicationResults');
    
    if (medications.length === 0) {
        resultsDiv.innerHTML = '<div class="p-3 text-gray-500">Aucun médicament trouvé</div>';
        resultsDiv.classList.remove('hidden');
        return;
    }
    
    let html = '';
    medications.forEach(med => {
        html += `
            <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100" 
                 onclick="addMedicationToCart(${med.id}, '${med.name}', '${med.form}', ${med.price}, ${med.stock})">
                <div class="font-medium">${med.name}</div>
                <div class="text-sm text-gray-500">${med.form} - ${formatCurrency(med.price)}</div>
                <div class="text-sm ${med.stock > 0 ? 'text-green-600' : 'text-red-600'}">Stock: ${med.stock}</div>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
    resultsDiv.classList.remove('hidden');
}

function addMedicationToCart(medicationId, name, form, price, stock) {
    if (stock <= 0) {
        alert('Ce médicament n\'est pas en stock.');
        return;
    }
    
    addToCart({
        medicationId: medicationId,
        name: name,
        form: form,
        price: price,
        stock: stock,
        quantity: 1,
        insuranceCoverage: 0,
        fromPrescription: false
    });
    
    hideMedicationResults();
    document.getElementById('medicationSearch').value = '';
}

function hideMedicationResults() {
    document.getElementById('medicationResults').classList.add('hidden');
}

// Cart management
function addToCart(item) {
    // Check if item already exists in cart
    const existingIndex = cart.findIndex(cartItem => cartItem.medicationId === item.medicationId);
    
    if (existingIndex !== -1) {
        // Update quantity if item exists
        cart[existingIndex].quantity += item.quantity;
    } else {
        // Add new item
        cart.push({
            ...item,
            id: Date.now() + Math.random() // Unique ID for cart item
        });
    }
    
    updateCart();
}

function removeFromCart(cartItemId) {
    cart = cart.filter(item => item.id !== cartItemId);
    updateCart();
}

function updateCartItemQuantity(cartItemId, quantity) {
    const item = cart.find(item => item.id === cartItemId);
    if (item) {
        if (quantity <= 0) {
            removeFromCart(cartItemId);
        } else if (quantity <= item.stock) {
            item.quantity = quantity;
            updateCart();
        } else {
            alert(`Stock insuffisant. Stock disponible: ${item.stock}`);
        }
    }
}

function updateCartItemInsurance(cartItemId, coveragePercentage) {
    const item = cart.find(item => item.id === cartItemId);
    if (item) {
        item.insuranceCoverage = Math.max(0, Math.min(100, coveragePercentage));
        updateCart();
    }
}

function updateCart() {
    const cartItemsDiv = document.getElementById('cartItems');
    const cartSummaryDiv = document.getElementById('cartSummary');
    const cartItemCount = document.getElementById('cartItemCount');
    
    cartItemCount.textContent = cart.length;
    
    if (cart.length === 0) {
        cartItemsDiv.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                </svg>
                <p>Votre panier est vide</p>
                <p class="text-sm">Recherchez des médicaments pour commencer</p>
            </div>
        `;
        cartSummaryDiv.classList.add('hidden');
        return;
    }
    
    let html = '';
    let subtotal = 0;
    let totalDiscount = 0;
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        const itemDiscount = itemTotal * (item.insuranceCoverage / 100);
        subtotal += itemTotal;
        totalDiscount += itemDiscount;
        
        html += `
            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">${item.name}</h3>
                        <p class="text-sm text-gray-500">${item.form}</p>
                        ${item.fromPrescription ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">Ordonnance</span>' : ''}
                    </div>
                    <button type="button" onclick="removeFromCart(${item.id})" class="btn btn-sm btn-outline btn-error ml-4">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 bg-white p-4 rounded-lg mt-3">
                    <!-- Quantity -->
                    <div>
                        <label class="label">
                            <span class="label-text text-xs font-medium">Quantité</span>
                        </label>
                        <input type="number" 
                               value="${item.quantity}" 
                               min="1" 
                               max="${item.stock}"
                               class="input input-bordered input-sm w-full"
                               onchange="updateCartItemQuantity(${item.id}, parseInt(this.value))">
                        <div class="text-xs text-gray-500 mt-1">Stock: ${item.stock}</div>
                    </div>
                    
                    <!-- Unit Price -->
                    <div>
                        <label class="label">
                            <span class="label-text text-xs font-medium">Prix unitaire</span>
                        </label>
                        <div class="text-sm font-medium text-gray-900">${formatCurrency(item.price)}</div>
                    </div>
                    
                    <!-- Insurance Coverage -->
                    <div>
                        <label class="label">
                            <span class="label-text text-xs font-medium">Assurance (%)</span>
                        </label>
                        <input type="number" 
                               value="${item.insuranceCoverage}" 
                               min="0" 
                               max="100"
                               step="0.1"
                               class="input input-bordered input-sm w-full"
                               onchange="updateCartItemInsurance(${item.id}, parseFloat(this.value))"
                               ${!selectedPatient || !selectedPatient.hasInsurance ? 'disabled' : ''}>
                        ${selectedPatient && selectedPatient.hasInsurance ? 
                            `<div class="text-xs text-blue-600 mt-1">${selectedPatient.insuranceType}</div>` : 
                            '<div class="text-xs text-gray-500 mt-1">Pas d\'assurance</div>'
                        }
                    </div>
                    
                    <!-- Subtotal -->
                    <div>
                        <label class="label">
                            <span class="label-text text-xs font-medium">Sous-total</span>
                        </label>
                        <div class="text-sm font-medium text-gray-900">${formatCurrency(itemTotal)}</div>
                        ${itemDiscount > 0 ? `<div class="text-xs text-green-600">-${formatCurrency(itemDiscount)}</div>` : ''}
                    </div>
                    
                    <!-- Total -->
                    <div>
                        <label class="label">
                            <span class="label-text text-xs font-medium">Total</span>
                        </label>
                        <div class="text-lg font-bold text-primary">${formatCurrency(itemTotal - itemDiscount)}</div>
                    </div>
                </div>
            </div>
        `;
    });
    
    cartItemsDiv.innerHTML = html;
    
    // Update summary
    document.getElementById('subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('insuranceDiscount').textContent = formatCurrency(totalDiscount);
    document.getElementById('totalAmount').textContent = formatCurrency(subtotal - totalDiscount);
    
    cartSummaryDiv.classList.remove('hidden');
    
    // Update process payment button
    updateProcessPaymentButton();
}

function updateProcessPaymentButton() {
    const btn = document.getElementById('processPaymentBtn');
    const paymentMethod = document.getElementById('paymentMethod').value;
    
    btn.disabled = cart.length === 0 || !selectedPatient || !paymentMethod;
}

// Payment method change handler
document.getElementById('paymentMethod').addEventListener('change', updateProcessPaymentButton);

function clearCart() {
    if (confirm('Êtes-vous sûr de vouloir vider le panier ?')) {
        cart = [];
        updateCart();
    }
}

function processPayment() {
    if (!selectedPatient) {
        alert('Veuillez sélectionner un patient.');
        return;
    }
    
    if (cart.length === 0) {
        alert('Le panier est vide.');
        return;
    }
    
    const paymentMethod = document.getElementById('paymentMethod').value;
    if (!paymentMethod) {
        alert('Veuillez sélectionner une méthode de paiement.');
        return;
    }
    
    // Prepare data for submission
    const saleData = {
        patient_id: selectedPatient.id,
        prescription_id: selectedPrescription ? selectedPrescription.id : null,
        payment_method: paymentMethod,
        items: cart.map(item => ({
            medication_id: item.medicationId,
            quantity: item.quantity,
            unit_price: item.price,
            insurance_coverage_percentage: item.insuranceCoverage
        }))
    };
    
    // Submit the sale
    fetch('/pharmacist/process-cart-sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Vente enregistrée avec succès!');
            // Redirect to receipt or reset form
            if (data.receipt_url) {
                window.location.href = data.receipt_url;
            } else if (data.redirect_url) {
                window.location.href = data.redirect_url;
            } else {
                // Reset form
                cart = [];
                selectedPatient = null;
                selectedPrescription = null;
                clearPatientSelection();
                clearPrescriptionSelection();
                updateCart();
                document.getElementById('paymentMethod').value = '';
            }
        } else {
            alert('Erreur: ' + (data.message || 'Une erreur est survenue'));
        }
    })
    .catch(error => {
        console.error('Error processing payment:', error);
        alert('Une erreur est survenue lors du traitement du paiement.');
    });
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount) + ' FCFA';
}
</script>
@endsection