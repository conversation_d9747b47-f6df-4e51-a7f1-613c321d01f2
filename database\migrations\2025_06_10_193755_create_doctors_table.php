<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->constrained()->onDelete('restrict');
            $table->string('specialization');
            $table->string('qualification');
            $table->string('license_number')->unique();
            $table->text('biography')->nullable();
            $table->boolean('is_active')->default(true);
            $table->time('working_hours_start')->nullable();
            $table->time('working_hours_end')->nullable();
            $table->json('working_days')->nullable(); // JSON array of working days
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};