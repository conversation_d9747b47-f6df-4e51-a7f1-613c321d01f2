<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MedicationInsuranceCoverage extends Model
{
    use HasFactory;

    protected $table = 'medication_insurance_coverage';

    protected $fillable = [
        'medication_id',
        'insurance_type',
        'coverage_percentage',
        'minimum_amount',
        'maximum_coverage_amount',
        'valid_from',
        'valid_until',
        'is_active',
        'conditions',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'coverage_percentage' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_coverage_amount' => 'decimal:2',
        'valid_from' => 'date',
        'valid_until' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the medication associated with this coverage.
     */
    public function medication(): BelongsTo
    {
        return $this->belongsTo(Medication::class);
    }

    /**
     * Get the user who created this coverage.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this coverage.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Check if this coverage is currently valid
     */
    public function isCurrentlyValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now()->toDateString();

        // Vérifier la date de début
        if ($this->valid_from && $this->valid_from > $now) {
            return false;
        }

        // Vérifier la date de fin
        if ($this->valid_until && $this->valid_until < $now) {
            return false;
        }

        return true;
    }

    /**
     * Get coverage percentage for a specific medication and insurance type
     */
    public static function getCoverageForMedication(int $medicationId, string $insuranceType): ?float
    {
        $coverage = self::where('medication_id', $medicationId)
                       ->where('insurance_type', $insuranceType)
                       ->where('is_active', true)
                       ->first();

        if (!$coverage || !$coverage->isCurrentlyValid()) {
            return null;
        }

        return $coverage->coverage_percentage;
    }

    /**
     * Calculate insurance discount for a medication
     */
    public function calculateDiscount(float $amount): float
    {
        if (!$this->isCurrentlyValid()) {
            return 0;
        }

        // Vérifier le montant minimum
        if ($this->minimum_amount && $amount < $this->minimum_amount) {
            return 0;
        }

        // Calculer la réduction
        $discount = $amount * ($this->coverage_percentage / 100);

        // Appliquer le plafond maximum si défini
        if ($this->maximum_coverage_amount && $discount > $this->maximum_coverage_amount) {
            $discount = $this->maximum_coverage_amount;
        }

        return $discount;
    }

    /**
     * Get all active coverages for a medication
     */
    public static function getActiveCoveragesForMedication(int $medicationId): array
    {
        return self::where('medication_id', $medicationId)
                  ->where('is_active', true)
                  ->get()
                  ->filter(function ($coverage) {
                      return $coverage->isCurrentlyValid();
                  })
                  ->mapWithKeys(function ($coverage) {
                      return [$coverage->insurance_type => [
                          'percentage' => $coverage->coverage_percentage,
                          'minimum_amount' => $coverage->minimum_amount,
                          'maximum_coverage_amount' => $coverage->maximum_coverage_amount,
                          'conditions' => $coverage->conditions,
                      ]];
                  })
                  ->toArray();
    }

    /**
     * Get all medications covered by a specific insurance type
     */
    public static function getMedicationsCoveredByInsurance(string $insuranceType): array
    {
        return self::where('insurance_type', $insuranceType)
                  ->where('is_active', true)
                  ->with('medication')
                  ->get()
                  ->filter(function ($coverage) {
                      return $coverage->isCurrentlyValid();
                  })
                  ->map(function ($coverage) {
                      return [
                          'medication_id' => $coverage->medication_id,
                          'medication_name' => $coverage->medication->name,
                          'coverage_percentage' => $coverage->coverage_percentage,
                          'minimum_amount' => $coverage->minimum_amount,
                          'maximum_coverage_amount' => $coverage->maximum_coverage_amount,
                      ];
                  })
                  ->toArray();
    }

    /**
     * Scope for active coverages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for currently valid coverages
     */
    public function scopeCurrentlyValid($query)
    {
        $now = now()->toDateString();
        
        return $query->where('is_active', true)
                    ->where(function ($q) use ($now) {
                        $q->whereNull('valid_from')
                          ->orWhere('valid_from', '<=', $now);
                    })
                    ->where(function ($q) use ($now) {
                        $q->whereNull('valid_until')
                          ->orWhere('valid_until', '>=', $now);
                    });
    }
}
