@props(['patient', 'originalAmount', 'type' => 'appointment'])

@php
    $hasValidInsurance = $patient->hasValidInsurance();
    $insuranceDetails = $patient->getInsuranceDetails();
@endphp

<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex items-center justify-between mb-3">
        <h4 class="text-lg font-medium text-blue-900 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
            Calcul d'Assurance
        </h4>
        
        @if($hasValidInsurance)
            <label class="flex items-center">
                <input type="checkbox" 
                       name="apply_insurance" 
                       value="1" 
                       checked
                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                       id="apply_insurance_checkbox">
                <span class="ml-2 text-sm text-blue-700">Appliquer l'assurance</span>
            </label>
        @endif
    </div>

    @if($hasValidInsurance)
        <div id="insurance_calculation" class="space-y-3">
            <!-- Patient Insurance Info -->
            <div class="bg-white rounded p-3 border border-blue-100">
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">Type:</span>
                        <span class="text-gray-900">{{ $insuranceDetails['type'] }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Numéro:</span>
                        <span class="text-gray-900 font-mono">{{ $insuranceDetails['number'] }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Couverture:</span>
                        <span class="text-gray-900">{{ number_format($insuranceDetails['coverage_percentage'], 1) }}%</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Validité:</span>
                        <span class="text-green-600">
                            @if($insuranceDetails['expiry_date'])
                                Jusqu'au {{ $insuranceDetails['expiry_date']->format('d/m/Y') }}
                            @else
                                Valide
                            @endif
                        </span>
                    </div>
                </div>
            </div>

            <!-- Calculation Details -->
            <div class="bg-white rounded p-3 border border-blue-100">
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-700">Montant original:</span>
                        <span class="text-sm text-gray-900" id="original_amount_display">{{ number_format($originalAmount, 2) }} DH</span>
                    </div>
                    
                    <div class="flex justify-between items-center text-green-600">
                        <span class="text-sm font-medium">Réduction d'assurance ({{ number_format($insuranceDetails['coverage_percentage'], 1) }}%):</span>
                        <span class="text-sm font-medium" id="insurance_discount_display">
                            -{{ number_format($originalAmount * ($insuranceDetails['coverage_percentage'] / 100), 2) }} DH
                        </span>
                    </div>
                    
                    <hr class="border-gray-200">
                    
                    <div class="flex justify-between items-center">
                        <span class="text-base font-semibold text-gray-900">Montant à payer:</span>
                        <span class="text-base font-semibold text-blue-600" id="final_amount_display">
                            {{ number_format($originalAmount - ($originalAmount * ($insuranceDetails['coverage_percentage'] / 100)), 2) }} DH
                        </span>
                    </div>
                </div>
            </div>

            <!-- Hidden fields for form submission -->
            <input type="hidden" name="original_amount" value="{{ $originalAmount }}" id="original_amount_input">
            
            @if($type === 'lab')
                <!-- For lab payments, we might need additional fields -->
                <input type="hidden" name="insurance_type" value="{{ $insuranceDetails['type'] }}">
                <input type="hidden" name="insurance_coverage_percentage" value="{{ $insuranceDetails['coverage_percentage'] }}">
            @endif
        </div>

        <!-- No Insurance Applied State -->
        <div id="no_insurance_calculation" class="hidden">
            <div class="bg-white rounded p-3 border border-gray-200">
                <div class="flex justify-between items-center">
                    <span class="text-base font-semibold text-gray-900">Montant à payer:</span>
                    <span class="text-base font-semibold text-gray-900" id="no_insurance_amount_display">
                        {{ number_format($originalAmount, 2) }} DH
                    </span>
                </div>
                <p class="text-sm text-gray-500 mt-1">Aucune assurance appliquée</p>
            </div>
            <input type="hidden" name="original_amount" value="{{ $originalAmount }}">
        </div>

    @else
        <!-- No Valid Insurance -->
        <div class="bg-white rounded p-3 border border-gray-200">
            @if($patient->has_insurance)
                <div class="flex items-center text-amber-600 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <span class="text-sm font-medium">Assurance expirée ou invalide</span>
                </div>
                <p class="text-sm text-gray-600 mb-3">
                    L'assurance {{ $patient->insurance_type }} du patient n'est plus valide.
                    @if($patient->insurance_expiry_date)
                        Expirée le {{ $patient->insurance_expiry_date->format('d/m/Y') }}.
                    @endif
                </p>
            @else
                <div class="flex items-center text-gray-600 mb-2">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm font-medium">Aucune assurance enregistrée</span>
                </div>
                <p class="text-sm text-gray-600 mb-3">Ce patient n'a pas d'assurance enregistrée.</p>
            @endif
            
            <div class="flex justify-between items-center">
                <span class="text-base font-semibold text-gray-900">Montant à payer:</span>
                <span class="text-base font-semibold text-gray-900">{{ number_format($originalAmount, 2) }} DH</span>
            </div>
            
            <input type="hidden" name="original_amount" value="{{ $originalAmount }}">
            <input type="hidden" name="apply_insurance" value="0">
        </div>
    @endif
</div>

@if($hasValidInsurance)
<script>
document.addEventListener('DOMContentLoaded', function() {
    const applyInsuranceCheckbox = document.getElementById('apply_insurance_checkbox');
    const insuranceCalculation = document.getElementById('insurance_calculation');
    const noInsuranceCalculation = document.getElementById('no_insurance_calculation');
    
    if (applyInsuranceCheckbox) {
        applyInsuranceCheckbox.addEventListener('change', function() {
            if (this.checked) {
                insuranceCalculation.classList.remove('hidden');
                noInsuranceCalculation.classList.add('hidden');
            } else {
                insuranceCalculation.classList.add('hidden');
                noInsuranceCalculation.classList.remove('hidden');
            }
        });
    }
});

// Function to update amounts when original amount changes (for dynamic calculations)
function updateInsuranceCalculation(newOriginalAmount) {
    const coveragePercentage = {{ $insuranceDetails['coverage_percentage'] ?? 0 }};
    const discount = newOriginalAmount * (coveragePercentage / 100);
    const finalAmount = newOriginalAmount - discount;
    
    document.getElementById('original_amount_display').textContent = new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(newOriginalAmount) + ' DH';
    
    document.getElementById('insurance_discount_display').textContent = '-' + new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(discount) + ' DH';
    
    document.getElementById('final_amount_display').textContent = new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(finalAmount) + ' DH';
    
    document.getElementById('no_insurance_amount_display').textContent = new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(newOriginalAmount) + ' DH';
    
    // Update hidden input
    document.getElementById('original_amount_input').value = newOriginalAmount;
}
</script>
@endif
