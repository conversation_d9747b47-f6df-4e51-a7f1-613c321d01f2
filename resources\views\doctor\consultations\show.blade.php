@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="container mx-auto px-2">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-3xl font-bold text-primary">
                        Consultation #{{ $appointment->id }}
                    </h2>
                    <div class="mt-2 flex flex-col sm:flex-row sm:flex-wrap gap-2">
                        <div class="badge badge-ghost gap-1">
                            <svg class="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z" clip-rule="evenodd" />
                            </svg>
                            {{ \Carbon\Carbon::parse($appointment->appointment_datetime)->format('d/m/Y à H:i') }}
                        </div>
                        <div class="badge badge-outline gap-1">
                            <svg class="h-5 w-5 text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-5.5-2.5a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zM10 12a5.99 5.99 0 00-4.793 2.39A6.483 6.483 0 0010 16.5a6.483 6.483 0 004.793-2.11A5.99 5.99 0 0010 12z" clip-rule="evenodd" />
                            </svg>
                            {{ $appointment->patient->first_name }} {{ $appointment->patient->last_name }}
                        </div>
                        <div class="badge badge-accent gap-1">
                            <svg class="h-5 w-5 text-accent" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M11 5a3 3 0 11-6 0 3 3 0 016 0zM2.615 16.428a1.224 1.224 0 01-.569-1.175 6.002 6.002 0 0111.908 0c.058.467-.172.92-.57 1.174A9.953 9.953 0 018 18a9.953 9.953 0 01-5.385-1.572zM16.25 5.75a.75.75 0 00-1.5 0v2h-2a.75.75 0 000 1.5h2v2a.75.75 0 001.5 0v-2h2a.75.75 0 000-1.5h-2v-2z" />
                            </svg>
                            {{ $appointment->patient->gender }} - {{ $appointment->patient->getAge() }} ans
                        </div>
                        <div class="badge badge-info gap-1">
                            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z" clip-rule="evenodd" />
                            </svg>
                            {{ $appointment->service->name }}
                        </div>
                    </div>
                </div>
                <div class="flex gap-2">
                    <a href="{{ route('doctor.consultations.index') }}" class="btn btn-outline btn-sm">
                        <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M17 10a.75.75 0 01-.75.75H5.612l4.158 3.96a.75.75 0 11-1.04 1.08l-5.5-5.25a.75.75 0 010-1.08l5.5-5.25a.75.75 0 111.04 1.08L5.612 9.25H16.25A.75.75 0 0117 10z" clip-rule="evenodd" />
                        </svg>
                        Retour
                    </a>
                    @if($appointment->status === \App\Models\Appointment::STATUS_SCHEDULED || $appointment->status === \App\Models\Appointment::STATUS_CONFIRMED)
                        <form action="{{ route('doctor.consultations.update', $appointment) }}" method="POST" class="inline">
                            @csrf
                            @method('PUT')
                            <button type="submit" name="status" value="{{ \App\Models\Appointment::STATUS_IN_PROGRESS }}" class="btn btn-primary btn-sm flex items-center gap-2">
                                <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M2 10a8 8 0 1116 0 8 8 0 01-16 0zm6.39-2.908a.75.75 0 01.766.027l3.5 2.25a.75.75 0 010 1.262l-3.5 2.25A.75.75 0 018 12.25v-4.5a.75.75 0 01.39-.658z" clip-rule="evenodd" />
                                </svg>
                                Démarrer la consultation
                            </button>
                        </form>
                    @elseif($appointment->status === \App\Models\Appointment::STATUS_IN_PROGRESS)
                        <form action="{{ route('doctor.consultations.update', $appointment) }}" method="POST" class="inline" id="completeConsultationForm">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="status" value="{{ \App\Models\Appointment::STATUS_COMPLETED }}">
                            <input type="hidden" name="doctor_notes" id="complete_doctor_notes" value="{{ $appointment->doctor_notes }}">
                            <input type="hidden" name="diagnosis" id="complete_diagnosis" value="{{ $appointment->diagnosis }}">
                            <input type="hidden" name="treatment_plan" id="complete_treatment_plan" value="{{ $appointment->treatment_plan }}">
                            <button type="button" onclick="completeConsultation()" class="btn btn-success btn-sm flex items-center gap-2">
                                <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                </svg>
                                Terminer la consultation
                            </button>
                        </form>
                    @endif
                </div>
            </div>
            
            <div class="mt-4">
                <span class="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium
                    @switch($appointment->status)
                        @case(\App\Models\Appointment::STATUS_COMPLETED) bg-green-100 text-green-800 ring-1 ring-green-600/20 @break
                        @case(\App\Models\Appointment::STATUS_IN_PROGRESS) bg-yellow-100 text-yellow-800 ring-1 ring-yellow-600/20 @break
                        @case(\App\Models\Appointment::STATUS_SCHEDULED) bg-blue-100 text-blue-800 ring-1 ring-blue-600/20 @break
                        @case(\App\Models\Appointment::STATUS_CONFIRMED) bg-indigo-100 text-indigo-800 ring-1 ring-indigo-600/20 @break
                        @case(\App\Models\Appointment::STATUS_CANCELLED) bg-red-100 text-red-800 ring-1 ring-red-600/20 @break
                        @case(\App\Models\Appointment::STATUS_NO_SHOW) bg-gray-100 text-gray-800 ring-1 ring-gray-600/20 @break
                        @default bg-gray-100 text-gray-800 ring-1 ring-gray-600/20
                    @endswitch
                ">
                    <svg class="-ml-1 mr-1.5 h-2 w-2 fill-current" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3" />
                    </svg>
                    {{ $appointment->status_label }}
                </span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Sidebar: Patient Info -->
            <div class="lg:col-span-1 space-y-8">
                <!-- Patient Card -->
                <div class="card bg-base-100 shadow-lg border border-gray-200/80 dark:border-gray-700/80">
                    <div class="card-body p-6">
                        <div class="flex items-center gap-3">
                            <div class="avatar">
                                <div class="w-16 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                    <img src="{{ $appointment->patient->getInitialsAttribute() }}" alt="{{ $appointment->patient->first_name }}" />
                                </div>
                            </div>
                            <div>
                                <h2 class="card-title text-xl font-bold">
                                    {{ $appointment->patient->first_name }} {{ $appointment->patient->last_name }}
                                </h2>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Patient #{{ $appointment->patient->patient_number ?? 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 space-y-2 text-sm">
                            <svg class="h-6 w-6 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-5.5-2.5a2.5 2.5 0 11-5 0 2.5 2.5 0 014.5 0zM10 12a5.99 5.99 0 00-4.793 2.39A6.483 6.483 0 0010 16.5a6.483 6.483 0 004.793-2.11A5.99 5.99 0 0010 12z" clip-rule="evenodd" />
                            </svg>
                            Informations Patient
                        </h3>



                                    <svg class="w-16 h-16 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">{{ $appointment->patient->first_name }} {{ $appointment->patient->last_name }}</h4>


                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2 text-sm mb-4">
                            <div>
                                <span class="font-semibold text-gray-600">Âge</span>
                                <div class="badge badge-info ml-1">{{ $appointment->patient->getAge() }} ans</div>
                            </div>
                            <div>
                                <span class="font-semibold text-gray-600">Sexe</span>
                                <div class="badge badge-secondary ml-1">{{ $appointment->patient->gender }}</div>
                            </div>
                            <div>
                                <span class="font-semibold text-gray-600">Téléphone</span>
                                <div class="badge badge-outline ml-1">{{ $appointment->patient->phone_number }}</div>
                            </div>
                            <div>
                                <span class="font-semibold text-gray-600">Groupe sanguin</span>
                                <div class="badge badge-outline ml-1">{{ $appointment->patient->blood_group ?: 'Non renseigné' }}</div>
                            </div>
                        </div>
                        @if($appointment->patient->allergies)
                        <div class="alert alert-error py-2 rounded-lg flex items-center gap-2">
                            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                            </svg>
                            <span class="font-semibold">Allergies :</span>
                            <span>{{ $appointment->patient->allergies }}</span>
                        </div>
                        @endif
                        @if($appointment->patient->medical_history)
                        <div class="alert alert-info py-2 rounded-lg flex items-center gap-2 mt-2">
                            <svg class="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.25 2A2.25 2.25 0 002 4.25v11.5A2.25 2.25 0 004.25 18h11.5A2.25 2.25 0 0018 15.75V4.25A2.25 2.25 0 0015.75 2H4.25zm4.03 6.28a.75.75 0 00-1.06-1.06L6 8.44l-1.22-1.22a.75.75 0 00-1.06 1.06L5.44 10l-1.72 1.72a.75.75 0 101.06 1.06L6 11.56l1.22 1.22a.75.75 0 001.06-1.06L6.56 10l1.72-1.72z" clip-rule="evenodd" />
                            </svg>
                            <span class="font-semibold">Antécédents médicaux :</span>
                            <span>{{ $appointment->patient->medical_history }}</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Main Content: Consultation Form -->
            <div class="lg:col-span-2">
                <!-- Status Guide -->
                @if($appointment->status === \App\Models\Appointment::STATUS_IN_PROGRESS)
                <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Consultation en cours</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>1. Remplissez vos observations cliniques et le diagnostic</p>
                                <p>2. Cliquez sur <strong>"Enregistrer la consultation"</strong> pour sauvegarder</p>
                                <p>3. Créez une ordonnance si nécessaire</p>
                                <p>4. Cliquez sur <strong>"🏁 Terminer la consultation"</strong> pour finaliser</p>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                
                <!-- Consultation Form -->
                <div class="bg-white shadow-sm rounded-xl border border-gray-200 mb-6">
                    <div class="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4">
                        <h3 class="text-lg font-semibold text-white flex items-center">
                            <svg class="mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M2.5 3A1.5 1.5 0 001 4.5v.793c.**************.076.032L7.674 8.51c.206.1.446.1.652 0l6.598-3.185A.755.755 0 0015 5.293V4.5A1.5 1.5 0 0013.5 3h-11z" clip-rule="evenodd" />
                                <path fill-rule="evenodd" d="M15 6.954L8.978 9.86a2.25 2.25 0 01-1.956 0L1 6.954V11.5A1.5 1.5 0 002.5 13h11a1.5 1.5 0 001.5-1.5V6.954z" clip-rule="evenodd" />
                            </svg>
                            Dossier de Consultation
                        </h3>
                    </div>
                    <div class="p-6">
                        @if($appointment->status === \App\Models\Appointment::STATUS_COMPLETED)
                            <!-- Consultation terminée - Mode lecture seule -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 text-green-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    <h4 class="text-sm font-medium text-green-800">Consultation terminée</h4>
                                </div>
                                <p class="mt-1 text-sm text-green-700">Cette consultation a été finalisée et ne peut plus être modifiée.</p>
                            </div>
                            
                            <!-- Motif de consultation -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <label class="block text-sm font-semibold text-gray-700 mb-2">Motif de consultation</label>
                                <p class="text-gray-900 bg-white p-3 rounded border">{{ $appointment->reason ?: 'Non spécifié' }}</p>
                            </div>
                            
                            <!-- Observations cliniques -->
                            <div class="mb-4">
                                <label class="block text-sm font-semibold text-gray-700 mb-2">
                                    <svg class="inline w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.25 2A2.25 2.25 0 002 4.25v11.5A2.25 2.25 0 004.25 18h11.5A2.25 2.25 0 0018 15.75V4.25A2.25 2.25 0 0015.75 2H4.25zm7.5 5.25a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm1.5 5.25a.75.75 0 00-.75-.75h-6a.75.75 0 000 1.5h6a.75.75 0 00.75-.75z" clip-rule="evenodd" />
                                    </svg>
                                    Observations Cliniques
                                </label>
                                <div class="bg-white p-3 rounded border text-gray-900">{{ $appointment->doctor_notes ?: 'Aucune observation enregistrée' }}</div>
                            </div>
                            
                            <!-- Diagnostic -->
                            <div class="mb-4">
                                <label class="block text-sm font-semibold text-gray-700 mb-2">
                                    <svg class="inline w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.107 10.5a.75.75 0 00-1.214.882l1.5 2.25a.75.75 0 001.214 0l4-5.5z" clip-rule="evenodd" />
                                    </svg>
                                    Diagnostic
                                </label>
                                <div class="bg-white p-3 rounded border text-gray-900">{{ $appointment->diagnosis ?: 'Aucun diagnostic enregistré' }}</div>
                            </div>
                            
                            <!-- Plan de traitement -->
                            <div class="mb-4">
                                <label class="block text-sm font-semibold text-gray-700 mb-2">
                                    <svg class="inline w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />
                                    </svg>
                                    Plan de Traitement
                                </label>
                                <div class="bg-white p-3 rounded border text-gray-900">{{ $appointment->treatment_plan ?: 'Aucun plan de traitement enregistré' }}</div>
                            </div>
                        @else
                            <!-- Consultation en cours - Mode édition -->
                            <form action="{{ route('doctor.consultations.update', $appointment) }}" method="POST" class="space-y-6">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="status" value="{{ $appointment->status }}">
                                
                                <!-- Motif de consultation -->
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Motif de consultation</label>
                                    <p class="text-gray-900 bg-white p-3 rounded border">{{ $appointment->reason ?: 'Non spécifié' }}</p>
                                </div>
                                
                                <!-- Observations cliniques -->
                                <div>
                                    <label for="doctor_notes" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <svg class="inline w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4.25 2A2.25 2.25 0 002 4.25v11.5A2.25 2.25 0 004.25 18h11.5A2.25 2.25 0 0018 15.75V4.25A2.25 2.25 0 0015.75 2H4.25zm7.5 5.25a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm1.5 5.25a.75.75 0 00-.75-.75h-6a.75.75 0 000 1.5h6a.75.75 0 00.75-.75z" clip-rule="evenodd" />
                                        </svg>
                                        Observations Cliniques
                                    </label>
                                    <textarea id="doctor_notes" name="doctor_notes" rows="5" 
                                        class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 text-sm"
                                        placeholder="Décrivez vos observations cliniques, l'examen physique, les symptômes observés...">{{ old('doctor_notes', $appointment->doctor_notes) }}</textarea>
                                </div>
                                
                                <!-- Diagnostic -->
                                <div>
                                    <label for="diagnosis" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <svg class="inline w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.107 10.5a.75.75 0 00-1.214.882l1.5 2.25a.75.75 0 001.214 0l4-5.5z" clip-rule="evenodd" />
                                        </svg>
                                        Diagnostic
                                    </label>
                                    <textarea id="diagnosis" name="diagnosis" rows="3" 
                                        class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 text-sm"
                                        placeholder="Établissez votre diagnostic principal et les diagnostics différentiels...">{{ old('diagnosis', $appointment->diagnosis) }}</textarea>
                                    @error('diagnosis')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                
                                <!-- Plan de traitement -->
                                <div>
                                    <label for="treatment_plan" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <svg class="inline w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />
                                        </svg>
                                        Plan de Traitement
                                    </label>
                                    <textarea id="treatment_plan" name="treatment_plan" rows="4" 
                                        class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 text-sm"
                                        placeholder="Décrivez le plan de traitement, les recommandations, le suivi...">{{ old('treatment_plan', $appointment->treatment_plan) }}</textarea>
                                </div>
                                
                                <div class="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3">
                                    <button type="submit" class="inline-flex items-center justify-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-sm text-white hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        <svg class="-ml-1 mr-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                        </svg>
                                        Enregistrer la consultation
                                    </button>
                                    @if($appointment->status === \App\Models\Appointment::STATUS_IN_PROGRESS)
                                    <button type="button" onclick="completeConsultation()" class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 border border-transparent rounded-lg font-bold text-base text-white hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150 shadow-lg">
                                        <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        🏁 Terminer la consultation
                                    </button>
                                    @endif
                                </div>
                            </form>
                        @endif
                    </div>
                </div>

                <!-- Prescription Section -->
                <div class="bg-white shadow-sm rounded-xl border border-gray-200 mb-6">
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-white flex items-center">
                                <svg class="mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.25 2A2.25 2.25 0 002 4.25v11.5A2.25 2.25 0 004.25 18h11.5A2.25 2.25 0 0018 15.75V4.25A2.25 2.25 0 0015.75 2H4.25zm7.5 5.25a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm1.5 5.25a.75.75 0 00-.75-.75h-6a.75.75 0 000 1.5h6a.75.75 0 00.75-.75z" clip-rule="evenodd" />
                                </svg>
                                Ordonnances Médicales
                            </h3>
                            @if($appointment->status !== \App\Models\Appointment::STATUS_COMPLETED)
                            <button type="button" onclick="togglePrescriptionForm()" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg font-semibold text-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition duration-150">
                                <svg class="-ml-1 mr-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                                </svg>
                                Nouvelle ordonnance
                            </button>
                            @endif
                        </div>
                    </div>
                    
                    <!-- Prescription Form -->
                    @if($appointment->status !== \App\Models\Appointment::STATUS_COMPLETED)
                    <div id="prescriptionForm" class="border-t border-gray-200 p-6 hidden slide-down">
                        <form action="{{ route('doctor.consultations.prescriptions', $appointment) }}" method="POST" class="space-y-6">
                            @csrf
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label for="prescription_diagnosis" class="block text-sm font-semibold text-gray-700 mb-2">Diagnostic pour l'ordonnance</label>
                                    <textarea id="prescription_diagnosis" name="diagnosis" rows="3"
                                        class="block w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-white transition-all duration-200 placeholder-gray-500 resize-none"
                                        placeholder="Diagnostic principal...">{{ $appointment->diagnosis }}</textarea>
                                </div>
                                
                                <div>
                                    <label for="prescription_notes" class="block text-sm font-semibold text-gray-700 mb-2">Instructions générales</label>
                                    <textarea id="prescription_notes" name="notes" rows="3"
                                        class="block w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-white transition-all duration-200 placeholder-gray-500 resize-none"
                                        placeholder="Instructions générales pour le patient..."></textarea>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between items-center mb-4">
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Médicaments prescrits</label>
                                    <button type="button" onclick="addMedicationRow()" class="inline-flex items-center px-3 py-2 bg-purple-100 text-purple-700 rounded-lg font-medium text-sm hover:bg-purple-200 transition duration-150">
                                        <svg class="-ml-1 mr-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                                        </svg>
                                        Ajouter médicament
                                    </button>
                                </div>
                                
                                <div id="medications-container" class="space-y-4">
                                    <div class="medication-row bg-gray-50 border border-gray-200 rounded-lg p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
                                            <div class="md:col-span-4">
                                                <label class="block text-xs font-semibold text-gray-700 mb-1">Nom du médicament <span class="text-red-500">*</span></label>
                                                <input type="text" name="medications[0][name]" required
                                                    class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                                                    placeholder="ex: Paracétamol">
                                            </div>
                                            <div class="md:col-span-2">
                                                <label class="block text-xs font-semibold text-gray-700 mb-1">Forme <span class="text-red-500">*</span></label>
                                                <select name="medications[0][form]" required class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm">
                                                    <option value="">Sélectionner</option>
                                                    <option value="Comprimé">Comprimé</option>
                                                    <option value="Gélule">Gélule</option>
                                                    <option value="Sirop">Sirop</option>
                                                    <option value="Injection">Injection</option>
                                                    <option value="Pommade">Pommade</option>
                                                    <option value="Gouttes">Gouttes</option>
                                                    <option value="Suppositoire">Suppositoire</option>
                                                    <option value="Autre">Autre</option>
                                                </select>
                                            </div>
                                            <div class="md:col-span-2">
                                                <label class="block text-xs font-semibold text-gray-700 mb-1">Dosage <span class="text-red-500">*</span></label>
                                                <input type="text" name="medications[0][dosage]" required
                                                    class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                                                    placeholder="ex: 500mg">
                                            </div>
                                            <div class="md:col-span-2">
                                                <label class="block text-xs font-semibold text-gray-700 mb-1">Fréquence <span class="text-red-500">*</span></label>
                                                <input type="text" name="medications[0][frequency]" required
                                                    class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                                                    placeholder="ex: 3x/jour">
                                            </div>
                                            <div class="md:col-span-1">
                                                <label class="block text-xs font-semibold text-gray-700 mb-1">Durée <span class="text-red-500">*</span></label>
                                                <input type="text" name="medications[0][duration]" required
                                                    class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                                                    placeholder="7 jours">
                                            </div>
                                            <div class="md:col-span-1 flex items-end">
                                                <button type="button" onclick="removeMedicationRow(this)" class="w-full flex justify-center items-center px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition duration-150">
                                                    <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <label class="block text-xs font-semibold text-gray-700 mb-1">Instructions spéciales</label>
                                            <input type="text" name="medications[0][instructions]"
                                                class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                                                placeholder="ex: Prendre après les repas, avec un grand verre d'eau">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200 bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-xl">
                                <button type="button" onclick="togglePrescriptionForm()" class="inline-flex items-center justify-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-semibold text-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-150">
                                    <svg class="-ml-1 mr-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.28 4.28a.75.75 0 00-1.06 1.06L8.94 11l-5.72 5.72a.75.75 0 101.06 1.06L10 12.06l5.72 5.72a.75.75 0 101.06-1.06L11.06 11l5.72-5.72a.75.75 0 00-1.06-1.06L10 9.94 4.28 4.28z" clip-rule="evenodd" />
                                    </svg>
                                    Annuler
                                </button>
                                <button type="submit" class="inline-flex items-center justify-center px-6 py-3 bg-purple-600 text-white rounded-lg font-bold text-base hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition duration-150 shadow-lg">
                                    <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                    </svg>
                                    Enregistrer l'ordonnance
                                </button>
                            </div>
                        </form>
                    </div>
                    @endif
                    
                    <!-- Prescriptions List -->
                    <div class="p-6">
                        @if($appointment->prescriptions->count() > 0)
                            <div class="space-y-4">
                                @foreach($appointment->prescriptions as $prescription)
                                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <div>
                                                <h4 class="text-lg font-semibold text-purple-900">Ordonnance #{{ $prescription->prescription_number }}</h4>
                                                <p class="text-sm text-purple-700">{{ $prescription->prescription_date->format('d/m/Y à H:i') }}</p>
                                            </div>
                                            <div class="flex space-x-2">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    @if($prescription->status === 'active') bg-green-100 text-green-800
                                                    @elseif($prescription->status === 'dispensed') bg-blue-100 text-blue-800
                                                    @else bg-gray-100 text-gray-800
                                                    @endif">
                                                    @if($prescription->status === 'active') Active
                                                    @elseif($prescription->status === 'dispensed') Délivrée
                                                    @else {{ ucfirst($prescription->status) }}
                                                    @endif
                                                </span>
                                                <a href="{{ route('doctor.prescriptions.pdf', $prescription) }}" target="_blank" 
                                                   class="inline-flex items-center px-3 py-1 bg-purple-600 text-white rounded-md text-xs font-medium hover:bg-purple-700 transition duration-150">
                                                    <svg class="-ml-1 mr-1 h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M4.25 2A2.25 2.25 0 002 4.25v11.5A2.25 2.25 0 004.25 18h11.5A2.25 2.25 0 0018 15.75V4.25A2.25 2.25 0 0015.75 2H4.25zm4.03 6.28a.75.75 0 00-1.06-1.06L6 8.44l-1.22-1.22a.75.75 0 00-1.06 1.06L5.44 10l-1.72 1.72a.75.75 0 101.06 1.06L6 11.56l1.22 1.22a.75.75 0 001.06-1.06L6.56 10l1.72-1.72z" clip-rule="evenodd" />
                                                    </svg>
                                                    PDF
                                                </a>
                                            </div>
                                        </div>
                                        
                                        @if($prescription->diagnosis)
                                        <div class="mb-3">
                                            <h5 class="text-sm font-semibold text-gray-700">Diagnostic:</h5>
                                            <p class="text-sm text-gray-600">{{ $prescription->diagnosis }}</p>
                                        </div>
                                        @endif
                                        
                                        <div class="mb-3">
                                            <h5 class="text-sm font-semibold text-gray-700">Médicaments prescrits:</h5>
                                            <div class="mt-2 space-y-2">
                                                @foreach($prescription->prescriptionItems as $item)
                                                    <div class="bg-white border border-gray-200 rounded-md p-3">
                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <p class="font-medium text-gray-900">{{ $item->medication_name ?? $item->medication->name }}</p>
                                                                <p class="text-sm text-gray-600">
                                                                    {{ $item->medication_form ?? $item->medication->form ?? '' }} - 
                                                                    {{ $item->dosage }}, {{ $item->frequency }}, {{ $item->duration }}
                                                                </p>
                                                                @if($item->instructions)
                                                                    <p class="text-xs text-gray-500 italic">{{ $item->instructions }}</p>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                        
                                        @if($prescription->notes)
                                        <div>
                                            <h5 class="text-sm font-semibold text-gray-700">Instructions générales:</h5>
                                            <p class="text-sm text-gray-600">{{ $prescription->notes }}</p>
                                        </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.25 2A2.25 2.25 0 002 4.25v11.5A2.25 2.25 0 004.25 18h11.5A2.25 2.25 0 0018 15.75V4.25A2.25 2.25 0 0015.75 2H4.25zm7.5 5.25a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm1.5 5.25a.75.75 0 00-.75-.75h-6a.75.75 0 000 1.5h6a.75.75 0 00.75-.75z" clip-rule="evenodd" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune ordonnance</h3>
                                <p class="mt-1 text-sm text-gray-500">Aucune ordonnance n'a été créée pour cette consultation.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Lab Tests Section -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 mb-6 overflow-hidden">
                <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-white flex items-center">
                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                            </svg>
                            Prescription d'Analyses de Laboratoire
                        </h3>
                        @if($appointment->status !== \App\Models\Appointment::STATUS_COMPLETED)
                        <button type="button" onclick="toggleLabTestForm()" class="btn btn-outline btn-info btn-sm gap-2">
                            <svg class="-ml-1 mr-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                            </svg>
                            Nouvelle prescription
                        </button>
                        @endif
                    </div>
                </div>
                
                <!-- Lab Test Form -->
                @if($appointment->status !== \App\Models\Appointment::STATUS_COMPLETED)
                <div id="labTestForm" class="border-t border-gray-200 p-6 hidden slide-down">
                    <form action="{{ route('doctor.consultations.lab-tests', $appointment) }}" method="POST" class="space-y-6" id="labTestsForm">
                        @csrf

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Informations cliniques -->
                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                                <label for="clinical_information" class="block text-sm font-semibold text-gray-700 mb-2">
                                    <div class="flex items-center">
                                        <svg class="mr-2 h-5 w-5 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
                                        </svg>
                                        Informations cliniques <span class="text-red-500">*</span>
                                    </div>
                                </label>
                                <textarea id="clinical_information" name="clinical_information" rows="3" required
                                    class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm @error('clinical_information') border-red-500 @enderror"
                                    placeholder="Précisez les symptômes, antécédents ou raisons justifiant les analyses...">{{ old('clinical_information') }}</textarea>
                                @error('clinical_information')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Urgence et notes -->
                            <div class="space-y-4">
                                <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-4 border border-amber-200">
                                    <label for="urgency" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <div class="flex items-center">
                                            <svg class="mr-2 h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                            </svg>
                                            Niveau d'urgence
                                        </div>
                                    </label>
                                    <select id="urgency" name="urgency" class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-amber-500 focus:ring-amber-500 text-sm">
                                        <option value="normal">Normal</option>
                                        <option value="urgent">Urgent</option>
                                        <option value="stat">STAT (Urgence absolue)</option>
                                    </select>
                                </div>

                                <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
                                    <label for="lab_notes" class="block text-sm font-semibold text-gray-700 mb-2">
                                        <div class="flex items-center">
                                            <svg class="mr-2 h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                                            </svg>
                                            Notes complémentaires
                                        </div>
                                    </label>
                                    <textarea id="lab_notes" name="notes" rows="2"
                                        class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 text-sm"
                                        placeholder="Instructions spéciales, conditions de prélèvement..."></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-4 flex items-center">
                                <svg class="mr-2 h-5 w-5 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2 4.5A2.5 2.5 0 014.5 2h11a2.5 2.5 0 010 5h-11A2.5 2.5 0 012 4.5zM2.75 9.083a.75.75 0 000 1.5h14.5a.75.75 0 000-1.5H2.75zM2.75 12.663a.75.75 0 000 1.5h14.5a.75.75 0 000-1.5H2.75zM2.75 16.25a.75.75 0 000 1.5h14.5a.75.75 0 100-1.5H2.75z" />
                                </svg>
                                Sélectionnez les analyses à prescrire
                            </label>
                            
                            <!-- Analyses par catégories -->
                            <div class="space-y-6">
                                @foreach($labTests as $category => $tests)
                                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                        <!-- En-tête de catégorie -->
                                        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 px-4 py-3">
                                            <div class="flex items-center justify-between">
                                                <h4 class="text-lg font-semibold text-white flex items-center">
                                                    @switch($category)
                                                        @case('Hématologie')
                                                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
                                                            </svg>
                                                            @break
                                                        @case('Biochimie')
                                                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.611L5 14.5" />
                                                            </svg>
                                                            @break
                                                        @case('Urologie')
                                                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.611L5 14.5" />
                                                            </svg>
                                                            @break
                                                        @case('Parasitologie')
                                                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75-7.478v-1.5m0 1.5a6.01 6.01 0 001.5-.189M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                                                            </svg>
                                                            @break
                                                        @case('Immunologie')
                                                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                                                            </svg>
                                                            @break
                                                        @case('Hormonologie')
                                                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                                            </svg>
                                                            @break
                                                        @default
                                                            <svg class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.611L5 14.5" />
                                                            </svg>
                                                    @endswitch
                                                    {{ $category }}
                                                </h4>
                                                <span class="bg-white bg-opacity-20 text-white text-xs font-medium px-2 py-1 rounded-full">
                                                    {{ $tests->count() }} analyse(s)
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Liste des analyses de la catégorie -->
                                        <div class="p-4">
                                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                                @foreach($tests as $test)
                                                    <div class="bg-gray-50 p-3 rounded-lg border border-gray-200 hover:border-indigo-300 hover:shadow-md transition-all duration-200 relative group">
                                                        <div class="absolute right-2 top-2">
                                                            <input id="test-{{ $test->id }}" name="lab_tests[]" type="checkbox" value="{{ $test->id }}"
                                                                class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                                        </div>
                                                        <label for="test-{{ $test->id }}" class="block cursor-pointer">
                                                            <div class="pr-6">
                                                                <h5 class="font-medium text-gray-900 text-sm mb-1">{{ $test->name }}</h5>
                                                                <p class="text-xs text-gray-600 mb-2">{{ $test->test_code }}</p>
                                                                @if($test->price)
                                                                    <p class="text-xs font-semibold text-indigo-600">{{ number_format($test->price, 0, ',', ' ') }} FCFA</p>
                                                                @endif
                                                                @if($test->description)
                                                                    <p class="text-xs text-gray-500 mt-1 line-clamp-2">{{ $test->description }}</p>
                                                                @endif
                                                            </div>
                                                        </label>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        
                        <div class="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200 bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-xl">
                            <button type="button" onclick="toggleLabTestForm()" class="inline-flex items-center justify-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg font-semibold text-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition duration-150">
                                Annuler
                            </button>
                            <button type="submit" class="inline-flex items-center justify-center px-6 py-3 bg-indigo-600 text-white rounded-lg font-bold text-base hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition duration-150 shadow-lg">
                                <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                                </svg>
                                Valider la prescription
                            </button>
                        </div>
                    </form>
                </div>
                @endif
                
                <!-- Lab Tests List -->
                <div class="p-6">
                    @if($appointment->labResults->count() > 0)
                        @php
                            // Grouper les résultats par numéro de prescription
                            $groupedResults = $appointment->labResults->groupBy('prescription_number');
                        @endphp

                        <div class="space-y-6">
                            @foreach($groupedResults as $prescriptionNumber => $results)
                                <div class="bg-gradient-to-br from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl p-6 shadow-sm">
                                    <!-- En-tête de prescription -->
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <div class="bg-indigo-600 rounded-full p-3 mr-4">
                                                <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-bold text-indigo-900">
                                                    @if($prescriptionNumber)
                                                        Prescription {{ $prescriptionNumber }}
                                                    @else
                                                        Analyses individuelles
                                                    @endif
                                                </h3>
                                                <p class="text-sm text-indigo-700">
                                                    Prescrite le {{ $results->first()->ordered_at->format('d/m/Y à H:i') }} •
                                                    {{ $results->count() }} analyse(s)
                                                </p>
                                            </div>
                                        </div>

                                        <div class="flex items-center space-x-3">
                                            <!-- Badge d'urgence -->
                                            @if($results->first()->urgency !== 'normal')
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold
                                                    @if($results->first()->urgency === 'urgent') bg-orange-100 text-orange-800 ring-1 ring-orange-600/20
                                                    @elseif($results->first()->urgency === 'stat') bg-red-100 text-red-800 ring-1 ring-red-600/20
                                                    @endif">
                                                    {{ strtoupper($results->first()->urgency) }}
                                                </span>
                                            @endif

                                            <!-- Bouton PDF -->
                                            @if($prescriptionNumber)
                                                <a href="{{ route('doctor.consultations.lab-tests.pdf', $appointment) }}?prescription_number={{ $prescriptionNumber }}"
                                                   target="_blank"
                                                   class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg font-semibold text-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition duration-150 shadow-md">
                                                    <svg class="-ml-1 mr-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M4.25 2A2.25 2.25 0 002 4.25v11.5A2.25 2.25 0 004.25 18h11.5A2.25 2.25 0 0018 15.75V4.25A2.25 2.25 0 0015.75 2H4.25zm7.5 5.25a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm1.5 5.25a.75.75 0 00-.75-.75h-6a.75.75 0 000 1.5h6a.75.75 0 00.75-.75z" clip-rule="evenodd" />
                                                    </svg>
                                                    Télécharger PDF
                                                </a>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Informations cliniques -->
                                    @if($results->first()->clinical_information)
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                                            <h5 class="text-sm font-semibold text-blue-900 mb-1">Informations cliniques:</h5>
                                            <p class="text-sm text-blue-800">{{ $results->first()->clinical_information }}</p>
                                        </div>
                                    @endif

                                    <!-- Liste des analyses -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        @foreach($results as $result)
                                            <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="font-semibold text-gray-900">{{ $result->labTest->name }}</h4>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                        @if($result->status === 'ordered') bg-amber-100 text-amber-800
                                                        @elseif($result->status === 'sample_collected') bg-blue-100 text-blue-800
                                                        @elseif($result->status === 'processing') bg-purple-100 text-purple-800
                                                        @elseif($result->status === 'completed') bg-green-100 text-green-800
                                                        @else bg-gray-100 text-gray-800
                                                        @endif">
                                                        @switch($result->status)
                                                            @case('ordered') Prescrite @break
                                                            @case('sample_collected') Échantillon collecté @break
                                                            @case('processing') En cours @break
                                                            @case('completed') Terminée @break
                                                            @default {{ ucfirst($result->status) }}
                                                        @endswitch
                                                    </span>
                                                </div>

                                                <div class="text-sm text-gray-600 space-y-1">
                                                    <div><span class="font-medium">Code:</span> {{ $result->labTest->test_code }}</div>
                                                    @if($result->labTest->price)
                                                        <div><span class="font-medium">Prix:</span> {{ number_format($result->labTest->price, 0, ',', ' ') }} FCFA</div>
                                                    @endif
                                                    <div><span class="font-medium">N° Analyse:</span> {{ $result->result_number }}</div>
                                                </div>

                                                @if($result->labTest->description)
                                                    <p class="text-xs text-gray-500 mt-2 italic">{{ $result->labTest->description }}</p>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>

                                    <!-- Notes complémentaires -->
                                    @if($results->first()->notes)
                                        <div class="bg-green-50 border border-green-200 rounded-lg p-3 mt-4">
                                            <h5 class="text-sm font-semibold text-green-900 mb-1">Notes complémentaires:</h5>
                                            <p class="text-sm text-green-800">{{ $results->first()->notes }}</p>
                                        </div>
                                    @endif

                                    <!-- Total estimé -->
                                    @php
                                        $totalPrice = $results->sum(function($result) { return $result->labTest->price ?? 0; });
                                    @endphp
                                    @if($totalPrice > 0)
                                        <div class="mt-4 pt-4 border-t border-indigo-200">
                                            <div class="flex justify-between items-center">
                                                <span class="text-sm font-medium text-indigo-900">Total estimé:</span>
                                                <span class="text-lg font-bold text-indigo-900">{{ number_format($totalPrice, 0, ',', ' ') }} FCFA</span>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                            <svg class="mx-auto h-12 w-12 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune analyse prescrite</h3>
                            <p class="mt-1 text-sm text-gray-500">Aucune analyse de laboratoire n'a été prescrite pour cette consultation.</p>
                            @if($appointment->status !== \App\Models\Appointment::STATUS_COMPLETED)
                            <div class="mt-6">
                                <button type="button" onclick="toggleLabTestForm()" class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                                    </svg>
                                    Prescrire des analyses
                                </button>
                            </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle lab test form
    function toggleLabTestForm() {
        const form = document.getElementById('labTestForm');
        if (form.classList.contains('hidden')) {
            form.classList.remove('hidden');
            form.classList.add('fade-in');
        } else {
            form.classList.add('hidden');
            form.classList.remove('fade-in');
        }
    }

    // Add the new function to the existing DOMContentLoaded event listener
    document.addEventListener('DOMContentLoaded', function() {
        // ... existing code ...
        
        // Validation du formulaire d'analyses de laboratoire
        const labTestsForm = document.getElementById('labTestsForm');
        if (labTestsForm) {
            labTestsForm.addEventListener('submit', function(e) {
                // Vérifier qu'au moins une analyse est sélectionnée
                const selectedTests = document.querySelectorAll('input[name="lab_tests[]"]:checked');
                if (selectedTests.length === 0) {
                    e.preventDefault();
                    alert('Veuillez sélectionner au moins une analyse de laboratoire.');
                    return false;
                }
                
                // Vérifier que les informations cliniques sont renseignées
                const clinicalInfo = document.getElementById('clinical_information').value.trim();
                if (!clinicalInfo) {
                    e.preventDefault();
                    alert('Veuillez renseigner les informations cliniques.');
                    document.getElementById('clinical_information').focus();
                    return false;
                }
            });
        }
    });
</script>

<script>
    // Toggle prescription form with animation
    function togglePrescriptionForm() {
        const form = document.getElementById('prescriptionForm');
        if (form.classList.contains('hidden')) {
            form.classList.remove('hidden');
            form.classList.add('fade-in');
        } else {
            form.classList.add('hidden');
            form.classList.remove('fade-in');
        }
    }
    
    // Complete consultation function
    function completeConsultation() {
        // Vérifier que les champs obligatoires sont remplis
        const doctorNotes = document.getElementById('doctor_notes').value.trim();
        const diagnosis = document.getElementById('diagnosis').value.trim();
        const treatmentPlan = document.getElementById('treatment_plan').value.trim();
        
        if (!diagnosis) {
            alert('Veuillez saisir un diagnostic avant de terminer la consultation.');
            document.getElementById('diagnosis').focus();
            return;
        }
        
        if (!doctorNotes) {
            alert('Veuillez saisir vos observations cliniques avant de terminer la consultation.');
            document.getElementById('doctor_notes').focus();
            return;
        }
        
        // Confirmer l'action
        if (confirm('Êtes-vous sûr de vouloir terminer cette consultation ? Cette action ne peut pas être annulée.')) {
            // Mettre à jour les champs cachés du formulaire de completion
            document.getElementById('complete_doctor_notes').value = doctorNotes;
            document.getElementById('complete_diagnosis').value = diagnosis;
            document.getElementById('complete_treatment_plan').value = treatmentPlan;
            
            // Soumettre le formulaire
            document.getElementById('completeConsultationForm').submit();
        }
    }
    
    // Medication rows management
    let medicationRowCount = 1;
    
    function addMedicationRow() {
        const container = document.getElementById('medications-container');
        const newRow = document.createElement('div');
        newRow.className = 'medication-row bg-gray-50 border border-gray-200 rounded-lg p-4';
        
        newRow.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
                <div class="md:col-span-4">
                    <label class="block text-xs font-semibold text-gray-700 mb-1">Nom du médicament <span class="text-red-500">*</span></label>
                    <input type="text" name="medications[${medicationRowCount}][name]" required
                        class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                        placeholder="ex: Paracétamol">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-xs font-semibold text-gray-700 mb-1">Forme <span class="text-red-500">*</span></label>
                    <select name="medications[${medicationRowCount}][form]" required class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm">
                        <option value="">Sélectionner</option>
                        <option value="Comprimé">Comprimé</option>
                        <option value="Gélule">Gélule</option>
                        <option value="Sirop">Sirop</option>
                        <option value="Injection">Injection</option>
                        <option value="Pommade">Pommade</option>
                        <option value="Gouttes">Gouttes</option>
                        <option value="Suppositoire">Suppositoire</option>
                        <option value="Autre">Autre</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-xs font-semibold text-gray-700 mb-1">Dosage <span class="text-red-500">*</span></label>
                    <input type="text" name="medications[${medicationRowCount}][dosage]" required
                        class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                        placeholder="ex: 500mg">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-xs font-semibold text-gray-700 mb-1">Fréquence <span class="text-red-500">*</span></label>
                    <input type="text" name="medications[${medicationRowCount}][frequency]" required
                        class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                        placeholder="ex: 3x/jour">
                </div>
                <div class="md:col-span-1">
                    <label class="block text-xs font-semibold text-gray-700 mb-1">Durée <span class="text-red-500">*</span></label>
                    <input type="text" name="medications[${medicationRowCount}][duration]" required
                        class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                        placeholder="7 jours">
                </div>
                <div class="md:col-span-1 flex items-end">
                    <button type="button" onclick="removeMedicationRow(this)" class="w-full flex justify-center items-center px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition duration-150">
                        <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <label class="block text-xs font-semibold text-gray-700 mb-1">Instructions spéciales</label>
                <input type="text" name="medications[${medicationRowCount}][instructions]"
                    class="block w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-sm"
                    placeholder="ex: Prendre après les repas, avec un grand verre d'eau">
            </div>
        `;
        
        container.appendChild(newRow);
        newRow.classList.add('fade-in');
        medicationRowCount++;
    }
    
    function removeMedicationRow(button) {
        const row = button.closest('.medication-row');
        if (document.querySelectorAll('.medication-row').length > 1) {
            row.style.opacity = '0';
            row.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                row.remove();
            }, 300);
        } else {
            // Show alert if trying to remove the last row
            alert('Vous devez avoir au moins un médicament dans l\'ordonnance.');
        }
    }
    
    // Form validation and auto-sync
    document.addEventListener('DOMContentLoaded', function() {
        // Prescription form validation
        const prescriptionForm = document.querySelector('#prescriptionForm form');
        if (prescriptionForm) {
            prescriptionForm.addEventListener('submit', function(e) {
                const medications = document.querySelectorAll('.medication-row');
                let hasValidMedication = false;
                
                medications.forEach(function(row) {
                    const name = row.querySelector('input[name*="[name]"]').value.trim();
                    const form = row.querySelector('select[name*="[form]"]').value;
                    const dosage = row.querySelector('input[name*="[dosage]"]').value.trim();
                    const frequency = row.querySelector('input[name*="[frequency]"]').value.trim();
                    const duration = row.querySelector('input[name*="[duration]"]').value.trim();
                    
                    if (name && form && dosage && frequency && duration) {
                        hasValidMedication = true;
                    }
                });
                
                if (!hasValidMedication) {
                    e.preventDefault();
                    alert('Veuillez remplir au moins un médicament complet avec tous les champs obligatoires.');
                    return false;
                }
            });
        }
        
        // Auto-sync consultation form fields
        const consultationForm = document.querySelector('form[action*="consultations"]');
        if (consultationForm) {
            const doctorNotesField = document.getElementById('doctor_notes');
            const diagnosisField = document.getElementById('diagnosis');
            const treatmentPlanField = document.getElementById('treatment_plan');
            
            // Sync fields when they change
            if (doctorNotesField) {
                doctorNotesField.addEventListener('input', function() {
                    const hiddenField = document.getElementById('complete_doctor_notes');
                    if (hiddenField) {
                        hiddenField.value = this.value;
                    }
                });
            }
            
            if (diagnosisField) {
                diagnosisField.addEventListener('input', function() {
                    const hiddenField = document.getElementById('complete_diagnosis');
                    if (hiddenField) {
                        hiddenField.value = this.value;
                    }
                });
            }
            
            if (treatmentPlanField) {
                treatmentPlanField.addEventListener('input', function() {
                    const hiddenField = document.getElementById('complete_treatment_plan');
                    if (hiddenField) {
                        hiddenField.value = this.value;
                    }
                });
            }
        }
        
        // Show success message if consultation was completed
        @if(session('consultation_completed'))
            alert('✅ Consultation terminée avec succès !');
        @endif
    });
</script>
@endsection