<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Patient extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'patient_number',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'phone_number',
        'email',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'medical_history',
        'allergies',
        'blood_group',
        'has_insurance',
        'insurance_type',
        'insurance_name',
        'insurance_number',
        'nina',
        'custom_coverage_percentage',
        'insurance_start_date',
        'insurance_expiry_date',
        'insurance_notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'has_insurance' => 'boolean',
        'custom_coverage_percentage' => 'decimal:2',
        'insurance_start_date' => 'date',
        'insurance_expiry_date' => 'date',
    ];

    /**
     * Get the user associated with the patient.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the appointments for the patient.
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get the prescriptions for the patient.
     */
    public function prescriptions(): HasMany
    {
        return $this->hasMany(Prescription::class);
    }

    /**
     * Get the payments for the patient.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the vital signs for the patient.
     */
    public function vitalSigns(): HasMany
    {
        return $this->hasMany(VitalSign::class);
    }

    /**
     * Get the lab results for the patient.
     */
    public function labResults(): HasMany
    {
        return $this->hasMany(LabResult::class);
    }

    /**
     * Get the full name of the patient.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get the age of the patient.
     */
    public function getAge(): int
    {
        if (!$this->date_of_birth) {
            return 0;
        }
        
        return $this->date_of_birth->age;
    }

    public function getInitialsAttribute(): string
    {
        return strtoupper($this->first_name[0] . $this->last_name[0]);
    }

    /**
     * Get the effective insurance coverage percentage for this patient
     */
    public function getInsuranceCoveragePercentage(): float
    {
        if (!$this->has_insurance || !$this->insurance_type) {
            return 0;
        }

        // Vérifier si l'assurance est encore valide
        if ($this->insurance_expiry_date && $this->insurance_expiry_date->isPast()) {
            return 0;
        }

        return InsuranceCoverage::getPatientCoveragePercentage(
            $this->insurance_type,
            $this->custom_coverage_percentage
        );
    }

    /**
     * Check if patient has valid insurance
     */
    public function hasValidInsurance(): bool
    {
        if (!$this->has_insurance || !$this->insurance_type) {
            return false;
        }

        // Vérifier la date d'expiration
        if ($this->insurance_expiry_date && $this->insurance_expiry_date->isPast()) {
            return false;
        }

        // Vérifier que le type d'assurance existe et est actif
        $coverage = InsuranceCoverage::where('insurance_type', $this->insurance_type)
                                   ->where('is_active', true)
                                   ->first();

        return $coverage !== null;
    }

    /**
     * Get insurance type details
     */
    public function getInsuranceDetails(): ?array
    {
        if (!$this->has_insurance || !$this->insurance_type) {
            return null;
        }

        $coverage = InsuranceCoverage::where('insurance_type', $this->insurance_type)
                                   ->where('is_active', true)
                                   ->first();

        if (!$coverage) {
            return null;
        }

        return [
            'type' => $this->insurance_type,
            'name' => $this->insurance_name,
            'number' => $this->insurance_number,
            'nina' => $this->nina,
            'coverage_type' => $coverage->coverage_type,
            'coverage_percentage' => $this->getInsuranceCoveragePercentage(),
            'is_valid' => $this->hasValidInsurance(),
            'expiry_date' => $this->insurance_expiry_date,
            'start_date' => $this->insurance_start_date,
            'notes' => $this->insurance_notes,
        ];
    }

    /**
     * Calculate insurance discount for a given amount
     */
    public function calculateInsuranceDiscount(float $amount): float
    {
        if (!$this->hasValidInsurance()) {
            return 0;
        }

        $percentage = $this->getInsuranceCoveragePercentage();
        return $amount * ($percentage / 100);
    }

    /**
     * Calculate final amount after insurance discount
     */
    public function calculateFinalAmount(float $originalAmount): float
    {
        $discount = $this->calculateInsuranceDiscount($originalAmount);
        return $originalAmount - $discount;
    }
}