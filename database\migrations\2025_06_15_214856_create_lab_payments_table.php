<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lab_payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_number')->unique(); // Numéro de reçu
            $table->string('prescription_number'); // Référence à la prescription médecin
            $table->foreignId('patient_id')->constrained()->onDelete('restrict');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->json('lab_tests'); // Liste des analyses payées avec détails
            $table->decimal('total_amount', 10, 2); // Montant total
            $table->enum('payment_method', ['cash', 'mobile_money', 'card', 'insurance', 'credit'])->default('cash');
            $table->string('transaction_reference')->nullable(); // Référence transaction mobile money/carte
            $table->enum('status', ['pending', 'paid', 'partially_paid', 'refunded'])->default('paid');
            $table->foreignId('received_by')->constrained('users')->onDelete('restrict'); // Réceptionniste
            $table->text('notes')->nullable();
            $table->timestamp('payment_date');
            $table->timestamp('work_order_generated_at')->nullable(); // Quand le bon de travail a été généré
            $table->timestamps();

            // Index pour recherche rapide
            $table->index(['prescription_number', 'patient_id']);
            $table->index(['payment_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lab_payments');
    }
};
