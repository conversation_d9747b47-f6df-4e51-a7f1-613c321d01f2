@extends('layouts.app')

@section('title', 'Ordonnances en Attente')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Ordonnances en Attente</h1>
            <p class="text-gray-600 mt-1">Gestion des prescriptions médicales</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('pharmacist.cart') }}" class="btn btn-outline btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Vente Directe
            </a>
            <a href="{{ route('pharmacist.dashboard') }}" class="btn btn-ghost">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                </svg>
                Tableau de Bord
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-title">En Attente</div>
            <div class="stat-value text-warning">{{ $stats['pending_count'] }}</div>
            <div class="stat-desc">ordonnances</div>
        </div>
        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-title">Dispensées Aujourd'hui</div>
            <div class="stat-value text-success">{{ $stats['dispensed_today'] }}</div>
            <div class="stat-desc">ordonnances</div>
        </div>
        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-title">Valeur Totale</div>
            <div class="stat-value text-info">{{ number_format($stats['pending_total_value'], 0, ',', ' ') }}</div>
            <div class="stat-desc">FCFA</div>
        </div>
        <div class="stat bg-base-100 shadow rounded-lg">
            <div class="stat-title">Patients</div>
            <div class="stat-value text-primary">{{ $stats['pending_patients'] }}</div>
            <div class="stat-desc">en attente</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card bg-base-100 shadow-xl mb-6">
        <div class="card-body">
            <form method="GET" action="{{ route('pharmacist.prescriptions.index') }}" class="flex flex-wrap gap-4 items-end">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Rechercher Patient</span>
                    </label>
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Nom ou numéro patient" class="input input-bordered w-full max-w-xs">
                </div>
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Statut</span>
                    </label>
                    <select name="status" class="select select-bordered w-full max-w-xs">
                        <option value="">Tous les statuts</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>En attente</option>
                        <option value="dispensed" {{ request('status') == 'dispensed' ? 'selected' : '' }}>Dispensée</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Annulée</option>
                    </select>
                </div>
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Date</span>
                    </label>
                    <input type="date" name="date" value="{{ request('date') }}" class="input input-bordered w-full max-w-xs">
                </div>
                <div class="form-control">
                    <button type="submit" class="btn btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Filtrer
                    </button>
                </div>
                @if(request()->hasAny(['search', 'status', 'date']))
                <div class="form-control">
                    <a href="{{ route('pharmacist.prescriptions.index') }}" class="btn btn-ghost">
                        Réinitialiser
                    </a>
                </div>
                @endif
            </form>
        </div>
    </div>

    <!-- Prescriptions List -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            @if($prescriptions->count() > 0)
                <div class="overflow-x-auto">
                    <table class="table table-zebra w-full">
                        <thead>
                            <tr>
                                <th>N° Ordonnance</th>
                                <th>Patient</th>
                                <th>Médecin</th>
                                <th>Date</th>
                                <th>Médicaments</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($prescriptions as $prescription)
                            <tr class="hover">
                                <td>
                                    <div class="font-medium text-primary">
                                        {{ $prescription->prescription_number }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ $prescription->created_at->format('H:i') }}
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center space-x-3">
                                        <div class="avatar placeholder">
                                            <div class="bg-neutral-focus text-neutral-content rounded-full w-8">
                                                <span class="text-xs">{{ substr($prescription->patient->first_name, 0, 1) }}{{ substr($prescription->patient->last_name, 0, 1) }}</span>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="font-bold">{{ $prescription->patient->first_name }} {{ $prescription->patient->last_name }}</div>
                                            <div class="text-sm opacity-50">{{ $prescription->patient->patient_number }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($prescription->appointment && $prescription->appointment->doctor)
                                        <div class="font-medium">Dr. {{ $prescription->appointment->doctor->first_name }} {{ $prescription->appointment->doctor->last_name }}</div>
                                        <div class="text-sm text-gray-500">{{ $prescription->appointment->doctor->specialization }}</div>
                                    @else
                                        <span class="text-gray-400">Vente directe</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="font-medium">{{ $prescription->created_at->format('d/m/Y') }}</div>
                                    <div class="text-sm text-gray-500">{{ $prescription->created_at->diffForHumans() }}</div>
                                </td>
                                <td>
                                    <div class="text-sm">
                        <span class="badge badge-outline">{{ $prescription->prescriptionItems->count() }} médicament(s)</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        @foreach($prescription->prescriptionItems->take(2) as $item)
                            {{ $item->medication_name }}@if(!$loop->last), @endif
                        @endforeach
                        @if($prescription->prescriptionItems->count() > 2)
                            <span class="text-primary">+{{ $prescription->prescriptionItems->count() - 2 }} autres</span>
                        @endif
                    </div>
                                </td>
                                <td>
                                    <div class="font-bold text-lg">{{ number_format($prescription->total_amount, 0, ',', ' ') }} FCFA</div>
                                </td>
                                <td>
                                    @if($prescription->status == 'pending')
                                        <span class="badge badge-warning">En attente</span>
                                    @elseif($prescription->status == 'dispensed')
                                        <span class="badge badge-success">Dispensée</span>
                                        @if($prescription->dispensed_at)
                                            <div class="text-xs text-gray-500 mt-1">{{ $prescription->dispensed_at->format('d/m/Y H:i') }}</div>
                                        @endif
                                    @elseif($prescription->status == 'cancelled')
                                        <span class="badge badge-error">Annulée</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="flex space-x-2">
                                        <a href="{{ route('pharmacist.prescriptions.show', $prescription) }}" 
                                           class="btn btn-sm btn-outline btn-primary" title="Voir détails">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </a>
                                        @if($prescription->status == 'dispensed')
                                            <a href="{{ route('pharmacist.prescriptions.receipt', $prescription) }}" 
                                               class="btn btn-sm btn-outline btn-info" title="Voir reçu">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($prescriptions->hasPages())
                <div class="flex justify-center mt-6">
                    {{ $prescriptions->links() }}
                </div>
                @endif
            @else
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune ordonnance</h3>
                    <p class="mt-1 text-sm text-gray-500">Aucune ordonnance ne correspond à vos critères de recherche.</p>
                    <div class="mt-6">
                        <a href="{{ route('pharmacist.cart') }}" class="btn btn-primary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                            </svg>
                            Panier Intelligent
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection