<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('medications', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('generic_name')->nullable();
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->string('manufacturer')->nullable();
            $table->decimal('unit_price', 10, 2);
            $table->string('dosage_form'); // tablet, syrup, injection, etc.
            $table->string('strength')->nullable(); // 500mg, 250ml, etc.
            $table->boolean('prescription_required')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medications');
    }
};