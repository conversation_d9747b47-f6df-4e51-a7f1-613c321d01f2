<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LabPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_number',
        'prescription_number',
        'patient_id',
        'appointment_id',
        'lab_tests',
        'total_amount',
        'payment_method',
        'transaction_reference',
        'status',
        'received_by',
        'notes',
        'payment_date',
        'work_order_generated_at',
    ];

    protected $casts = [
        'lab_tests' => 'array',
        'total_amount' => 'decimal:2',
        'payment_date' => 'datetime',
        'work_order_generated_at' => 'datetime',
    ];

    /**
     * Get the patient associated with the lab payment.
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the appointment associated with the lab payment.
     */
    public function appointment(): BelongsTo
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the user who received the payment.
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Get the work order associated with this payment.
     */
    public function workOrder(): HasOne
    {
        return $this->hasOne(LabWorkOrder::class);
    }

    /**
     * Get the lab results associated with this payment.
     */
    public function labResults(): HasMany
    {
        return $this->hasMany(LabResult::class, 'prescription_number', 'prescription_number');
    }

    /**
     * Generate a unique payment number.
     */
    public static function generatePaymentNumber(): string
    {
        $date = now()->format('Ymd');
        $count = self::whereDate('created_at', today())->count() + 1;
        return 'LAB-PAY-' . $date . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get payment method label.
     */
    public function getPaymentMethodLabelAttribute(): string
    {
        return match($this->payment_method) {
            'cash' => 'Espèces',
            'mobile_money' => 'Mobile Money',
            'card' => 'Carte bancaire',
            'insurance' => 'Assurance',
            'credit' => 'Crédit',
            default => ucfirst($this->payment_method),
        };
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'paid' => 'Payé',
            'partially_paid' => 'Partiellement payé',
            'refunded' => 'Remboursé',
            default => ucfirst($this->status),
        };
    }
}
