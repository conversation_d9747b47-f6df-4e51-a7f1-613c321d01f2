<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('medication_insurance_coverage', function (Blueprint $table) {
            $table->id();
            
            // Référence au médicament
            $table->foreignId('medication_id')
                  ->constrained('medications')
                  ->onDelete('cascade');
            
            // Type d'assurance (AMO, CNOPS, etc.)
            $table->string('insurance_type');
            
            // Pourcentage de couverture pour ce médicament avec cette assurance
            $table->decimal('coverage_percentage', 5, 2)
                  ->comment('Pourcentage de couverture (0.00 à 100.00)');
            
            // Conditions spéciales
            $table->decimal('minimum_amount', 10, 2)
                  ->nullable()
                  ->comment('Montant minimum pour bénéficier de la couverture');
            
            $table->decimal('maximum_coverage_amount', 10, 2)
                  ->nullable()
                  ->comment('Montant maximum de couverture');
            
            // Dates de validité
            $table->date('valid_from')
                  ->nullable()
                  ->comment('Date de début de validité');
            
            $table->date('valid_until')
                  ->nullable()
                  ->comment('Date de fin de validité');
            
            // Statut
            $table->boolean('is_active')
                  ->default(true)
                  ->comment('Si cette couverture est active');
            
            // Notes et conditions
            $table->text('conditions')
                  ->nullable()
                  ->comment('Conditions spéciales pour cette couverture');
            
            $table->text('notes')
                  ->nullable()
                  ->comment('Notes administratives');
            
            // Audit
            $table->foreignId('created_by')
                  ->nullable()
                  ->constrained('users')
                  ->onDelete('set null');
            
            $table->foreignId('updated_by')
                  ->nullable()
                  ->constrained('users')
                  ->onDelete('set null');
            
            $table->timestamps();
            
            // Index et contraintes
            $table->unique(['medication_id', 'insurance_type'], 'unique_medication_insurance');
            $table->index(['insurance_type', 'is_active']);
            $table->index(['medication_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medication_insurance_coverage');
    }
};
