@extends('layouts.app')

@section('title', 'Reçu de Vente #' . str_pad($sale->id, 6, '0', STR_PAD_LEFT))

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Reçu de Vente</h1>
                    <p class="text-gray-600">N° {{ str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('pharmacist.sales.receipt.download', $sale) }}" 
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Télécharger PDF
                    </a>
                    <a href="{{ route('pharmacist.sales.index') }}" 
                       class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Retour à la Liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Receipt Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden" id="receipt-content">
            <!-- Receipt Header -->
            <div class="bg-blue-600 text-white px-8 py-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h2 class="text-2xl font-bold">CLINIQUE MÉDICALE PRIVÉE</h2>
                        <p class="text-blue-100 mt-1">Pharmacie - Reçu de Vente</p>
                        <p class="text-blue-100 text-sm mt-2">
                            Adresse: [Adresse de la clinique]<br>
                            Téléphone: [Numéro de téléphone]<br>
                            Email: [Email de contact]
                        </p>
                    </div>
                    <div class="text-right">
                        <div class="bg-white text-blue-600 px-4 py-2 rounded-lg">
                            <p class="text-sm font-medium">N° REÇU</p>
                            <p class="text-xl font-bold">#{{ str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sale Information -->
            <div class="px-8 py-6 border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Patient Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Informations Patient</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Nom complet:</span>
                                <span class="font-medium">{{ $sale->patient->first_name }} {{ $sale->patient->last_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">N° Patient:</span>
                                <span class="font-medium">{{ $sale->patient->patient_number }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Téléphone:</span>
                                <span class="font-medium">{{ $sale->patient->phone ?? 'N/A' }}</span>
                            </div>
                            @if($sale->patient->date_of_birth)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Date de naissance:</span>
                                <span class="font-medium">{{ $sale->patient->date_of_birth->format('d/m/Y') }}</span>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Sale Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Informations Vente</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Date de vente:</span>
                                <span class="font-medium">{{ $sale->dispensed_at->format('d/m/Y à H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Type de vente:</span>
                                <span class="font-medium">
                                    @if($sale->sale_type == 'prescription')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Prescription
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Vente Directe
                                        </span>
                                    @endif
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Mode de paiement:</span>
                                <span class="font-medium">
                                    @if($sale->payment_method == 'cash')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Espèces
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            Mobile Money
                                        </span>
                                    @endif
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Pharmacien:</span>
                                <span class="font-medium">{{ $sale->pharmacist->first_name ?? 'N/A' }} {{ $sale->pharmacist->last_name ?? '' }}</span>
                            </div>
                            @if($sale->prescription_id)
                            <div class="flex justify-between">
                                <span class="text-gray-600">N° Prescription:</span>
                                <span class="font-medium">#{{ str_pad($sale->prescription_id, 6, '0', STR_PAD_LEFT) }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medications List -->
            <div class="px-8 py-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Détail des Médicaments</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Médicament</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lot</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiration</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantité</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Prix Unitaire</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Sous-total</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($sale->saleItems as $item)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $item->medicationInventory->medication->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $item->medicationInventory->medication->dosage }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $item->medicationInventory->batch_number }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $item->medicationInventory->expiry_date->format('d/m/Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                        {{ $item->quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                        {{ number_format($item->unit_price, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-medium">
                                        {{ number_format($item->subtotal, 0, ',', ' ') }} FCFA
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Totals -->
            <div class="px-8 py-6 bg-gray-50 border-t border-gray-200">
                <div class="flex justify-end">
                    <div class="w-full max-w-md">
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Sous-total:</span>
                                <span class="font-medium">{{ number_format($sale->saleItems->sum('subtotal'), 0, ',', ' ') }} FCFA</span>
                            </div>
                            @if($sale->total_discount > 0)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Remise:</span>
                                <span class="font-medium text-green-600">-{{ number_format($sale->total_discount, 0, ',', ' ') }} FCFA</span>
                            </div>
                            @endif
                            <div class="border-t border-gray-200 pt-2">
                                <div class="flex justify-between">
                                    <span class="text-lg font-semibold text-gray-900">Total à payer:</span>
                                    <span class="text-lg font-bold text-blue-600">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Montant payé:</span>
                                <span class="font-medium text-green-600">{{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA</span>
                            </div>
                            @if($sale->amount_paid > $sale->total_amount)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Monnaie rendue:</span>
                                <span class="font-medium text-blue-600">{{ number_format($sale->amount_paid - $sale->total_amount, 0, ',', ' ') }} FCFA</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="px-8 py-6 bg-blue-50 border-t border-blue-200">
                <div class="text-center">
                    <p class="text-sm text-blue-800 font-medium">Merci pour votre confiance !</p>
                    <p class="text-xs text-blue-600 mt-2">
                        Ce reçu fait foi de paiement. Conservez-le précieusement.<br>
                        Pour toute réclamation, veuillez présenter ce reçu.
                    </p>
                    <div class="mt-4 text-xs text-blue-500">
                        <p>Reçu généré le {{ now()->format('d/m/Y à H:i:s') }}</p>
                        <p>Système de Gestion Clinique - Version 1.0</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    #receipt-content {
        box-shadow: none !important;
        border: none !important;
    }
}
</style>
@endsection