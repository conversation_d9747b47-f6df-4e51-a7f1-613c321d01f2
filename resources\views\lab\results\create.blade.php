@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-5xl mx-auto">
        <!-- Header -->
        <div class="mb-8 text-center">
            <h1 class="text-4xl font-bold text-primary">Saisie des Résultats d'Analyses</h1>
            <p class="text-lg text-gray-600">Bon de Travail #{{ $workOrder->work_order_number }}</p>
        </div>

        <!-- Patient & Work Order Info -->
        <div class="mb-6 bg-white shadow-md rounded-lg p-6 border">
            <h2 class="text-2xl font-semibold mb-4">Informations Générales</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <p><strong>Patient :</strong> {{ $workOrder->patient->first_name }} {{ $workOrder->patient->last_name }}</p>
                <p><strong>Prescription :</strong> {{ $workOrder->prescription_number }}</p>
                <p><strong>Status du bon :</strong> <span class="badge badge-info">{{ $workOrder->status }}</span></p>
                <p><strong>Technicien :</strong> {{ Auth::user()->name }}</p>
                <p><strong>Priorité :</strong> <span class="badge badge-{{ $workOrder->priority == 'urgent' ? 'warning' : ($workOrder->priority == 'stat' ? 'error' : 'neutral') }}">{{ ucfirst($workOrder->priority) }}</span></p>
            </div>
        </div>

        <!-- Affichage des erreurs de validation -->
        @if ($errors->any())
            <div class="alert alert-error mb-6">
                <ul class="list-disc pl-5">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Results Entry Form -->
        <div class="bg-white shadow-md rounded-lg p-6 border">
            <h2 class="text-2xl font-semibold mb-4">Saisir les résultats</h2>
            @if($labResults->isEmpty())
                <div class="alert alert-info text-center">
                    Aucun test à saisir pour ce bon de travail.
                </div>
            @else
            <form action="{{ route('lab.results.store', $workOrder) }}" method="POST">
                @csrf
                <div class="space-y-6">
                    @foreach($labResults as $result)
                        <div class="border rounded-lg p-4 bg-gray-50">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                                <h3 class="text-xl font-semibold text-primary mb-2 md:mb-0">
                                    {{ $result->labTest->name }}
                                    <span class="text-xs text-gray-500 font-normal">({{ $result->labTest->test_code }})</span>
                                </h3>
                                @if($result->urgency && $result->urgency !== 'normal')
                                    <span class="badge badge-{{ $result->urgency == 'urgent' ? 'warning' : 'error' }}">
                                        {{ strtoupper($result->urgency) }}
                                    </span>
                                @endif
                            </div>
                            <div class="text-sm text-gray-600 mb-2">
                                @if($result->labTest->unit)
                                    <span class="mr-4"><strong>Unité :</strong> {{ $result->labTest->unit }}</span>
                                @endif
                                @if($result->labTest->normal_range)
                                    <span class="mr-4"><strong>Valeurs de référence :</strong> {{ $result->labTest->normal_range }}</span>
                                @endif
                            </div>
                            @if($result->labTest->description)
                                <div class="text-xs text-gray-500 mb-2">{{ $result->labTest->description }}</div>
                            @endif
                            <input type="hidden" name="results[{{ $loop->index }}][lab_result_id]" value="{{ $result->id }}">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Résultat @if($result->labTest->unit) ({{ $result->labTest->unit }}) @endif</span>
                                    </label>
                                    <input type="text" name="results[{{ $loop->index }}][result]" class="input input-bordered" required placeholder="Saisir le résultat...">
                                </div>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Interprétation</span>
                                    </label>
                                    <textarea name="results[{{ $loop->index }}][interpretation]" class="textarea textarea-bordered" placeholder="Interprétation des résultats..."></textarea>
                                </div>
                            </div>
                        </div>
                    @endforeach

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Notes du Technicien</span>
                        </label>
                        <textarea name="technician_notes" class="textarea textarea-bordered" placeholder="Notes générales sur les analyses..."></textarea>
                    </div>
                </div>

                <div class="mt-8 flex justify-end">
                    <button type="submit" class="btn btn-primary btn-lg">Enregistrer et Terminer</button>
                </div>
            </form>
            @endif
        </div>
    </div>
</div>
@endsection