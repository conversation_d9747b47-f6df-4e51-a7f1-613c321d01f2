<?php

namespace App\Http\Controllers;

use App\Models\Medication;
use App\Models\MedicationInsuranceCoverage;
use App\Models\InsuranceCoverage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MedicationInsuranceController extends Controller
{
    /**
     * Display insurance coverages for a medication.
     */
    public function index(Medication $medication)
    {
        $medication->load(['insuranceCoverages' => function($query) {
            $query->orderBy('insurance_type');
        }]);

        $availableInsuranceTypes = InsuranceCoverage::where('is_active', true)
            ->pluck('insurance_type')
            ->toArray();

        return view('admin.medications.insurance.index', compact('medication', 'availableInsuranceTypes'));
    }

    /**
     * Show the form for creating a new insurance coverage.
     */
    public function create(Medication $medication)
    {
        $availableInsuranceTypes = InsuranceCoverage::where('is_active', true)
            ->get();

        return view('admin.medications.insurance.create', compact('medication', 'availableInsuranceTypes'));
    }

    /**
     * Store a newly created insurance coverage.
     */
    public function store(Request $request, Medication $medication)
    {
        $validated = $request->validate([
            'insurance_type' => 'required|string|exists:insurance_coverage,insurance_type',
            'coverage_percentage' => 'required|numeric|min:0|max:100',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_coverage_amount' => 'nullable|numeric|min:0',
            'valid_from' => 'nullable|date',
            'valid_until' => 'nullable|date|after:valid_from',
            'conditions' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        // Check if coverage already exists for this medication and insurance type
        $existingCoverage = MedicationInsuranceCoverage::where('medication_id', $medication->id)
            ->where('insurance_type', $validated['insurance_type'])
            ->first();

        if ($existingCoverage) {
            return back()->withErrors([
                'insurance_type' => 'Une couverture existe déjà pour ce médicament et ce type d\'assurance.'
            ])->withInput();
        }

        $validated['medication_id'] = $medication->id;
        $validated['created_by'] = Auth::id();
        $validated['updated_by'] = Auth::id();

        MedicationInsuranceCoverage::create($validated);

        return redirect()->route('admin.medications.insurance.index', $medication)
            ->with('success', 'Couverture d\'assurance ajoutée avec succès.');
    }

    /**
     * Show the form for editing an insurance coverage.
     */
    public function edit(Medication $medication, MedicationInsuranceCoverage $coverage)
    {
        $availableInsuranceTypes = InsuranceCoverage::where('is_active', true)
            ->get();

        return view('admin.medications.insurance.edit', compact('medication', 'coverage', 'availableInsuranceTypes'));
    }

    /**
     * Update the specified insurance coverage.
     */
    public function update(Request $request, Medication $medication, MedicationInsuranceCoverage $coverage)
    {
        $validated = $request->validate([
            'coverage_percentage' => 'required|numeric|min:0|max:100',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_coverage_amount' => 'nullable|numeric|min:0',
            'valid_from' => 'nullable|date',
            'valid_until' => 'nullable|date|after:valid_from',
            'conditions' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        $validated['updated_by'] = Auth::id();

        $coverage->update($validated);

        return redirect()->route('admin.medications.insurance.index', $medication)
            ->with('success', 'Couverture d\'assurance mise à jour avec succès.');
    }

    /**
     * Remove the specified insurance coverage.
     */
    public function destroy(Medication $medication, MedicationInsuranceCoverage $coverage)
    {
        $coverage->delete();

        return redirect()->route('admin.medications.insurance.index', $medication)
            ->with('success', 'Couverture d\'assurance supprimée avec succès.');
    }

    /**
     * Bulk update insurance coverages for multiple medications.
     */
    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'medication_ids' => 'required|array',
            'medication_ids.*' => 'exists:medications,id',
            'insurance_type' => 'required|string|exists:insurance_coverage,insurance_type',
            'coverage_percentage' => 'required|numeric|min:0|max:100',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_coverage_amount' => 'nullable|numeric|min:0',
            'valid_from' => 'nullable|date',
            'valid_until' => 'nullable|date|after:valid_from',
            'conditions' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        DB::beginTransaction();

        try {
            foreach ($validated['medication_ids'] as $medicationId) {
                MedicationInsuranceCoverage::updateOrCreate(
                    [
                        'medication_id' => $medicationId,
                        'insurance_type' => $validated['insurance_type'],
                    ],
                    [
                        'coverage_percentage' => $validated['coverage_percentage'],
                        'minimum_amount' => $validated['minimum_amount'],
                        'maximum_coverage_amount' => $validated['maximum_coverage_amount'],
                        'valid_from' => $validated['valid_from'],
                        'valid_until' => $validated['valid_until'],
                        'conditions' => $validated['conditions'],
                        'notes' => $validated['notes'],
                        'is_active' => $validated['is_active'] ?? true,
                        'created_by' => Auth::id(),
                        'updated_by' => Auth::id(),
                    ]
                );
            }

            DB::commit();

            return redirect()->back()
                ->with('success', 'Couvertures d\'assurance mises à jour pour ' . count($validated['medication_ids']) . ' médicaments.');

        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la mise à jour: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get insurance coverage details for a medication (AJAX).
     */
    public function getCoverage(Medication $medication, string $insuranceType)
    {
        $coverage = MedicationInsuranceCoverage::where('medication_id', $medication->id)
            ->where('insurance_type', $insuranceType)
            ->currentlyValid()
            ->first();

        if (!$coverage) {
            return response()->json([
                'covered' => false,
                'message' => 'Ce médicament n\'est pas couvert par cette assurance.'
            ]);
        }

        return response()->json([
            'covered' => true,
            'coverage_percentage' => $coverage->coverage_percentage,
            'minimum_amount' => $coverage->minimum_amount,
            'maximum_coverage_amount' => $coverage->maximum_coverage_amount,
            'conditions' => $coverage->conditions,
        ]);
    }
}
