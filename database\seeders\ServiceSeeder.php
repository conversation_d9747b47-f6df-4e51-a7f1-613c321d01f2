<?php

namespace Database\Seeders;

use App\Models\Service;
use Illuminate\Database\Seeder;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'name' => 'Consultation générale',
                'description' => 'Consultation de médecine générale pour tous types de problèmes de santé courants.',
                'price' => 15000,
                'service_code' => 'CONS-GEN',
                'is_active' => true,
            ],
            [
                'name' => 'Consultation pédiatrique',
                'description' => 'Consultation spécialisée pour les enfants de 0 à 15 ans.',
                'price' => 20000,
                'service_code' => 'CONS-PED',
                'is_active' => true,
            ],
            [
                'name' => 'Consultation gynécologique',
                'description' => 'Consultation spécialisée pour les femmes incluant examens gynécologiques.',
                'price' => 25000,
                'service_code' => 'CONS-GYN',
                'is_active' => true,
            ],
            [
                'name' => 'Consultation cardiologique',
                'description' => 'Consultation et examens cardiaques incluant ECG.',
                'price' => 30000,
                'service_code' => 'CONS-CAR',
                'is_active' => true,
            ],
            [
                'name' => 'Consultation dentaire',
                'description' => 'Consultation et examens dentaires.',
                'price' => 20000,
                'service_code' => 'CONS-DEN',
                'is_active' => true,
            ],
            [
                'name' => 'Radiographie',
                'description' => 'Examen radiologique (rayons X) pour diagnostic.',
                'price' => 25000,
                'service_code' => 'RAD-GEN',
                'is_active' => true,
            ],
            [
                'name' => 'Échographie',
                'description' => 'Examen d\'imagerie par ultrasons.',
                'price' => 35000,
                'service_code' => 'ECHO-GEN',
                'is_active' => true,
            ],
            [
                'name' => 'Analyse de sang complète',
                'description' => 'Analyse sanguine complète incluant numération formule sanguine.',
                'price' => 15000,
                'service_code' => 'LAB-SANG',
                'is_active' => true,
            ],
            [
                'name' => 'Vaccination',
                'description' => 'Administration de vaccins (prix par dose).',
                'price' => 10000,
                'service_code' => 'VAC-GEN',
                'is_active' => true,
            ],
            [
                'name' => 'Consultation psychiatrique',
                'description' => 'Consultation spécialisée en santé mentale.',
                'price' => 35000,
                'service_code' => 'CONS-PSY',
                'is_active' => true,
            ],
            [
                'name' => 'Kinésithérapie',
                'description' => 'Séance de rééducation physique (1 heure).',
                'price' => 20000,
                'service_code' => 'KINE-GEN',
                'is_active' => true,
            ],
            [
                'name' => 'Soins infirmiers',
                'description' => 'Soins de base, pansements, injections.',
                'price' => 8000,
                'service_code' => 'INF-SOIN',
                'is_active' => true,
            ],
        ];

        foreach ($services as $service) {
            Service::create($service);
        }
    }
} 