<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'service_code',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the doctors associated with the service.
     */
    public function doctors(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Doctor::class);
    }

    /**
     * Get the appointments associated with the service.
     */
    public function appointments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Appointment::class);
    }
}