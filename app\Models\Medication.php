<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Medication extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'generic_name',
        'code',
        'description',
        'manufacturer',
        'unit_price',
        'dosage_form',
        'strength',
        'prescription_required',
        'is_active',
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'prescription_required' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the prescription items that include this medication.
     */
    public function prescriptionItems(): Has<PERSON>any
    {
        return $this->hasMany(PrescriptionItem::class);
    }

    /**
     * Get the inventory records for this medication.
     */
    public function inventories(): HasMany
    {
        return $this->hasMany(MedicationInventory::class);
    }

    /**
     * Get the insurance coverages for this medication.
     */
    public function insuranceCoverages(): HasMany
    {
        return $this->hasMany(MedicationInsuranceCoverage::class);
    }

    /**
     * Get active insurance coverages for this medication.
     */
    public function activeInsuranceCoverages(): Has<PERSON>any
    {
        return $this->hasMany(MedicationInsuranceCoverage::class)
                    ->currentlyValid();
    }

    /**
     * Get coverage percentage for a specific insurance type
     */
    public function getCoveragePercentage(string $insuranceType): ?float
    {
        return MedicationInsuranceCoverage::getCoverageForMedication($this->id, $insuranceType);
    }

    /**
     * Check if medication is covered by a specific insurance
     */
    public function isCoveredByInsurance(string $insuranceType): bool
    {
        return $this->getCoveragePercentage($insuranceType) !== null;
    }

    /**
     * Get all insurance types that cover this medication
     */
    public function getCoveredInsuranceTypes(): array
    {
        return MedicationInsuranceCoverage::getActiveCoveragesForMedication($this->id);
    }

    /**
     * Calculate insurance discount for this medication
     */
    public function calculateInsuranceDiscount(string $insuranceType, float $amount): float
    {
        $coverage = $this->insuranceCoverages()
                         ->where('insurance_type', $insuranceType)
                         ->currentlyValid()
                         ->first();

        if (!$coverage) {
            return 0;
        }

        return $coverage->calculateDiscount($amount);
    }

    /**
     * Get the current stock level of the medication.
     */
    public function getCurrentStockAttribute(): int
    {
        return $this->inventories()->sum('quantity');
    }
}