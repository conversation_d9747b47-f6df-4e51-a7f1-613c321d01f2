@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Modifier le Service
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Modifier les informations de {{ $service->name }}
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0">
                <a href="{{ route('admin.services') }}" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Retour
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <form action="{{ route('admin.services.update', $service) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Name -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nom du service *</span>
                                </label>
                                <input type="text" name="name" value="{{ old('name', $service->name) }}" 
                                       class="input input-bordered @error('name') input-error @enderror" 
                                       placeholder="Ex: Cardiologie, Pédiatrie..." required>
                                @error('name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Service Code -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Code du service *</span>
                                </label>
                                <input type="text" name="service_code" value="{{ old('service_code', $service->service_code) }}" 
                                       class="input input-bordered @error('service_code') input-error @enderror" 
                                       placeholder="Ex: CARD, PEDI, GYNE..." required>
                                @error('service_code')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Price -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Prix de consultation *</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" name="price" value="{{ old('price', $service->price) }}" 
                                           class="input input-bordered @error('price') input-error @enderror" 
                                           placeholder="0" min="0" step="100" required>
                                    <span class="bg-base-200 px-4 py-3 text-sm">FCFA</span>
                                </div>
                                @error('price')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Statut</span>
                                </label>
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" name="is_active" value="1" 
                                           class="checkbox checkbox-primary" 
                                           {{ old('is_active', $service->is_active) ? 'checked' : '' }}>
                                    <span class="label-text ml-2">Service actif</span>
                                </label>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-control mt-6">
                            <label class="label">
                                <span class="label-text">Description</span>
                            </label>
                            <textarea name="description" class="textarea textarea-bordered @error('description') textarea-error @enderror" 
                                      placeholder="Description détaillée du service médical...">{{ old('description', $service->description) }}</textarea>
                            @error('description')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="form-control mt-8">
                            <button type="submit" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Mettre à jour
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Service Statistics -->
        @if($service->doctors()->count() > 0 || $service->appointments()->count() > 0)
            <div class="mt-8">
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">Statistiques du Service</h3>
                        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3 mt-4">
                            <div class="stat bg-base-200 rounded-lg">
                                <div class="stat-figure text-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div class="stat-title">Médecins</div>
                                <div class="stat-value text-primary">{{ $service->doctors()->count() }}</div>
                                <div class="stat-desc">médecin(s) assigné(s)</div>
                            </div>

                            <div class="stat bg-base-200 rounded-lg">
                                <div class="stat-figure text-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="stat-title">Rendez-vous</div>
                                <div class="stat-value text-secondary">{{ $service->appointments()->count() }}</div>
                                <div class="stat-desc">total des RDV</div>
                            </div>

                            <div class="stat bg-base-200 rounded-lg">
                                <div class="stat-figure text-accent">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                </div>
                                <div class="stat-title">Revenus estimés</div>
                                <div class="stat-value text-accent">{{ number_format($service->appointments()->count() * $service->price) }}</div>
                                <div class="stat-desc">FCFA</div>
                            </div>
                        </div>

                        @if($service->doctors()->count() > 0)
                            <div class="mt-6">
                                <h4 class="font-semibold mb-3">Médecins assignés :</h4>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($service->doctors as $doctor)
                                        <div class="badge badge-primary badge-lg">
                                            Dr. {{ $doctor->user->name }}
                                            @if($doctor->specialization)
                                                <span class="ml-1 opacity-70">({{ $doctor->specialization }})</span>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
