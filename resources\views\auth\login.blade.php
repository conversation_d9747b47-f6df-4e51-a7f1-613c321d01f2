<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Connexion - GlobalCare Solutions</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="min-h-screen bg-gradient-to-br from-primary/5 to-secondary/5 flex items-center justify-center">
    <div class="card max-w-4xl bg-base-100 shadow-2xl mx-4">
        <div class="card-body p-8">
            <!-- Logo et Titre -->
            <div class="text-center mb-8">
                <div class="inline-block relative mb-6">
                    <div class="absolute -inset-1 bg-gradient-to-r from-primary to-secondary rounded-lg blur opacity-50"></div>
                    <div class="relative bg-base-100 rounded-lg p-4">
                        <span class="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">GC</span>
                    </div>
                </div>
                
                <h1 class="text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-4">GlobalCare</h1>
                <h2 class="text-2xl font-semibold">Connexion à votre compte</h2>
                <p class="text-base-content/70 mt-2">Système de gestion clinique nouvelle génération</p>
            </div>

            <!-- Formulaire de connexion -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <form action="{{ route('login') }}" method="POST" class="space-y-4">
                        @csrf
                        
                        <!-- Email -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">Adresse email</span>
                            </label>
                            <input type="email" name="email" 
                                   class="input input-bordered bg-base-100/50 transition-all duration-300 focus:ring-2 focus:ring-primary/20 @error('email') input-error @enderror" 
                                   value="{{ old('email') }}" required placeholder="<EMAIL>" />
                            @error('email')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Password -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">Mot de passe</span>
                            </label>
                            <input type="password" name="password" 
                                   class="input input-bordered bg-base-100/50 transition-all duration-300 focus:ring-2 focus:ring-primary/20 @error('password') input-error @enderror" 
                                   required placeholder="••••••••" />
                            @error('password')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Remember Me -->
                        <div class="form-control">
                            <label class="label cursor-pointer justify-start gap-3">
                                <input type="checkbox" name="remember" class="checkbox checkbox-primary checkbox-sm" />
                                <span class="label-text">Se souvenir de moi</span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary w-full hover:scale-[1.02] transition-transform">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            Se connecter
                        </button>
                    </form>

                    <!-- Demo Accounts -->
                    <div class="divider text-sm text-base-content/50 my-6">Comptes de démonstration</div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="card bg-base-200 hover:bg-base-300 transition-all hover:scale-[1.02] cursor-pointer">
                            <div class="card-body p-4">
                                <div class="flex items-center gap-3 mb-2">
                                    <div class="w-3 h-3 rounded-full bg-primary"></div>
                                    <div class="font-bold">Admin</div>
                                </div>
                                <div class="text-sm text-base-content/70"><EMAIL></div>
                                <div class="text-sm text-base-content/70">password</div>
                            </div>
                        </div>
                        <div class="card bg-base-200 hover:bg-base-300 transition-all hover:scale-[1.02] cursor-pointer">
                            <div class="card-body p-4">
                                <div class="flex items-center gap-3 mb-2">
                                    <div class="w-3 h-3 rounded-full bg-secondary"></div>
                                    <div class="font-bold">Réceptionniste</div>
                                </div>
                                <div class="text-sm text-base-content/70"><EMAIL></div>
                                <div class="text-sm text-base-content/70">password</div>
                            </div>
                        </div>
                        <div class="card bg-base-200 hover:bg-base-300 transition-all hover:scale-[1.02] cursor-pointer">
                            <div class="card-body p-4">
                                <div class="flex items-center gap-3 mb-2">
                                    <div class="w-3 h-3 rounded-full bg-accent"></div>
                                    <div class="font-bold">Médecin</div>
                                </div>
                                <div class="text-sm text-base-content/70"><EMAIL></div>
                                <div class="text-sm text-base-content/70">password</div>
                            </div>
                        </div>
                        <div class="card bg-base-200 hover:bg-base-300 transition-all hover:scale-[1.02] cursor-pointer">
                            <div class="card-body p-4">
                                <div class="flex items-center gap-3 mb-2">
                                    <div class="w-3 h-3 rounded-full bg-info"></div>
                                    <div class="font-bold">Pharmacien</div>
                                </div>
                                <div class="text-sm text-base-content/70"><EMAIL></div>
                                <div class="text-sm text-base-content/70">password</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
