<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu de paiement - {{ $payment->invoice_number }}</title>
    <style>
        body {
            font-family: '<PERSON>ja<PERSON><PERSON> Sans', Arial, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.5;
            color: #333;
            font-size: 14px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .clinic-name {
            font-size: 24px;
            font-weight: bold;
            color: #3182ce;
            margin-bottom: 5px;
        }
        .clinic-info {
            font-size: 14px;
            color: #4a5568;
        }
        .receipt-title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            text-transform: uppercase;
        }
        .receipt-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .meta-block {
            flex: 1;
        }
        .meta-label {
            font-weight: bold;
            color: #4a5568;
            margin-bottom: 5px;
        }
        .meta-value {
            margin-bottom: 10px;
        }
        .patient-info, .payment-info {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #e2e8f0;
        }
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        .info-label {
            flex: 1;
            font-weight: bold;
            color: #4a5568;
        }
        .info-value {
            flex: 2;
        }
        .service-details {
            margin-bottom: 30px;
        }
        .service-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .service-table th {
            background-color: #f7fafc;
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .service-table td {
            padding: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        .amount-row {
            text-align: right;
            margin-top: 10px;
        }
        .total-amount {
            font-size: 18px;
            font-weight: bold;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #718096;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        .signature {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-block {
            flex: 1;
            text-align: center;
            margin: 0 20px;
        }
        .signature-line {
            margin-top: 40px;
            border-top: 1px solid #718096;
            margin-bottom: 5px;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 9999px;
            font-size: 12px;
            font-weight: bold;
        }
        .badge-success {
            background-color: #c6f6d5;
            color: #2f855a;
        }
        .badge-warning {
            background-color: #feebc8;
            color: #c05621;
        }
        .badge-danger {
            background-color: #fed7d7;
            color: #c53030;
        }
        .badge-secondary {
            background-color: #e2e8f0;
            color: #4a5568;
        }
        @media print {
            body {
                font-size: 12px;
            }
            .container {
                border: none;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="clinic-name">Clinique Médicale</div>
            <div class="clinic-info">
                123 Avenue de la Santé, Bamako, Mali<br>
                Tél: +223 20 12 34 56 | Email: <EMAIL>
            </div>
        </div>

        <!-- Receipt Title -->
        <div class="receipt-title">Reçu de Paiement</div>

        <!-- Receipt Meta -->
        <div class="receipt-meta">
            <div class="meta-block">
                <div class="meta-label">FACTURE N°</div>
                <div class="meta-value">{{ $payment->invoice_number }}</div>
            </div>
            <div class="meta-block">
                <div class="meta-label">DATE</div>
                <div class="meta-value">{{ $payment->payment_date->format('d/m/Y') }}</div>
            </div>
            <div class="meta-block">
                <div class="meta-label">STATUT</div>
                <div class="meta-value">
                    @if($payment->status == 'completed')
                        <span class="badge badge-success">Payé</span>
                    @elseif($payment->status == 'pending')
                        <span class="badge badge-warning">En attente</span>
                    @elseif($payment->status == 'failed')
                        <span class="badge badge-danger">Échoué</span>
                    @elseif($payment->status == 'refunded')
                        <span class="badge badge-secondary">Remboursé</span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Patient Information -->
        <div class="patient-info">
            <div class="section-title">Informations du patient</div>
            <div class="info-row">
                <div class="info-label">Patient:</div>
                <div class="info-value">{{ $payment->appointment->patient->first_name }} {{ $payment->appointment->patient->last_name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">N° Patient:</div>
                <div class="info-value">{{ $payment->appointment->patient->patient_number }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Téléphone:</div>
                <div class="info-value">{{ $payment->appointment->patient->phone_number }}</div>
            </div>
            @if($payment->appointment->patient->email)
                <div class="info-row">
                    <div class="info-label">Email:</div>
                    <div class="info-value">{{ $payment->appointment->patient->email }}</div>
                </div>
            @endif
        </div>

        <!-- Service Details -->
        <div class="service-details">
            <div class="section-title">Détails de la prestation</div>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Date du RDV</th>
                        <th>Médecin</th>
                        <th>Montant</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ $payment->appointment->service->name }}</td>
                        <td>{{ $payment->appointment->appointment_datetime->format('d/m/Y H:i') }}</td>
                        <td>Dr. {{ $payment->appointment->doctor->user->first_name }} {{ $payment->appointment->doctor->user->last_name }}</td>
                        <td>{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</td>
                    </tr>
                </tbody>
            </table>

            <div class="amount-row">
                <div class="total-amount">Total: {{ number_format($payment->amount, 0, ',', ' ') }} FCFA</div>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="payment-info">
            <div class="section-title">Informations de paiement</div>
            <div class="info-row">
                <div class="info-label">Méthode de paiement:</div>
                <div class="info-value">
                    {{ $payment->payment_method == 'cash' ? 'Espèces' : 
                    ($payment->payment_method == 'mobile_money' ? 'Mobile Money' : 
                    ($payment->payment_method == 'card' ? 'Carte bancaire' : 
                    ($payment->payment_method == 'insurance' ? 'Assurance' : 'Autre'))) }}
                </div>
            </div>
            @if($payment->transaction_reference)
                <div class="info-row">
                    <div class="info-label">Référence de transaction:</div>
                    <div class="info-value">{{ $payment->transaction_reference }}</div>
                </div>
            @endif
            <div class="info-row">
                <div class="info-label">Date de paiement:</div>
                <div class="info-value">{{ $payment->payment_date->format('d/m/Y H:i') }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Reçu par:</div>
                <div class="info-value">{{ $payment->receiver->name }}</div>
            </div>
        </div>

        <!-- Signature Section -->
        <div class="signature">
            <div class="signature-block">
                <div class="signature-line"></div>
                <div>Signature du caissier</div>
            </div>
            <div class="signature-block">
                <div class="signature-line"></div>
                <div>Signature du patient</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Merci de votre confiance. Nous vous souhaitons un prompt rétablissement.</p>
            <p>Ce reçu a été généré automatiquement et ne nécessite pas de signature manuscrite pour être valide.</p>
        </div>
    </div>

    <!-- Print Button (only visible in browser, not in PDF) -->
    @if(!Request::is('*download*'))
        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button onclick="window.print()" style="background-color: #4299e1; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Imprimer le reçu</button>
        </div>
    @endif
</body>
</html> 