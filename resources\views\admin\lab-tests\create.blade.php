@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Nouveau Test de Laboratoire
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Créez un nouveau test pour le laboratoire.
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0">
                <a href="{{ route('admin.lab-tests') }}" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    Retour
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="mt-8 bg-white shadow overflow-hidden rounded-lg">
            <form action="{{ route('admin.lab-tests.store') }}" method="POST" class="p-6">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Nom -->
                    <div class="form-control">
                        <label for="name" class="label">
                            <span class="label-text">Nom du test <span class="text-error">*</span></span>
                        </label>
                        <input type="text" name="name" id="name" class="input input-bordered @error('name') input-error @enderror" value="{{ old('name') }}" required>
                        @error('name')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <!-- Code -->
                    <div class="form-control">
                        <label for="test_code" class="label">
                            <span class="label-text">Code du test <span class="text-error">*</span></span>
                        </label>
                        <input type="text" name="test_code" id="test_code" class="input input-bordered @error('test_code') input-error @enderror" value="{{ old('test_code') }}" required>
                        @error('test_code')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <!-- Catégorie -->
                    <div class="form-control">
                        <label for="category" class="label">
                            <span class="label-text">Catégorie <span class="text-error">*</span></span>
                        </label>
                        <select name="category" id="category" class="select select-bordered @error('category') select-error @enderror" required>
                            <option value="">Sélectionner une catégorie</option>
                            @foreach($categories as $category)
                                <option value="{{ $category }}" {{ old('category') == $category ? 'selected' : '' }}>{{ $category }}</option>
                            @endforeach
                        </select>
                        @error('category')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <!-- Prix -->
                    <div class="form-control">
                        <label for="price" class="label">
                            <span class="label-text">Prix (FCFA) <span class="text-error">*</span></span>
                        </label>
                        <input type="number" name="price" id="price" class="input input-bordered @error('price') input-error @enderror" value="{{ old('price') }}" min="0" step="100" required>
                        @error('price')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <!-- Unité -->
                    <div class="form-control">
                        <label for="unit" class="label">
                            <span class="label-text">Unité de mesure</span>
                        </label>
                        <input type="text" name="unit" id="unit" class="input input-bordered @error('unit') input-error @enderror" value="{{ old('unit') }}">
                        @error('unit')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <!-- Plage normale -->
                    <div class="form-control">
                        <label for="normal_range" class="label">
                            <span class="label-text">Plage normale</span>
                        </label>
                        <input type="text" name="normal_range" id="normal_range" class="input input-bordered @error('normal_range') input-error @enderror" value="{{ old('normal_range') }}">
                        @error('normal_range')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <!-- Ordre de tri -->
                    <div class="form-control">
                        <label for="sort_order" class="label">
                            <span class="label-text">Ordre de tri</span>
                        </label>
                        <input type="number" name="sort_order" id="sort_order" class="input input-bordered @error('sort_order') input-error @enderror" value="{{ old('sort_order', 0) }}" min="0">
                        @error('sort_order')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <!-- Actif -->
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Test actif</span>
                            <input type="checkbox" name="is_active" class="toggle toggle-primary" {{ old('is_active', true) ? 'checked' : '' }} value="1">
                        </label>
                        @error('is_active')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div class="form-control mt-6">
                    <label for="description" class="label">
                        <span class="label-text">Description</span>
                    </label>
                    <textarea name="description" id="description" class="textarea textarea-bordered h-24 @error('description') textarea-error @enderror">{{ old('description') }}</textarea>
                    @error('description')
                        <label class="label">
                            <span class="label-text-alt text-error">{{ $message }}</span>
                        </label>
                    @enderror
                </div>

                <div class="mt-6 flex justify-end">
                    <button type="submit" class="btn btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        Créer le test
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection