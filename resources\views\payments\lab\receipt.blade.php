<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> de Paiement - Analyses Laboratoire</title>
    <style>
        body {
            font-family: 'Deja<PERSON><PERSON> Sans', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .receipt-title {
            background: linear-gradient(135deg, #2563eb, #7c3aed);
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .info-row {
            display: table-row;
        }
        
        .info-label {
            display: table-cell;
            font-weight: bold;
            padding: 5px 10px 5px 0;
            width: 30%;
            color: #374151;
        }
        
        .info-value {
            display: table-cell;
            padding: 5px 0;
            color: #111827;
        }
        
        .section-title {
            background-color: #f3f4f6;
            padding: 10px;
            font-weight: bold;
            color: #374151;
            border-left: 4px solid #2563eb;
            margin: 20px 0 10px 0;
        }
        
        .analyses-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .analyses-table th {
            background-color: #2563eb;
            color: white;
            padding: 10px;
            text-align: left;
            font-weight: bold;
        }
        
        .analyses-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .analyses-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        
        .urgency-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        
        .urgency-urgent {
            background-color: #f59e0b;
        }
        
        .urgency-stat {
            background-color: #ef4444;
        }
        
        .total-section {
            background-color: #f0f9ff;
            border: 2px solid #2563eb;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .total-amount {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin: 10px 0;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 11px;
        }
        
        .signature-section {
            margin-top: 40px;
            display: table;
            width: 100%;
        }
        
        .signature-box {
            display: table-cell;
            width: 50%;
            text-align: center;
            padding: 20px;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 5px;
            font-size: 11px;
        }
        
        .qr-section {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background-color: #f9fafb;
            border: 1px dashed #d1d5db;
        }
        
        .instructions {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .instructions-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 10px;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
            color: #92400e;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <!-- En-tête -->
    <div class="header">
        <div class="logo">🏥 GlobalCare Solutions</div>
        <div class="subtitle">Système de Gestion Hospitalière</div>
        <div class="subtitle">Mali - Bamako</div>
    </div>

    <!-- Titre du reçu -->
    <div class="receipt-title">
        💉 REÇU DE PAIEMENT - ANALYSES DE LABORATOIRE
    </div>

    <!-- Informations du reçu -->
    <div class="info-section">
        <div class="section-title">📋 Informations du Reçu</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">N° de Reçu:</div>
                <div class="info-value">{{ $labPayment->payment_number }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Date de Paiement:</div>
                <div class="info-value">{{ $labPayment->payment_date->format('d/m/Y à H:i') }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">N° Prescription:</div>
                <div class="info-value">{{ $labPayment->prescription_number }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Reçu par:</div>
                <div class="info-value">{{ $labPayment->receiver->name }}</div>
            </div>
        </div>
    </div>

    <!-- Informations Patient -->
    <div class="info-section">
        <div class="section-title">👤 Informations Patient</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Nom Complet:</div>
                <div class="info-value">{{ $labPayment->patient->first_name }} {{ $labPayment->patient->last_name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">N° Patient:</div>
                <div class="info-value">{{ $labPayment->patient->patient_number }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Téléphone:</div>
                <div class="info-value">{{ $labPayment->patient->phone_number }}</div>
            </div>
            @if($labPayment->patient->date_of_birth)
            <div class="info-row">
                <div class="info-label">Âge:</div>
                <div class="info-value">{{ $labPayment->patient->getAge() }} ans</div>
            </div>
            @endif
        </div>
    </div>

    <!-- Détails des Analyses -->
    <div class="info-section">
        <div class="section-title">🧪 Analyses Prescrites</div>
        <table class="analyses-table">
            <thead>
                <tr>
                    <th style="width: 40%">Analyse</th>
                    <th style="width: 15%">Code</th>
                    <th style="width: 15%">Priorité</th>
                    <th style="width: 15%">Prix (FCFA)</th>
                    <th style="width: 15%">Sous-total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($labPayment->lab_tests as $test)
                <tr>
                    <td>{{ $test['name'] }}</td>
                    <td>{{ $test['code'] }}</td>
                    <td>
                        @if(isset($test['urgency']) && $test['urgency'] !== 'normal')
                            <span class="urgency-badge urgency-{{ $test['urgency'] }}">
                                {{ strtoupper($test['urgency']) }}
                            </span>
                        @else
                            Normal
                        @endif
                    </td>
                    <td style="text-align: right">{{ number_format($test['price'], 0, ',', ' ') }}</td>
                    <td style="text-align: right">{{ number_format($test['price'], 0, ',', ' ') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Total -->
    <div class="total-section">
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px;">
            TOTAL À PAYER
        </div>
        <div class="total-amount">
            {{ number_format($labPayment->total_amount, 0, ',', ' ') }} FCFA
        </div>
        <div style="font-size: 12px; color: #6b7280;">
            ({{ count($labPayment->lab_tests) }} analyse(s))
        </div>
    </div>

    <!-- Informations de Paiement -->
    <div class="info-section">
        <div class="section-title">💳 Détails du Paiement</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Méthode:</div>
                <div class="info-value">{{ $labPayment->payment_method_label }}</div>
            </div>
            @if($labPayment->transaction_reference)
            <div class="info-row">
                <div class="info-label">Référence:</div>
                <div class="info-value">{{ $labPayment->transaction_reference }}</div>
            </div>
            @endif
            <div class="info-row">
                <div class="info-label">Statut:</div>
                <div class="info-value">{{ $labPayment->status_label }}</div>
            </div>
            @if($labPayment->notes)
            <div class="info-row">
                <div class="info-label">Notes:</div>
                <div class="info-value">{{ $labPayment->notes }}</div>
            </div>
            @endif
        </div>
    </div>

    <!-- Instructions pour le patient -->
    <div class="instructions">
        <div class="instructions-title">📋 Instructions pour le Patient</div>
        <ul>
            <li>Présentez-vous au laboratoire avec ce reçu</li>
            <li>Le technicien effectuera les prélèvements selon les analyses prescrites</li>
            <li>Les résultats seront disponibles selon les délais habituels</li>
            <li>Conservez ce reçu jusqu'à la réception des résultats</li>
        </ul>
    </div>

    <!-- QR Code (placeholder) -->
    <div class="qr-section">
        <div style="font-weight: bold; margin-bottom: 10px;">Code de Vérification</div>
        <div style="font-family: monospace; font-size: 10px;">
            {{ $labPayment->payment_number }}
        </div>
        <div style="font-size: 10px; color: #6b7280; margin-top: 5px;">
            Scannez pour vérifier l'authenticité
        </div>
    </div>

    <!-- Signatures -->
    <div class="signature-section">
        <div class="signature-box">
            <div style="font-weight: bold; margin-bottom: 10px;">Patient / Représentant</div>
            <div class="signature-line">Signature</div>
        </div>
        <div class="signature-box">
            <div style="font-weight: bold; margin-bottom: 10px;">Réceptionniste</div>
            <div class="signature-line">{{ $labPayment->receiver->name }}</div>
        </div>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        <div>GlobalCare Solutions - Système de Gestion Hospitalière</div>
        <div>Reçu généré le {{ now()->format('d/m/Y à H:i') }}</div>
        <div style="margin-top: 10px; font-style: italic;">
            "Votre santé, notre priorité"
        </div>
    </div>
</body>
</html>
