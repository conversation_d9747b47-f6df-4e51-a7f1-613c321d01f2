<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleItem extends Model
{
    protected $fillable = [
        'sale_id',
        'medication_inventory_id',
        'quantity',
        'unit_price',
        'total_price',
        'insurance_coverage_percentage',
        'insurance_discount'
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'insurance_coverage_percentage' => 'decimal:2',
        'insurance_discount' => 'decimal:2'
    ];

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function medicationInventory(): BelongsTo
    {
        return $this->belongsTo(MedicationInventory::class);
    }
}
