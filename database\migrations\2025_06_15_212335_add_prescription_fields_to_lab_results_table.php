<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lab_results', function (Blueprint $table) {
            if (!Schema::hasColumn('lab_results', 'prescription_number')) {
                $table->string('prescription_number')->nullable()->after('result_number');
            }
            if (!Schema::hasColumn('lab_results', 'clinical_information')) {
                $table->text('clinical_information')->nullable()->after('interpretation');
            }
            if (!Schema::hasColumn('lab_results', 'urgency')) {
                $table->enum('urgency', ['normal', 'urgent', 'stat'])->default('normal')->after('notes');
            }
            $table->foreignId('performed_by')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lab_results', function (Blueprint $table) {
            $table->dropColumn(['prescription_number', 'clinical_information', 'urgency']);
            $table->foreignId('performed_by')->nullable(false)->change();
        });
    }
};
