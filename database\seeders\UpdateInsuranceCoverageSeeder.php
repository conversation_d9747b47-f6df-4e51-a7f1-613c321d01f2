<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\InsuranceCoverage;

class UpdateInsuranceCoverageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Mettre à jour les types d'assurance existants
        $insuranceUpdates = [
            [
                'insurance_type' => 'AMO',
                'coverage_type' => 'fixed',
                'coverage_percentage' => 70.00,
                'min_coverage_percentage' => null,
                'max_coverage_percentage' => null,
                'description' => 'Assurance Maladie Obligatoire - Couverture fixe de 70% pour les salariés du secteur privé',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'CNOPS',
                'coverage_type' => 'fixed',
                'coverage_percentage' => 80.00,
                'min_coverage_percentage' => null,
                'max_coverage_percentage' => null,
                'description' => 'Caisse Nationale des Organismes de Prévoyance Sociale - Couverture fixe de 80% pour les fonctionnaires',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'CNSS',
                'coverage_type' => 'fixed',
                'coverage_percentage' => 70.00,
                'min_coverage_percentage' => null,
                'max_coverage_percentage' => null,
                'description' => 'Caisse Nationale de Sécurité Sociale - Couverture fixe de 70% pour les salariés du secteur privé',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'RAMED',
                'coverage_type' => 'fixed',
                'coverage_percentage' => 90.00,
                'min_coverage_percentage' => null,
                'max_coverage_percentage' => null,
                'description' => 'Régime d\'Assistance Médicale - Couverture fixe de 90% pour la population économiquement défavorisée',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'Privée',
                'coverage_type' => 'variable',
                'coverage_percentage' => null,
                'min_coverage_percentage' => 30.00,
                'max_coverage_percentage' => 90.00,
                'description' => 'Assurance privée - Couverture variable selon le contrat (30% à 90%)',
                'is_active' => true,
            ],
        ];

        foreach ($insuranceUpdates as $insuranceData) {
            DB::table('insurance_coverage')->updateOrInsert(
                ['insurance_type' => $insuranceData['insurance_type']],
                $insuranceData
            );
        }

        // Ajouter de nouveaux types d'assurance variables
        $newInsuranceTypes = [
            [
                'insurance_type' => 'Mutuelle',
                'coverage_type' => 'variable',
                'coverage_percentage' => null,
                'min_coverage_percentage' => 40.00,
                'max_coverage_percentage' => 80.00,
                'description' => 'Mutuelle complémentaire - Couverture variable selon le contrat (40% à 80%)',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'Assurance_Internationale',
                'coverage_type' => 'variable',
                'coverage_percentage' => null,
                'min_coverage_percentage' => 50.00,
                'max_coverage_percentage' => 100.00,
                'description' => 'Assurance internationale - Couverture variable selon le contrat (50% à 100%)',
                'is_active' => true,
            ],
        ];

        foreach ($newInsuranceTypes as $insuranceData) {
            DB::table('insurance_coverage')->updateOrInsert(
                ['insurance_type' => $insuranceData['insurance_type']],
                $insuranceData
            );
        }

        $this->command->info('Types d\'assurance mis à jour avec succès.');
    }
}
