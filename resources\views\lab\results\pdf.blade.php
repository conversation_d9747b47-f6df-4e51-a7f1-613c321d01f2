<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résultats du Bon de Travail #{{ $workOrder->work_order_number }}</title>
    <style>
        body {
            font-family: 'Helvetica', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .container {
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 24px;
            margin: 0;
            color: #000;
        }
        .header p {
            margin: 5px 0;
        }
        .info-section {
            margin-bottom: 20px;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
        }
        .info-section h3 {
            font-size: 16px;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        .info-grid {
            display: table;
            width: 100%;
        }
        .info-grid .row {
            display: table-row;
        }
        .info-grid .cell {
            display: table-cell;
            padding: 5px;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .results-table th, .results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #777;
        }
        .notes {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 3px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Résultats d'Analyses Médicales</h1>
            <p>Bon de Travail #{{ $workOrder->work_order_number }}</p>
            <p>Date de réception: {{ \Carbon\Carbon::parse($workOrder->created_at)->format('d/m/Y') }} | Date de complétion: {{ \Carbon\Carbon::parse($workOrder->completed_at)->format('d/m/Y') }}</p>
        </div>

        <div class="info-section">
            <h3>Informations du Patient</h3>
            <div class="info-grid">
                <div class="row">
                    <div class="cell"><strong>Nom:</strong> {{ $workOrder->patient->first_name }} {{ $workOrder->patient->last_name }}</div>
                    <div class="cell"><strong>Age:</strong> {{ $workOrder->patient->getAge() }} ans</div>
                </div>
                <div class="row">
                    <div class="cell"><strong>Sexe:</strong> {{ $workOrder->patient->gender }}</div>
                    <div class="cell"><strong>Téléphone:</strong> {{ $workOrder->patient->phone_number }}</div>
                </div>
            </div>
        </div>

        <div class="info-section">
            <h3>Détails des Résultats</h3>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>Test</th>
                        <th>Résultat</th>
                        <th>Interprétation</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($labResults as $result)
                        <tr>
                            <td>{{ $result->labTest->name ?? 'N/A' }}</td>
                            <td>{{ $result->result }}</td>
                            <td>{{ $result->interpretation ?? 'N/A' }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="3" style="text-align: center;">Aucun résultat disponible.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>

            @if($workOrder->technician_notes)
                <div class="notes">
                    <h4>Notes du Technicien</h4>
                    <p>{{ $workOrder->technician_notes }}</p>
                </div>
            @endif
        </div>

        <div class="footer">
            <p>Généré le {{ date('d/m/Y H:i') }}</p>
            @if($workOrder->assignedTechnician)
                <p>Technicien: {{ $workOrder->assignedTechnician->name }}</p>
            @endif
        </div>
    </div>
</body>
</html>