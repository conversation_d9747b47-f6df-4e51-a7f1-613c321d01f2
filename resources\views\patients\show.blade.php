@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header avec breadcrumb -->
    <div class="breadcrumbs text-sm mb-6">
        <ul>
            <li><a href="{{ route('dashboard') }}" class="link link-hover">Accueil</a></li>
            <li><a href="{{ route('patients.index') }}" class="link link-hover">Patients</a></li>
            <li class="font-semibold">{{ $patient->first_name }} {{ $patient->last_name }}</li>
        </ul>
    </div>

    <!-- Header du patient -->
    <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content mb-6">
        <div class="card-body">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div class="avatar placeholder">
                        <div class="bg-primary-content text-primary rounded-full w-20 h-20">
                            <span class="text-2xl font-bold">
                                {{ strtoupper(substr($patient->first_name, 0, 1) . substr($patient->last_name, 0, 1)) }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold">{{ $patient->first_name }} {{ $patient->last_name }}</h1>
                        <div class="flex flex-wrap gap-2 mt-2">
                            <div class="badge badge-accent badge-lg">{{ $patient->patient_number }}</div>
                            <div class="badge badge-outline badge-lg">{{ $age }} ans</div>
                            <div class="badge badge-outline badge-lg 
                                {{ $patient->gender == 'male' ? 'badge-info' : ($patient->gender == 'female' ? 'badge-secondary' : 'badge-neutral') }}">
                                {{ $patient->gender == 'male' ? 'Homme' : ($patient->gender == 'female' ? 'Femme' : 'Autre') }}
                            </div>
                            @if($patient->blood_group)
                                <div class="badge badge-error badge-lg">{{ $patient->blood_group }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                
                <!-- Actions rapides -->
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('appointments.create', ['patient_id' => $patient->id]) }}" class="btn btn-success">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Nouveau RDV
                    </a>
                    <a href="{{ route('payments.create') }}?patient_id={{ $patient->id }}" class="btn btn-warning">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        Paiement
                    </a>
                    <a href="{{ route('patients.edit', $patient) }}" class="btn btn-primary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Modifier
                    </a>
                    <div class="dropdown dropdown-end">
                        <div tabindex="0" role="button" class="btn btn-ghost">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                            </svg>
                        </div>
                        <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                            <li><a href="#" onclick="printPatientCard()">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                                Imprimer fiche
                            </a></li>
                            <li><a href="#" onclick="exportPatientData()">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Exporter données
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal avec onglets -->
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
        <!-- Contenu principal -->
        <div class="xl:col-span-3">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body p-0">
                    <!-- Onglets -->
                    <div role="tablist" class="tabs tabs-lifted tabs-lg">
                        <input type="radio" name="patient_tabs" role="tab" class="tab" aria-label="Vue d'ensemble" checked />
                        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
                            <!-- Vue d'ensemble -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Informations de contact -->
                                <div class="card bg-base-100 border border-base-300">
                                    <div class="card-body">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="badge badge-primary p-3">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                </svg>
                                            </div>
                                            <h3 class="card-title text-lg">Contact</h3>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-center gap-3">
                                                <svg class="w-4 h-4 text-base-content/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                </svg>
                                                <span class="font-medium">{{ $patient->phone_number }}</span>
                                            </div>
                                            @if($patient->email)
                                                <div class="flex items-center gap-3">
                                                    <svg class="w-4 h-4 text-base-content/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                    </svg>
                                                    <span>{{ $patient->email }}</span>
                                                </div>
                                            @endif
                                            @if($patient->address)
                                                <div class="flex items-start gap-3">
                                                    <svg class="w-4 h-4 text-base-content/60 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    <span class="text-sm">{{ $patient->address }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations médicales -->
                                <div class="card bg-base-100 border border-base-300">
                                    <div class="card-body">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="badge badge-error p-3">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            </div>
                                            <h3 class="card-title text-lg">Médical</h3>
                                        </div>
                                        <div class="space-y-4">
                                            @if($patient->allergies)
                                                <div>
                                                    <div class="text-sm font-medium text-error mb-1">⚠️ Allergies</div>
                                                    <div class="text-sm bg-error/10 p-2 rounded">{{ $patient->allergies }}</div>
                                                </div>
                                            @endif
                                            @if($patient->medical_history)
                                                <div>
                                                    <div class="text-sm font-medium mb-1">Antécédents</div>
                                                    <div class="text-sm text-base-content/70">{{ $patient->medical_history }}</div>
                                                </div>
                                            @endif
                                            @if(!$patient->allergies && !$patient->medical_history)
                                                <div class="text-center py-4 text-base-content/50">
                                                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                    <p class="text-sm">Aucune information médicale</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <input type="radio" name="patient_tabs" role="tab" class="tab" aria-label="Rendez-vous" />
                        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
                            <!-- Rendez-vous -->
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-xl font-bold">Historique des rendez-vous</h3>
                                <a href="{{ route('appointments.create', ['patient_id' => $patient->id]) }}" class="btn btn-primary btn-sm">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    Nouveau RDV
                                </a>
                            </div>
                            
                            @if($recentAppointments->count() > 0)
                                <div class="space-y-4">
                                    @foreach($recentAppointments as $appointment)
                                        <div class="card bg-base-100 border border-base-300 hover:shadow-md transition-shadow">
                                            <div class="card-body p-4">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center gap-4">
                                                        <div class="badge 
                                                            {{ $appointment->status == 'completed' ? 'badge-success' : ($appointment->status == 'cancelled' ? 'badge-error' : 'badge-warning') }}">
                                                            {{ $appointment->status == 'completed' ? 'Terminé' : ($appointment->status == 'cancelled' ? 'Annulé' : 'Programmé') }}
                                                        </div>
                                                        <div>
                                                            <div class="font-semibold">{{ $appointment->service->name }}</div>
                                                            <div class="text-sm text-base-content/70">
                                                                Dr. {{ $appointment->doctor->user->name }} • 
                                                                {{ $appointment->appointment_datetime->format('d/m/Y à H:i') }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex items-center gap-2">
                                                        @if($appointment->payment)
                                                            <div class="badge badge-success badge-outline">Payé</div>
                                                        @else
                                                            <div class="badge badge-warning badge-outline">Non payé</div>
                                                        @endif
                                                        <a href="{{ route('appointments.show', $appointment) }}" class="btn btn-ghost btn-sm">
                                                            Voir
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <svg class="w-16 h-16 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <h3 class="text-lg font-semibold mb-2">Aucun rendez-vous</h3>
                                    <p class="text-base-content/60 mb-4">Ce patient n'a pas encore de rendez-vous.</p>
                                    <a href="{{ route('appointments.create', ['patient_id' => $patient->id]) }}" class="btn btn-primary">
                                        Prendre un rendez-vous
                                    </a>
                                </div>
                            @endif
                        </div>

                        <input type="radio" name="patient_tabs" role="tab" class="tab" aria-label="Paiements" />
                        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
                            <!-- Paiements -->
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-xl font-bold">Historique des paiements</h3>
                                <a href="{{ route('payments.create') }}?patient_id={{ $patient->id }}" class="btn btn-warning btn-sm">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    Nouveau Paiement
                                </a>
                            </div>
                            
                            @if(isset($payments) && $payments->count() > 0)
                                <div class="space-y-4">
                                    @foreach($payments as $payment)
                                        <div class="card bg-base-100 border border-base-300">
                                            <div class="card-body p-4">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center gap-4">
                                                        <div class="badge badge-success">{{ number_format($payment->amount) }} FCFA</div>
                                                        <div>
                                                            <div class="font-semibold">{{ $payment->service ?? 'Service général' }}</div>
                                                            <div class="text-sm text-base-content/70">
                                                                {{ $payment->payment_method }} • {{ $payment->created_at->format('d/m/Y à H:i') }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex items-center gap-2">
                                                        <a href="{{ route('payments.receipt', $payment) }}" class="btn btn-ghost btn-sm">
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                            </svg>
                                                            Reçu
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <svg class="w-16 h-16 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    <h3 class="text-lg font-semibold mb-2">Aucun paiement</h3>
                                    <p class="text-base-content/60 mb-4">Ce patient n'a effectué aucun paiement.</p>
                                    <a href="{{ route('payments.create') }}?patient_id={{ $patient->id }}" class="btn btn-warning">
                                        Enregistrer un paiement
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Statistiques rapides -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="badge badge-info p-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="card-title">Statistiques</h3>
                    </div>
                    <div class="stats stats-vertical shadow">
                        <div class="stat">
                            <div class="stat-title">Total RDV</div>
                            <div class="stat-value text-primary">{{ $recentAppointments->count() }}</div>
                            <div class="stat-desc">Depuis l'inscription</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">RDV à venir</div>
                            <div class="stat-value text-secondary">{{ $upcomingAppointments->count() }}</div>
                            <div class="stat-desc">Programmés</div>
                        </div>
                        <div class="stat">
                            <div class="stat-title">Patient depuis</div>
                            <div class="stat-value text-accent text-lg">{{ $patient->created_at->diffForHumans() }}</div>
                            <div class="stat-desc">{{ $patient->created_at->format('d/m/Y') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact d'urgence -->
            @if($patient->emergency_contact_name || $patient->emergency_contact_phone)
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="badge badge-error p-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <h3 class="card-title">Contact d'urgence</h3>
                        </div>
                        <div class="space-y-3">
                            @if($patient->emergency_contact_name)
                                <div class="flex items-center gap-3">
                                    <svg class="w-4 h-4 text-base-content/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="font-medium">{{ $patient->emergency_contact_name }}</span>
                                </div>
                            @endif
                            @if($patient->emergency_contact_phone)
                                <div class="flex items-center gap-3">
                                    <svg class="w-4 h-4 text-base-content/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <span>{{ $patient->emergency_contact_phone }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Prochains rendez-vous -->
            @if($upcomingAppointments->count() > 0)
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="badge badge-success p-3">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <h3 class="card-title">Prochains RDV</h3>
                        </div>
                        <div class="space-y-3">
                            @foreach($upcomingAppointments->take(3) as $appointment)
                                <div class="alert alert-info">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <div class="font-semibold text-sm">{{ $appointment->service->name }}</div>
                                        <div class="text-xs">Dr. {{ $appointment->doctor->user->name }}</div>
                                        <div class="text-xs">{{ $appointment->appointment_datetime->format('d/m/Y à H:i') }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Actions rapides -->
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title mb-4">Actions rapides</h3>
                    <div class="space-y-2">
                        <a href="{{ route('appointments.create', ['patient_id' => $patient->id]) }}" class="btn btn-primary btn-block btn-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Nouveau RDV
                        </a>
                        <a href="{{ route('payments.create') }}?patient_id={{ $patient->id }}" class="btn btn-warning btn-block btn-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            Paiement
                        </a>
                        <a href="{{ route('patients.edit', $patient) }}" class="btn btn-ghost btn-block btn-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Modifier
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printPatientCard() {
    window.print();
}

function exportPatientData() {
    // Fonction pour exporter les données du patient
    alert('Fonctionnalité d\'export en cours de développement');
}
</script>
@endsection
