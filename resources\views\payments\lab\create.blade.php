@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="container mx-auto p-6">
        <!-- Header avec breadcrumb -->
        <div class="mb-8">
            <div class="breadcrumbs text-sm mb-4">
                <ul>
                    <li><a href="{{ route('receptionist.dashboard') }}" class="text-primary hover:text-primary-focus">🏠 Dashboard</a></li>
                    <li><a href="{{ route('payments.index') }}" class="text-primary hover:text-primary-focus">💰 Paiements</a></li>
                    <li class="text-base-content/70">💉 Paiement Analyses</li>
                </ul>
            </div>

            <!-- Hero Section -->
            <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content shadow-2xl">
                <div class="card-body text-center py-8">
                    <h1 class="text-4xl font-bold mb-4 text-white">
                        💉 Paiement Analyses
                    </h1>
                    <div class="badge badge-accent badge-lg font-mono mb-4">
                        📋 {{ $prescriptionNumber }}
                    </div>
                    <p class="text-white/90 text-lg">
                        Enregistrement du paiement pour les analyses de laboratoire prescrites
                    </p>
                </div>
            </div>
        </div>

        <!-- Layout principal -->
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
            <!-- Sidebar Patient & Résumé -->
            <div class="xl:col-span-1 space-y-6">
                <!-- Carte Patient -->
                <div class="card bg-base-100 shadow-2xl border border-primary/20">
                    <div class="card-body">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="card-title text-primary">
                                👤 Patient
                            </h3>
                            <div class="badge badge-primary badge-outline">ID: {{ $patient->id }}</div>
                        </div>

                        <!-- Avatar et nom -->
                        <div class="flex items-center space-x-4 mb-6">
                            <div class="avatar placeholder">
                                <div class="bg-gradient-to-br from-primary to-secondary text-primary-content rounded-full w-16 shadow-lg">
                                    <span class="text-xl font-bold">{{ substr($patient->first_name, 0, 1) }}{{ substr($patient->last_name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="font-bold text-lg text-gray-900">{{ $patient->first_name }} {{ $patient->last_name }}</div>
                                <div class="text-sm font-semibold text-gray-600">Patient GlobalCare</div>
                            </div>
                        </div>

                        <!-- Informations détaillées -->
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-success/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-success/20 rounded-full flex items-center justify-center">
                                        📞
                                    </div>
                                    <div>
                                        <div class="text-sm font-bold text-gray-600">Téléphone</div>
                                        <div class="font-semibold text-gray-900">{{ $patient->phone_number }}</div>
                                    </div>
                                </div>
                            </div>

                            @if($patient->date_of_birth)
                            <div class="flex items-center justify-between p-3 bg-info/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-info/20 rounded-full flex items-center justify-center">
                                        🎂
                                    </div>
                                    <div>
                                        <div class="text-sm font-bold text-gray-600">Âge</div>
                                        <div class="font-semibold text-gray-900">{{ $patient->getAge() }} ans</div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            @if($patient->patient_number)
                            <div class="flex items-center justify-between p-3 bg-secondary/10 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-secondary/20 rounded-full flex items-center justify-center">
                                        🆔
                                    </div>
                                    <div>
                                        <div class="text-sm font-bold text-gray-600">N° Patient</div>
                                        <div class="font-semibold font-mono text-gray-900">{{ $patient->patient_number }}</div>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Carte Résumé -->
                <div class="card bg-base-100 shadow-2xl border-2 border-primary">
                    <div class="card-body">
                        <h4 class="card-title justify-center text-primary mb-6">
                            📊 Résumé de la Commande
                        </h4>

                        <!-- Statistiques principales -->
                        <div class="grid grid-cols-1 gap-4 mb-6">
                            <div class="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl p-4 text-center border border-primary/20">
                                <div class="text-4xl mb-2">🧪</div>
                                <div class="text-3xl font-bold text-primary">{{ $labResults->count() }}</div>
                                <div class="text-base font-bold text-gray-700">Analyse(s) prescrite(s)</div>
                            </div>

                            <div class="bg-gradient-to-br from-secondary/10 to-accent/10 rounded-xl p-4 text-center border border-secondary/20">
                                <div class="text-4xl mb-2">💰</div>
                                <div class="text-3xl font-bold text-secondary">{{ number_format($totalAmount, 0, ',', ' ') }}</div>
                                <div class="text-base font-bold text-gray-700">FCFA à payer</div>
                            </div>
                        </div>

                        <!-- Prescription -->
                        <div class="bg-gradient-to-br from-accent/10 to-primary/10 rounded-xl p-4 text-center border border-accent/20">
                            <div class="text-base font-bold text-gray-700 mb-2">📋 Prescription N°</div>
                            <div class="font-mono font-bold text-gray-900 text-sm break-all">{{ $prescriptionNumber }}</div>
                        </div>

                        <!-- Badge de statut -->
                        <div class="flex justify-center mt-6">
                            <div class="badge badge-success badge-lg text-white font-bold">
                                ✅ Prêt pour paiement
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section principale -->
            <div class="xl:col-span-3 space-y-6">
                <!-- Analyses prescrites -->
                <div class="card bg-base-100 shadow-2xl border border-secondary/20">
                    <div class="card-body">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="card-title text-secondary text-xl">
                                🧪 Analyses Prescrites
                            </h3>
                            <div class="badge badge-secondary badge-lg">
                                {{ $labResults->count() }} analyse(s)
                            </div>
                        </div>

                        <!-- Grille des analyses -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            @foreach($labResults as $index => $result)
                                <div class="card bg-gradient-to-br from-base-200 to-base-300 shadow-lg hover:shadow-xl transition-all duration-300 border border-primary/10">
                                    <div class="card-body p-4">
                                        <div class="flex items-start justify-between mb-3">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold text-sm">
                                                    {{ $index + 1 }}
                                                </div>
                                                <div>
                                                    <h4 class="font-bold text-base">{{ $result->labTest->name }}</h4>
                                                    <div class="badge badge-outline badge-xs">{{ $result->labTest->test_code }}</div>
                                                </div>
                                            </div>

                                            @if($result->urgency !== 'normal')
                                                <div class="tooltip" data-tip="Priorité {{ ucfirst($result->urgency) }}">
                                                    <div class="badge
                                                        @if($result->urgency === 'urgent') badge-warning
                                                        @elseif($result->urgency === 'stat') badge-error
                                                        @endif badge-sm">
                                                        @if($result->urgency === 'urgent') ⚡
                                                        @elseif($result->urgency === 'stat') 🚨
                                                        @endif
                                                        {{ strtoupper($result->urgency) }}
                                                    </div>
                                                </div>
                                            @endif
                                        </div>

                                        @if($result->labTest->description)
                                            <p class="text-sm font-medium text-gray-600 mb-3 line-clamp-2">{{ $result->labTest->description }}</p>
                                        @endif

                                        <div class="flex items-center justify-between">
                                            <div class="text-xs opacity-60">
                                                @if($result->labTest->category ?? false)
                                                    <span class="badge badge-ghost badge-xs">{{ $result->labTest->category }}</span>
                                                @endif
                                            </div>
                                            <div class="text-right">
                                                <div class="text-lg font-bold text-primary">{{ number_format($result->labTest->price, 0, ',', ' ') }}</div>
                                                <div class="text-xs opacity-60">FCFA</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Total -->
                        <div class="divider divider-secondary"></div>
                        <div class="flex justify-between items-center bg-gradient-to-r from-secondary/10 to-accent/10 p-4 rounded-xl">
                            <span class="text-xl font-bold">💰 Total à payer</span>
                            <span class="text-3xl font-bold text-secondary">{{ number_format($totalAmount, 0, ',', ' ') }} FCFA</span>
                        </div>
                    </div>
                </div>

                <!-- Formulaire de paiement -->
                <div class="card bg-base-100 shadow-2xl border border-accent/20">
                    <div class="card-body">
                        <h3 class="card-title text-accent text-xl mb-6">
                            💳 Enregistrement du Paiement
                        </h3>

                        <form action="{{ route('payments.lab.store') }}" method="POST" class="space-y-6">
                            @csrf

                            <!-- Champs cachés -->
                            <input type="hidden" name="prescription_number" value="{{ $prescriptionNumber }}">
                            <input type="hidden" name="patient_id" value="{{ $patient->id }}">
                            <input type="hidden" name="total_amount" value="{{ $totalAmount }}">
                            <input type="hidden" name="appointment_id" value="{{ $labResults->first()->appointment_id }}">

                            <!-- Données des analyses -->
                            @foreach($labResults as $index => $result)
                                <input type="hidden" name="lab_tests[{{ $index }}][id]" value="{{ $result->labTest->id }}">
                                <input type="hidden" name="lab_tests[{{ $index }}][name]" value="{{ $result->labTest->name }}">
                                <input type="hidden" name="lab_tests[{{ $index }}][code]" value="{{ $result->labTest->test_code }}">
                                <input type="hidden" name="lab_tests[{{ $index }}][price]" value="{{ $result->labTest->price }}">
                                <input type="hidden" name="lab_tests[{{ $index }}][urgency]" value="{{ $result->urgency }}">
                                <input type="hidden" name="lab_tests[{{ $index }}][clinical_information]" value="{{ $result->clinical_information }}">
                            @endforeach

                            <!-- Méthodes de paiement -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text text-lg font-bold">
                                        💰 Méthode de paiement <span class="text-error">*</span>
                                    </span>
                                </label>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                    <label class="cursor-pointer">
                                        <input type="radio" name="payment_method" value="cash" class="radio radio-primary" required {{ old('payment_method') === 'cash' ? 'checked' : '' }}>
                                        <div class="card bg-gradient-to-br from-green-50 to-green-100 border-2 border-green-200 hover:border-green-400 transition-all duration-200 ml-3">
                                            <div class="card-body p-4 text-center">
                                                <div class="text-3xl mb-2">💵</div>
                                                <div class="font-bold">Espèces</div>
                                                <div class="text-sm opacity-70">Paiement cash</div>
                                            </div>
                                        </div>
                                    </label>

                                    <label class="cursor-pointer">
                                        <input type="radio" name="payment_method" value="mobile_money" class="radio radio-primary" {{ old('payment_method') === 'mobile_money' ? 'checked' : '' }}>
                                        <div class="card bg-gradient-to-br from-orange-50 to-orange-100 border-2 border-orange-200 hover:border-orange-400 transition-all duration-200 ml-3">
                                            <div class="card-body p-4 text-center">
                                                <div class="text-3xl mb-2">📱</div>
                                                <div class="font-bold">Mobile Money</div>
                                                <div class="text-sm opacity-70">Orange Money, etc.</div>
                                            </div>
                                        </div>
                                    </label>

                                    <label class="cursor-pointer">
                                        <input type="radio" name="payment_method" value="card" class="radio radio-primary" {{ old('payment_method') === 'card' ? 'checked' : '' }}>
                                        <div class="card bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200 hover:border-blue-400 transition-all duration-200 ml-3">
                                            <div class="card-body p-4 text-center">
                                                <div class="text-3xl mb-2">💳</div>
                                                <div class="font-bold">Carte</div>
                                                <div class="text-sm opacity-70">Carte bancaire</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                                    <label class="cursor-pointer">
                                        <input type="radio" name="payment_method" value="insurance" class="radio radio-primary" {{ old('payment_method') === 'insurance' ? 'checked' : '' }}>
                                        <div class="card bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-200 hover:border-purple-400 transition-all duration-200 ml-3">
                                            <div class="card-body p-4 text-center">
                                                <div class="text-3xl mb-2">🏥</div>
                                                <div class="font-bold">Assurance</div>
                                                <div class="text-sm opacity-70">Prise en charge</div>
                                            </div>
                                        </div>
                                    </label>

                                    <label class="cursor-pointer">
                                        <input type="radio" name="payment_method" value="credit" class="radio radio-primary" {{ old('payment_method') === 'credit' ? 'checked' : '' }}>
                                        <div class="card bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200 hover:border-gray-400 transition-all duration-200 ml-3">
                                            <div class="card-body p-4 text-center">
                                                <div class="text-3xl mb-2">📋</div>
                                                <div class="font-bold">Crédit</div>
                                                <div class="text-sm opacity-70">Paiement différé</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                @error('payment_method')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Référence transaction -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-semibold">
                                        🔗 Référence transaction
                                    </span>
                                </label>
                                <input type="text" name="transaction_reference"
                                    value="{{ old('transaction_reference') }}"
                                    placeholder="Ex: TXN123456789 (optionnel)"
                                    class="input input-bordered input-accent">
                                <label class="label">
                                    <span class="label-text-alt">Pour Mobile Money ou carte bancaire</span>
                                </label>
                                @error('transaction_reference')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Notes -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-semibold">
                                        📝 Notes complémentaires
                                    </span>
                                </label>
                                <textarea name="notes" rows="3"
                                    placeholder="Remarques particulières sur ce paiement..."
                                    class="textarea textarea-bordered textarea-accent">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Récapitulatif final -->
                            <div class="alert alert-info">
                                <svg class="w-6 h-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h3 class="font-bold">Récapitulatif</h3>
                                    <div class="text-sm">
                                        Patient: <strong>{{ $patient->first_name }} {{ $patient->last_name }}</strong><br>
                                        Analyses: <strong>{{ $labResults->count() }}</strong> •
                                        Total: <strong>{{ number_format($totalAmount, 0, ',', ' ') }} FCFA</strong>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex flex-col sm:flex-row gap-4 justify-between pt-6">
                                <a href="{{ route('payments.lab.create') }}" class="btn btn-ghost btn-lg">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                                    </svg>
                                    Retour
                                </a>

                                <button type="submit" class="btn btn-primary btn-lg btn-wide">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                    💰 Confirmer le Paiement
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Animation d'entrée pour les cartes
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Gestion des méthodes de paiement
        const paymentRadios = document.querySelectorAll('input[name="payment_method"]');
        const transactionField = document.querySelector('input[name="transaction_reference"]');

        paymentRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Mise à jour visuelle des cartes
                paymentRadios.forEach(r => {
                    const card = r.closest('label').querySelector('.card');
                    if (r.checked) {
                        card.classList.add('ring-4', 'ring-primary', 'ring-opacity-50');
                        card.classList.remove('border-gray-200');
                    } else {
                        card.classList.remove('ring-4', 'ring-primary', 'ring-opacity-50');
                        card.classList.add('border-gray-200');
                    }
                });

                // Affichage conditionnel du champ référence
                if (this.value === 'mobile_money' || this.value === 'card') {
                    transactionField.closest('.form-control').style.display = 'block';
                    transactionField.setAttribute('placeholder',
                        this.value === 'mobile_money' ?
                        'Ex: OM123456789 ou WAVE123456' :
                        'Ex: CARD123456789'
                    );
                } else {
                    transactionField.closest('.form-control').style.display = 'none';
                }
            });
        });

        // Validation du formulaire avec modal de confirmation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const paymentMethod = document.querySelector('input[name="payment_method"]:checked');
            if (!paymentMethod) {
                alert('⚠️ Veuillez sélectionner une méthode de paiement.');
                return false;
            }

            // Modal de confirmation moderne
            const modal = document.createElement('div');
            modal.className = 'modal modal-open';
            modal.innerHTML = `
                <div class="modal-box">
                    <h3 class="font-bold text-lg">💰 Confirmer le Paiement</h3>
                    <div class="py-4">
                        <div class="stats shadow w-full">
                            <div class="stat">
                                <div class="stat-title">Patient</div>
                                <div class="stat-value text-lg">{{ $patient->first_name }} {{ $patient->last_name }}</div>
                            </div>
                            <div class="stat">
                                <div class="stat-title">Montant</div>
                                <div class="stat-value text-primary">{{ number_format($totalAmount, 0, ",", " ") }} FCFA</div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <strong>Méthode:</strong> ${paymentMethod.nextElementSibling.querySelector('.font-bold').textContent}
                        </div>
                    </div>
                    <div class="modal-action">
                        <button type="button" class="btn btn-ghost" onclick="this.closest('.modal').remove()">
                            Annuler
                        </button>
                        <button type="button" class="btn btn-primary" onclick="document.querySelector('form').submit()">
                            ✅ Confirmer
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        });

        // Effets hover sur les cartes d'analyses
        const analysisCards = document.querySelectorAll('.card');
        analysisCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Auto-scroll vers le formulaire après animation
        setTimeout(() => {
            const paymentForm = document.querySelector('form').closest('.card');
            if (paymentForm) {
                paymentForm.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }, 1000);
    });
</script>

<style>
    /* Animations personnalisées */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    /* Style pour les radio buttons */
    input[type="radio"]:checked + .card {
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    /* Styles supplémentaires */
    .hero {
        background: linear-gradient(135deg, hsl(var(--p)) 0%, hsl(var(--s)) 100%);
    }
</style>
@endpush
@endsection
