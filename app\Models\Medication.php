<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Medication extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'generic_name',
        'code',
        'description',
        'manufacturer',
        'unit_price',
        'dosage_form',
        'strength',
        'prescription_required',
        'is_active',
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'prescription_required' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the prescription items that include this medication.
     */
    public function prescriptionItems(): HasMany
    {
        return $this->hasMany(PrescriptionItem::class);
    }

    /**
     * Get the inventory records for this medication.
     */
    public function inventories(): HasMany
    {
        return $this->hasMany(MedicationInventory::class);
    }

    /**
     * Get the current stock level of the medication.
     */
    public function getCurrentStockAttribute(): int
    {
        return $this->inventories()->sum('quantity');
    }
}