<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PrescriptionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_id',
        'medication_id',
        'medication_name',
        'medication_form',
        'dosage',
        'frequency',
        'duration',
        'quantity',
        'unit_price',
        'total_price',
        'insurance_discount',
        'final_price',
        'quantity_dispensed',
        'instructions',
        'status',
        'dispensed_by',
        'dispensed_at',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'insurance_discount' => 'decimal:2',
        'final_price' => 'decimal:2',
        'quantity_dispensed' => 'integer',
        'dispensed_at' => 'datetime',
    ];

    /**
     * Get the prescription associated with the prescription item.
     */
    public function prescription(): BelongsTo
    {
        return $this->belongsTo(Prescription::class);
    }

    /**
     * Get the medication associated with the prescription item.
     */
    public function medication(): BelongsTo
    {
        return $this->belongsTo(Medication::class);
    }

    /**
     * Get the user who dispensed the medication.
     */
    public function dispenser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'dispensed_by');
    }

    /**
     * Calculate the total price based on quantity and unit price.
     */
    public function calculateTotalPrice(): void
    {
        $this->total_price = $this->quantity_dispensed * $this->unit_price;
    }

    /**
     * Apply insurance discount based on patient's insurance coverage.
     */
    public function applyInsuranceDiscount(float $coveragePercentage): void
    {
        $this->insurance_discount = $this->total_price * ($coveragePercentage / 100);
        $this->final_price = $this->total_price - $this->insurance_discount;
    }

    /**
     * Calculate final price without insurance.
     */
    public function calculateFinalPrice(): void
    {
        $this->final_price = $this->total_price - $this->insurance_discount;
    }
}