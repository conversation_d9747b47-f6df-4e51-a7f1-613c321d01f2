<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Prescription extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_number',
        'appointment_id',
        'patient_id',
        'doctor_id',
        'diagnosis',
        'notes',
        'prescription_date',
        'status',
        'dispensed_at',
    ];

    protected $casts = [
        'prescription_date' => 'date',
    ];

    /**
     * Get the appointment associated with the prescription.
     */
    public function appointment(): BelongsTo
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the patient associated with the prescription.
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the doctor associated with the prescription.
     */
    public function doctor(): BelongsTo
    {
        return $this->belongsTo(Doctor::class);
    }

    /**
     * Get the prescription items for the prescription.
     */
    public function prescriptionItems(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PrescriptionItem::class);
    }
}