<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            // Ajouter le champ NINA (Numéro d'Identification National d'Assurance)
            $table->string('nina', 20)
                  ->nullable()
                  ->after('insurance_number')
                  ->comment('Numéro d\'Identification National d\'Assurance');
            
            // Ajouter le pourcentage personnalisé pour les assurances variables
            $table->decimal('custom_coverage_percentage', 5, 2)
                  ->nullable()
                  ->after('nina')
                  ->comment('Pourcentage personnalisé pour assurance variable');
            
            // Ajouter le nom de l'assurance (compagnie/organisme)
            $table->string('insurance_name')
                  ->nullable()
                  ->after('insurance_type')
                  ->comment('Nom de la compagnie ou organisme d\'assurance');
            
            // Ajouter des champs pour une meilleure traçabilité
            $table->date('insurance_start_date')
                  ->nullable()
                  ->after('custom_coverage_percentage')
                  ->comment('Date de début de couverture');
            
            $table->text('insurance_notes')
                  ->nullable()
                  ->after('insurance_start_date')
                  ->comment('Notes sur l\'assurance du patient');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropColumn([
                'nina',
                'custom_coverage_percentage',
                'insurance_name',
                'insurance_start_date',
                'insurance_notes'
            ]);
        });
    }
};
