<?php

namespace App\Http\Controllers;

use App\Models\LabWorkOrder;
use App\Models\LabResult;
use App\Models\LabSample;
use App\Models\LabPayment;
use App\Models\Patient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LabTechnicianController extends Controller
{
/**
     * Display a listing of the resource.
     */
    public function indexWorkOrders(Request $request)
    {
        $query = LabWorkOrder::with(['patient', 'labPayment'])
            ->orderBy('created_at', 'desc');

        // Filtering
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('search_query')) {
            $search = $request->search_query;
            $query->where(function ($q) use ($search) {
                $q->where('work_order_number', 'like', "%{$search}%")
                    ->orWhereHas('patient', function ($q) use ($search) {
                        $q->where('first_name', 'like', "%{$search}%")
                          ->orWhere('last_name', 'like', "%{$search}%")
                          ->orWhere('patient_number', 'like', "%{$search}%");
                    });
            });
        }

        $workOrders = $query->paginate(15);

        return view('lab.work-order.index', compact('workOrders'));
    }
    /**
     * Show the lab technician dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        // Statistiques du jour
        $today = Carbon::today();
        
        // Analyses en attente (payées mais pas encore traitées)
        $pendingTests = LabWorkOrder::where('status', 'pending')
            ->orWhere('status', 'received')
            ->count();
        
        // Analyses terminées aujourd'hui
        $completedToday = LabWorkOrder::whereDate('completed_at', $today)
            ->where('status', 'completed')
            ->count();
        
        // Analyses urgentes en attente
        $urgentTests = LabWorkOrder::where('priority', 'urgent')
            ->orWhere('priority', 'stat')
            ->whereIn('status', ['pending', 'received', 'in_progress'])
            ->count();
        
        // Échantillons en attente de traitement
        $pendingSamples = LabSample::where('status', 'collected')
            ->count();
        
        // Bons de travail récents (dernières 24h)
        $recentWorkOrders = LabWorkOrder::with(['patient', 'labPayment'])
            ->whereIn('status', ['pending', 'received', 'in_progress'])
            ->orderBy('priority', 'desc')
            ->orderBy('received_at', 'asc')
            ->limit(10)
            ->get();
        
        // Résultats récents terminés
        $recentResults = LabWorkOrder::with(['patient', 'labPayment'])
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc')
            ->limit(5)
            ->get();
        
        return view('dashboard.lab-technician', compact(
            'pendingTests',
            'completedToday', 
            'urgentTests',
            'pendingSamples',
            'recentWorkOrders',
            'recentResults'
        ));
    }
    
    /**
     * Search for patient by receipt number or patient info.
     */
    public function searchPatient(Request $request)
    {
        $query = $request->get('q');
        
        if (!$query) {
            return response()->json([]);
        }
        
        // Recherche par numéro de reçu de paiement
        $labPayment = LabPayment::with(['patient', 'workOrder'])
            ->where('payment_number', 'LIKE', "%{$query}%")
            ->first();
        
        if ($labPayment) {
            return response()->json([
                'type' => 'payment',
                'data' => $labPayment
            ]);
        }
        
        // Recherche par numéro de prescription
        $workOrder = LabWorkOrder::with(['patient', 'labPayment'])
            ->where('prescription_number', 'LIKE', "%{$query}%")
            ->first();
        
        if ($workOrder) {
            return response()->json([
                'type' => 'work_order',
                'data' => $workOrder
            ]);
        }
        
        // Recherche par nom de patient
        $patients = Patient::where('first_name', 'LIKE', "%{$query}%")
            ->orWhere('last_name', 'LIKE', "%{$query}%")
            ->orWhere('patient_number', 'LIKE', "%{$query}%")
            ->with(['labWorkOrders' => function($query) {
                $query->whereIn('status', ['pending', 'received', 'in_progress'])
                      ->with('labPayment');
            }])
            ->limit(5)
            ->get();
        
        return response()->json([
            'type' => 'patients',
            'data' => $patients
        ]);
    }
    
    /**
     * Show work order details for processing.
     */
    public function showWorkOrder(LabWorkOrder $workOrder)
    {
        $workOrder->load([
            'patient',
            'labPayment',
            'samples',
            'assignedTechnician'
        ]);
        
        return view('lab.work-order.show', compact('workOrder'));
    }
    
    /**
     * Start processing a work order.
     */
    public function startWorkOrder(Request $request, LabWorkOrder $workOrder)
    {
        $user = Auth::user();
        
        if ($workOrder->status !== 'pending' && $workOrder->status !== 'received') {
            return back()->withErrors(['error' => 'Ce bon de travail ne peut pas être démarré.']);
        }
        
        $workOrder->update([
            'status' => 'in_progress',
            'assigned_to' => $user->id,
            'started_at' => now()
        ]);
        
        return redirect()->route('lab.samples.create', $workOrder)
            ->with('success', 'Bon de travail démarré. Procédez aux prélèvements.');
    }
    
    /**
     * Show sample collection form.
     */
    public function createSamples(LabWorkOrder $workOrder)
    {
        $workOrder->load(['patient', 'labPayment']);
        
        // Analyser les tests requis pour déterminer les échantillons nécessaires
        $labTestsDetails = $workOrder->lab_tests_details;
        $requiredSamples = $this->determineRequiredSamples($labTestsDetails);
        
        return view('lab.samples.create', compact('workOrder', 'requiredSamples'));
    }
    
    /**
     * Store sample collection data.
     */
    public function storeSamples(Request $request, LabWorkOrder $workOrder)
    {
        try {
            DB::beginTransaction();
            
            $user = Auth::user();
            
            // Traiter chaque échantillon
            foreach ($request->samples as $key => $sampleData) {
                $sampleCode = 'SAMP-' . date('Ymd') . '-' . random_int(1000, 9999);
                
                // Créer l'échantillon avec un type valide (blood par défaut)
                LabSample::create([
                    'sample_code' => $sampleCode,
                    'lab_work_order_id' => $workOrder->id,
                    'patient_id' => $workOrder->patient_id,
                    'sample_type' => 'blood', // Valeur par défaut sûre
                    'container_type' => $sampleData['container'],
                    'volume' => $sampleData['volume'] ?? null,
                    'status' => 'collected',
                    'collected_at' => now(),
                    'collected_by' => $user->id,
                    'collection_notes' => $sampleData['notes'] ?? null,
                ]);
            }
            
            // Mettre à jour le statut du bon de travail
            $workOrder->update([
                'status' => 'in_progress'
            ]);
            
            DB::commit();
            
            // Rediriger vers la page de saisie des résultats
            return redirect()->route('lab.results.create', $workOrder)
                ->with('success', 'Échantillons collectés avec succès. Procédez aux analyses.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Erreur échantillons: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Erreur: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Show results entry form.
     */
    public function createResults(LabWorkOrder $workOrder)
    {
        $workOrder->load(['patient', 'labPayment', 'samples']);

        // Récupérer les résultats de laboratoire associés
        $labResults = LabResult::where('prescription_number', $workOrder->prescription_number)
            ->with('labTest')
            ->get();

        // S'il n'y a pas de résultats, on les crée à partir des tests du bon de travail
        if ($labResults->isEmpty() && is_array($workOrder->lab_tests_details)) {
            $labResults = collect();
            foreach ($workOrder->lab_tests_details as $test) {
                // On cherche le test en base
                $labTest = null;
                if (isset($test['id'])) {
                    $labTest = \App\Models\LabTest::find($test['id']);
                } elseif (isset($test['code'])) {
                    $labTest = \App\Models\LabTest::where('test_code', $test['code'])->first();
                }
                if ($labTest) {
                    $resultNumber = 'LAB-' . date('Ymd') . '-' . random_int(1000, 9999);
                    $labResult = LabResult::create([
                        'result_number' => $resultNumber,
                        'prescription_number' => $workOrder->prescription_number,
                        'appointment_id' => $workOrder->labPayment->appointment_id ?? null,
                        'patient_id' => $workOrder->patient_id,
                        'doctor_id' => $workOrder->labPayment->appointment->doctor_id ?? null,
                        'lab_test_id' => $labTest->id,
                        'clinical_information' => $test['clinical_information'] ?? null,
                        'urgency' => $test['urgency'] ?? 'normal',
                        'notes' => $test['notes'] ?? null,
                        'status' => 'ordered',
                        'ordered_at' => now(),
                    ]);
                    $labResults->push($labResult->load('labTest'));
                }
            }
        }

        return view('lab.results.create', compact('workOrder', 'labResults'));
    }
    
    /**
     * Store analysis results.
     */
    public function storeResults(Request $request, LabWorkOrder $workOrder)
    {
        $validated = $request->validate([
            'results' => 'required|array',
            'results.*.lab_result_id' => 'required|exists:lab_results,id',
            'results.*.result' => 'required|string',
            'results.*.interpretation' => 'nullable|string',
            'technician_notes' => 'nullable|string',
        ]);
        
        DB::beginTransaction();
        
        try {
            $user = Auth::user();
            
            // Mettre à jour les résultats
            foreach ($validated['results'] as $resultData) {
                LabResult::where('id', $resultData['lab_result_id'])
                    ->update([
                        'result' => $resultData['result'],
                        'interpretation' => $resultData['interpretation'] ?? null,
                        'performed_by' => $user->id,
                        'status' => 'completed',
                        'completed_at' => now(),
                    ]);
            }
            
            // Mettre à jour le bon de travail
            $workOrder->update([
                'status' => 'completed',
                'completed_at' => now(),
                'technician_notes' => $validated['technician_notes'] ?? null,
            ]);
            
            DB::commit();
            
            return redirect()->route('lab-technician.dashboard')
                ->with('success', 'Résultats enregistrés avec succès. Analyses terminées.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors([
                'error' => 'Erreur lors de l\'enregistrement: ' . $e->getMessage()
            ])->withInput();
        }
    }
/**
     * Display the specified resource.
     */
    public function showResults(LabWorkOrder $workOrder)
    {
        $workOrder->load('patient', 'labPayment', 'samples', 'assignedTechnician');
        
        // Charger les résultats de laboratoire séparément
        $labResults = LabResult::where('prescription_number', $workOrder->prescription_number)
            ->with('labTest')
            ->get();

        return view('lab.results.show', compact('workOrder', 'labResults'));
    }
/**
     * Download the specified resource as PDF.
     */
    public function downloadPdf(LabWorkOrder $workOrder)
    {
        $workOrder->load('patient', 'labPayment', 'samples', 'assignedTechnician');
        
        // Charger les résultats de laboratoire séparément
        $labResults = LabResult::where('prescription_number', $workOrder->prescription_number)
            ->with('labTest')
            ->get();

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('lab.results.pdf', compact('workOrder', 'labResults'));
        
        return $pdf->stream('resultats-labo-' . $workOrder->id . '.pdf');
    }
    
    /**
     * Determine required samples based on lab tests.
     */
    private function determineRequiredSamples($labTestsDetails)
    {
        $sampleTypes = [];
        
        // Ajouter par défaut un échantillon de sang
        $sampleTypes['blood'] = [
            'type' => 'Sang',
            'container' => 'Tube EDTA',
            'volume' => 5
        ];
        
        // Parcourir les tests pour ajouter d'autres types d'échantillons si nécessaire
        foreach ($labTestsDetails as $test) {
            $testName = strtolower($test['name'] ?? '');
            
            if (str_contains($testName, 'urine')) {
                $sampleTypes['urine_sample'] = [
                    'type' => 'Urine',
                    'container' => 'Pot stérile',
                    'volume' => 50
                ];
            }
            
            if (str_contains($testName, 'selle')) {
                $sampleTypes['stool_sample'] = [
                    'type' => 'Selles',
                    'container' => 'Pot stérile',
                    'volume' => null
                ];
            }
        }
        
        return $sampleTypes;
    }
}
