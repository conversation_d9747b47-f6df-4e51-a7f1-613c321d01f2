@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> de Vente - ' . $prescription->prescription_number)

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Re<PERSON>u de Vente</h1>
            <p class="text-gray-600 mt-1">{{ $prescription->prescription_number }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('pharmacist.prescriptions.receipt.download', $prescription) }}" class="btn btn-outline btn-info">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Télécharger PDF
            </a>
            <button onclick="window.print()" class="btn btn-outline btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Imprimer
            </a>
            <a href="{{ route('pharmacist.prescriptions.index') }}" class="btn btn-ghost">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Retour
            </a>
        </div>
    </div>

    <!-- Receipt Content -->
    <div class="max-w-4xl mx-auto">
        <div class="card bg-base-100 shadow-xl print:shadow-none print:border">
            <div class="card-body print:p-8">
                <!-- Header -->
                <div class="text-center mb-8 print:mb-6">
                    <h1 class="text-3xl font-bold text-primary print:text-black">CLINIQUE MÉDICALE PRIVÉE</h1>
                    <p class="text-lg text-gray-600 print:text-black mt-2">Pharmacie</p>
                    <div class="divider print:border-t print:border-gray-300"></div>
                </div>

                <!-- Receipt Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h3 class="text-lg font-semibold mb-3 print:text-black">Informations de Vente</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="font-medium">N° Reçu:</span>
                                <span class="font-mono">{{ $prescription->prescription_number }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">Date:</span>
                                <span>{{ $prescription->dispensed_at ? $prescription->dispensed_at->format('d/m/Y à H:i') : $prescription->created_at->format('d/m/Y à H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">Type:</span>
                                <span>{{ $prescription->appointment_id ? 'Ordonnance médicale' : 'Vente directe' }}</span>
                            </div>
                            @if($prescription->dispensed_by_user)
                            <div class="flex justify-between">
                                <span class="font-medium">Dispensé par:</span>
                                <span>{{ $prescription->dispensed_by_user->name }}</span>
                            </div>
                            @endif
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-3 print:text-black">Informations Patient</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="font-medium">Nom:</span>
                                <span>{{ $prescription->patient->first_name }} {{ $prescription->patient->last_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">N° Patient:</span>
                                <span class="font-mono">{{ $prescription->patient->patient_number }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">Téléphone:</span>
                                <span>{{ $prescription->patient->phone ?? 'N/A' }}</span>
                            </div>
                            @if($prescription->appointment && $prescription->appointment->doctor)
                            <div class="flex justify-between">
                                <span class="font-medium">Médecin:</span>
                                <span>Dr. {{ $prescription->appointment->doctor->first_name }} {{ $prescription->appointment->doctor->last_name }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Medications Table -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-4 print:text-black">Médicaments Dispensés</h3>
                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full print:table-auto print:border-collapse">
                            <thead>
                                <tr class="print:border print:border-gray-300">
                                    <th class="print:border print:border-gray-300 print:p-2 print:text-left print:bg-gray-100">Médicament</th>
                                    <th class="print:border print:border-gray-300 print:p-2 print:text-center print:bg-gray-100">Quantité</th>
                                    <th class="print:border print:border-gray-300 print:p-2 print:text-right print:bg-gray-100">Prix Unit.</th>
                                    <th class="print:border print:border-gray-300 print:p-2 print:text-right print:bg-gray-100">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($prescription->prescriptionItems as $item)
                                <tr class="print:border print:border-gray-300">
                                    <td class="print:border print:border-gray-300 print:p-2">
                                        <div class="font-medium">{{ $item->medication_name }}</div>
                                        @if($item->medication_form)
                                            <div class="text-sm text-gray-500 print:text-gray-700">{{ $item->medication_form }}</div>
                                        @endif
                                        @if($item->dosage)
                                            <div class="text-sm text-gray-500 print:text-gray-700">Dosage: {{ $item->dosage }}</div>
                                        @endif
                                        @if($item->frequency && $prescription->appointment_id)
                                            <div class="text-sm text-gray-500 print:text-gray-700">{{ $item->frequency }}</div>
                                        @endif
                                        @if($item->duration && $prescription->appointment_id)
                                            <div class="text-sm text-gray-500 print:text-gray-700">Durée: {{ $item->duration }}</div>
                                        @endif
                                    </td>
                                    <td class="text-center print:border print:border-gray-300 print:p-2">{{ $item->quantity }}</td>
                                    <td class="text-right print:border print:border-gray-300 print:p-2">{{ number_format($item->unit_price, 0, ',', ' ') }} FCFA</td>
                                    <td class="text-right font-medium print:border print:border-gray-300 print:p-2">{{ number_format($item->total_price, 0, ',', ' ') }} FCFA</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr class="print:border print:border-gray-300 print:bg-gray-100">
                                    <td colspan="3" class="text-right font-bold print:border print:border-gray-300 print:p-2">TOTAL GÉNÉRAL:</td>
                                    <td class="text-right font-bold text-lg text-primary print:text-black print:border print:border-gray-300 print:p-2">{{ number_format($prescription->total_amount, 0, ',', ' ') }} FCFA</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Payment Information -->
                @if($prescription->payment)
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-4 print:text-black">Informations de Paiement</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div class="flex justify-between">
                            <span class="font-medium">Méthode de paiement:</span>
                            <span>{{ $prescription->payment->payment_method == 'cash' ? 'Espèces' : 'Mobile Money' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Montant payé:</span>
                            <span class="font-bold">{{ number_format($prescription->payment->amount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Référence:</span>
                            <span class="font-mono">{{ $prescription->payment->reference_number ?? 'N/A' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Statut:</span>
                            <span class="badge badge-success print:bg-green-100 print:text-green-800 print:px-2 print:py-1 print:rounded">{{ ucfirst($prescription->payment->status) }}</span>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Instructions (for prescriptions) -->
                @if($prescription->appointment_id && ($prescription->notes || $prescription->prescriptionItems->whereNotNull('instructions')->count() > 0))
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-4 print:text-black">Instructions Médicales</h3>
                    @if($prescription->notes)
                    <div class="bg-blue-50 print:bg-gray-100 border border-blue-200 print:border-gray-300 rounded-lg p-4 mb-4">
                        <h4 class="font-medium mb-2">Notes générales:</h4>
                        <p class="text-sm">{{ $prescription->notes }}</p>
                    </div>
                    @endif
                    
                    @foreach($prescription->prescriptionItems->whereNotNull('instructions') as $item)
                    <div class="bg-yellow-50 print:bg-gray-100 border border-yellow-200 print:border-gray-300 rounded-lg p-4 mb-2">
                        <h4 class="font-medium mb-1">{{ $item->medication_name }}:</h4>
                        <p class="text-sm">{{ $item->instructions }}</p>
                    </div>
                    @endforeach
                </div>
                @endif

                <!-- Footer -->
                <div class="text-center mt-8 pt-6 border-t border-gray-200 print:border-gray-300">
                    <p class="text-sm text-gray-600 print:text-black mb-2">
                        Merci de votre confiance. Conservez ce reçu pour vos dossiers.
                    </p>
                    <p class="text-xs text-gray-500 print:text-gray-700">
                        Document généré le {{ now()->format('d/m/Y à H:i') }}
                    </p>
                    
                    @if($prescription->appointment_id)
                    <div class="mt-4 p-3 bg-amber-50 print:bg-gray-100 border border-amber-200 print:border-gray-300 rounded-lg">
                        <p class="text-sm font-medium text-amber-800 print:text-black">
                            ⚠️ Respectez scrupuleusement les posologies et durées de traitement prescrites.
                        </p>
                        <p class="text-xs text-amber-700 print:text-gray-700 mt-1">
                            En cas d'effets indésirables, consultez immédiatement votre médecin.
                        </p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    body * {
        visibility: hidden;
    }
    .card, .card * {
        visibility: visible;
    }
    .card {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .btn, .navbar, .breadcrumbs {
        display: none !important;
    }
    .print\:shadow-none {
        box-shadow: none !important;
    }
    .print\:border {
        border: 1px solid #000 !important;
    }
    .print\:border-gray-300 {
        border-color: #d1d5db !important;
    }
    .print\:p-8 {
        padding: 2rem !important;
    }
    .print\:p-2 {
        padding: 0.5rem !important;
    }
    .print\:text-black {
        color: #000 !important;
    }
    .print\:text-left {
        text-align: left !important;
    }
    .print\:text-center {
        text-align: center !important;
    }
    .print\:text-right {
        text-align: right !important;
    }
    .print\:bg-gray-100 {
        background-color: #f3f4f6 !important;
    }
    .print\:bg-green-100 {
        background-color: #dcfce7 !important;
    }
    .print\:text-green-800 {
        color: #166534 !important;
    }
    .print\:text-gray-700 {
        color: #374151 !important;
    }
    .print\:px-2 {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
    .print\:py-1 {
        padding-top: 0.25rem !important;
        padding-bottom: 0.25rem !important;
    }
    .print\:rounded {
        border-radius: 0.25rem !important;
    }
    .print\:table-auto {
        table-layout: auto !important;
    }
    .print\:border-collapse {
        border-collapse: collapse !important;
    }
    .print\:mb-6 {
        margin-bottom: 1.5rem !important;
    }
}
</style>
@endsection