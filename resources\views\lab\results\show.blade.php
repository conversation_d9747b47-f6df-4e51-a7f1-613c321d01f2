@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-5xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-3xl font-bold text-primary">
                        Résultats du Bon de Travail #{{ $workOrder->work_order_number }}
                    </h2>
                    <div class="mt-2 flex flex-wrap gap-2 text-sm">
                        <span class="badge badge-ghost">Reçu le: {{ \Carbon\Carbon::parse($workOrder->created_at)->format('d/m/Y à H:i') }}</span>
                        <span class="badge badge-outline">{{ $workOrder->patient->first_name }} {{ $workOrder->patient->last_name }}</span>
                        <span class="badge badge-info">Priorité: {{ ucfirst($workOrder->priority) }}</span>
                    </div>
                </div>
                <a href="{{ route('lab.work-orders.index') }}" class="btn btn-outline btn-sm">
                    Retour
                </a>
            </div>
            <div class="mt-4">
                <span class="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium
                    @switch($workOrder->status)
                        @case('completed') bg-green-100 text-green-800 @break
                        @case('in_progress') bg-yellow-100 text-yellow-800 @break
                        @case('pending') bg-blue-100 text-blue-800 @break
                        @case('received') bg-indigo-100 text-indigo-800 @break
                        @case('cancelled') bg-red-100 text-red-800 @break
                        @default bg-gray-100 text-gray-800
                    @endswitch
                ">
                    {{ ucfirst($workOrder->status) }}
                </span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Sidebar: Patient & Payment Info -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Patient Card -->
                <div class="card bg-base-100 shadow-md border">
                    <div class="card-body">
                        <h3 class="card-title">Informations du Patient</h3>
                        <p><strong>Nom:</strong> {{ $workOrder->patient->first_name }} {{ $workOrder->patient->last_name }}</p>
                        <p><strong>Age:</strong> {{ $workOrder->patient->getAge() }} ans</p>
                        <p><strong>Sexe:</strong> {{ $workOrder->patient->gender }}</p>
                        <p><strong>Téléphone:</strong> {{ $workOrder->patient->phone_number }}</p>
                    </div>
                </div>

                <!-- Payment Card -->
                <div class="card bg-base-100 shadow-md border">
                    <div class="card-body">
                        <h3 class="card-title">Informations de Paiement</h3>
                        <p><strong>Numéro de Reçu:</strong> {{ $workOrder->labPayment->payment_number }}</p>
                        <p><strong>Montant Payé:</strong> {{ number_format($workOrder->labPayment->amount, 2) }} FCFA</p>
                        <p><strong>Date de Paiement:</strong> {{ \Carbon\Carbon::parse($workOrder->labPayment->payment_date)->format('d/m/Y') }}</p>
                    </div>
                </div>
            </div>

            <!-- Main Content: Results Details -->
            <div class="lg:col-span-2">
                <div class="card bg-base-100 shadow-md border">
                    <div class="card-body">
                        <h3 class="card-title">Détails des Résultats</h3>

                        <div class="overflow-x-auto">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th>Test</th>
                                        <th>Résultat</th>
                                        <th>Interprétation</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($labResults as $result)
                                        <tr>
                                            <td>{{ $result->labTest->name ?? 'N/A' }}</td>
                                            <td>{{ $result->result }}</td>
                                            <td>{{ $result->interpretation ?? 'N/A' }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="3" class="text-center">Aucun résultat disponible.</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        @if($workOrder->technician_notes)
                            <div class="mt-4">
                                <h4 class="font-semibold">Notes du Technicien</h4>
                                <p>{{ $workOrder->technician_notes }}</p>
                            </div>
                        @endif

                        @if($workOrder->assignedTechnician)
                            <div class="mt-4">
                                <h4 class="font-semibold">Technicien</h4>
                                <p>{{ $workOrder->assignedTechnician->name }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection