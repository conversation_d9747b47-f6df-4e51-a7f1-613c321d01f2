@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Ajouter un Médicament
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Ajouter un nouveau médicament au catalogue
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0">
                <a href="{{ route('admin.medications') }}" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Retour
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <form action="{{ route('admin.medications.store') }}" method="POST">
                        @csrf

                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Name -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nom commercial *</span>
                                </label>
                                <input type="text" name="name" value="{{ old('name') }}" 
                                       class="input input-bordered @error('name') input-error @enderror" 
                                       placeholder="Ex: Paracétamol, Amoxicilline..." required>
                                @error('name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Generic Name -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nom générique</span>
                                </label>
                                <input type="text" name="generic_name" value="{{ old('generic_name') }}" 
                                       class="input input-bordered @error('generic_name') input-error @enderror" 
                                       placeholder="Dénomination commune internationale">
                                @error('generic_name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Code -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Code médicament *</span>
                                </label>
                                <input type="text" name="code" value="{{ old('code') }}" 
                                       class="input input-bordered @error('code') input-error @enderror" 
                                       placeholder="Ex: PARA500, AMOX250..." required>
                                @error('code')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Unit Price -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Prix unitaire *</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" name="unit_price" value="{{ old('unit_price') }}" 
                                           class="input input-bordered @error('unit_price') input-error @enderror" 
                                           placeholder="0" min="0" step="10" required>
                                    <span class="bg-base-200 px-4 py-3 text-sm">FCFA</span>
                                </div>
                                @error('unit_price')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Dosage Form -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Forme pharmaceutique</span>
                                </label>
                                <select name="dosage_form" class="select select-bordered @error('dosage_form') select-error @enderror">
                                    <option value="">Sélectionner une forme</option>
                                    <option value="comprimé" {{ old('dosage_form') == 'comprimé' ? 'selected' : '' }}>Comprimé</option>
                                    <option value="gélule" {{ old('dosage_form') == 'gélule' ? 'selected' : '' }}>Gélule</option>
                                    <option value="sirop" {{ old('dosage_form') == 'sirop' ? 'selected' : '' }}>Sirop</option>
                                    <option value="injection" {{ old('dosage_form') == 'injection' ? 'selected' : '' }}>Injection</option>
                                    <option value="pommade" {{ old('dosage_form') == 'pommade' ? 'selected' : '' }}>Pommade</option>
                                    <option value="suppositoire" {{ old('dosage_form') == 'suppositoire' ? 'selected' : '' }}>Suppositoire</option>
                                    <option value="gouttes" {{ old('dosage_form') == 'gouttes' ? 'selected' : '' }}>Gouttes</option>
                                    <option value="spray" {{ old('dosage_form') == 'spray' ? 'selected' : '' }}>Spray</option>
                                </select>
                                @error('dosage_form')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Strength -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Dosage/Concentration</span>
                                </label>
                                <input type="text" name="strength" value="{{ old('strength') }}" 
                                       class="input input-bordered @error('strength') input-error @enderror" 
                                       placeholder="Ex: 500mg, 250mg/5ml, 1%...">
                                @error('strength')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>
                        </div>

                        <!-- Manufacturer -->
                        <div class="form-control mt-6">
                            <label class="label">
                                <span class="label-text">Fabricant</span>
                            </label>
                            <input type="text" name="manufacturer" value="{{ old('manufacturer') }}" 
                                   class="input input-bordered @error('manufacturer') input-error @enderror" 
                                   placeholder="Nom du laboratoire pharmaceutique">
                            @error('manufacturer')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="form-control mt-6">
                            <label class="label">
                                <span class="label-text">Description</span>
                            </label>
                            <textarea name="description" class="textarea textarea-bordered @error('description') textarea-error @enderror" 
                                      placeholder="Indications, contre-indications, effets secondaires...">{{ old('description') }}</textarea>
                            @error('description')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Options -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 mt-6">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Prescription</span>
                                </label>
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" name="prescription_required" value="1" 
                                           class="checkbox checkbox-warning" 
                                           {{ old('prescription_required') ? 'checked' : '' }}>
                                    <span class="label-text ml-2">Prescription médicale requise</span>
                                </label>
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Statut</span>
                                </label>
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" name="is_active" value="1" 
                                           class="checkbox checkbox-primary" 
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <span class="label-text ml-2">Médicament actif</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-control mt-8">
                            <button type="submit" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Ajouter le médicament
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title">Guide de saisie</h3>
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 mt-4">
                        <div>
                            <h4 class="font-semibold mb-2">Formes pharmaceutiques courantes :</h4>
                            <ul class="text-sm space-y-1">
                                <li>• <strong>Comprimé :</strong> Forme solide à avaler</li>
                                <li>• <strong>Gélule :</strong> Enveloppe contenant le principe actif</li>
                                <li>• <strong>Sirop :</strong> Solution liquide sucrée</li>
                                <li>• <strong>Injection :</strong> Administration parentérale</li>
                                <li>• <strong>Pommade :</strong> Application cutanée</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">Exemples de dosages :</h4>
                            <ul class="text-sm space-y-1">
                                <li>• <strong>500mg :</strong> Comprimés, gélules</li>
                                <li>• <strong>250mg/5ml :</strong> Sirops, suspensions</li>
                                <li>• <strong>1% :</strong> Pommades, crèmes</li>
                                <li>• <strong>10mg/ml :</strong> Solutions injectables</li>
                                <li>• <strong>0.5% :</strong> Gouttes ophtalmiques</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
