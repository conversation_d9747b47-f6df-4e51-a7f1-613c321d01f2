<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Patient;
use App\Models\Prescription;
use App\Models\PrescriptionItem;
use App\Models\Medication;
use App\Models\LabTest;
use App\Models\LabResult;
use App\Models\VitalSign;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Models\User;
use Spatie\Permission\Traits\HasRoles;
use Barryvdh\DomPDF\Facade\Pdf;

class ConsultationController extends Controller
{
    use AuthorizesRequests;
    
    /**
     * Display a listing of today's consultations for the logged-in doctor.
     */
    public function index()
    {
        $user = Auth::user();

        if (!$user->hasRole('doctor') && !$user->hasRole('admin')) {
            abort(403, 'Accès non autorisé. Vous n\'avez pas le rôle de médecin.');
        }

        $today = Carbon::today();

        // Si l'utilisateur est admin, il peut voir toutes les consultations
        if ($user->hasRole('admin')) {
            // Get today's appointments for all doctors
            $appointments = Appointment::with(['patient', 'service', 'doctor.user'])
                ->whereDate('appointment_datetime', $today)
                ->orderBy('appointment_datetime')
                ->get();

            // Get stats for the dashboard
            $stats = [
                'total_today' => $appointments->count(),
                'completed' => $appointments->where('status', Appointment::STATUS_COMPLETED)->count(),
                'in_progress' => $appointments->where('status', Appointment::STATUS_IN_PROGRESS)->count(),
                'scheduled' => $appointments->whereIn('status', [Appointment::STATUS_SCHEDULED, Appointment::STATUS_CONFIRMED])->count(),
                'cancelled' => $appointments->where('status', Appointment::STATUS_CANCELLED)->count(),
            ];

            return view('doctor.consultations.index', compact('appointments', 'stats'));
        }

        // Pour les médecins, vérifier le profil médecin
        $doctor = $user->doctor;

        if (!$doctor) {
            // Log l'erreur pour le débogage
            Log::error('Profil médecin manquant pour l\'utilisateur', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_name' => $user->name,
                'has_doctor_role' => $user->hasRole('doctor')
            ]);

            abort(403, 'Profil médecin manquant. L\'utilisateur a le rôle de médecin mais aucun profil médecin associé n\'a été trouvé dans la base de données. Veuillez contacter l\'administrateur pour créer votre profil médecin.');
        }

        // Get today's appointments for this doctor
        $appointments = Appointment::with(['patient', 'service'])
            ->where('doctor_id', $doctor->id)
            ->whereDate('appointment_datetime', $today)
            ->orderBy('appointment_datetime')
            ->get();

        // Get stats for the dashboard
        $stats = [
            'total_today' => $appointments->count(),
            'completed' => $appointments->where('status', Appointment::STATUS_COMPLETED)->count(),
            'in_progress' => $appointments->where('status', Appointment::STATUS_IN_PROGRESS)->count(),
            'scheduled' => $appointments->whereIn('status', [Appointment::STATUS_SCHEDULED, Appointment::STATUS_CONFIRMED])->count(),
            'cancelled' => $appointments->where('status', Appointment::STATUS_CANCELLED)->count(),
        ];

        return view('doctor.consultations.index', compact('appointments', 'stats'));
    }

    /**
     * Display consultation history for the logged-in doctor.
     */
    public function history(Request $request)
    {
        $user = Auth::user();

        if (!$user->hasRole('doctor') && !$user->hasRole('admin')) {
            abort(403, 'Accès non autorisé. Vous n\'avez pas le rôle de médecin.');
        }

        // Get filter parameters
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $status = $request->get('status');
        $patientSearch = $request->get('patient_search');

        // Si l'utilisateur est admin, il peut voir toutes les consultations
        if ($user->hasRole('admin')) {
            // Build query for consultation history (all doctors)
            $query = Appointment::with(['patient', 'service', 'doctor.user']);
        } else {
            // Pour les médecins, vérifier le profil médecin
            $doctor = $user->doctor;

            if (!$doctor) {
                abort(403, 'Profil médecin manquant.');
            }

            // Build query for consultation history (specific doctor)
            $query = Appointment::with(['patient', 'service'])
                ->where('doctor_id', $doctor->id);
        }
            
        // Apply date filters
        if ($startDate) {
            $query->whereDate('appointment_datetime', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('appointment_datetime', '<=', $endDate);
        }
        
        // Apply status filter
        if ($status && $status !== 'all') {
            $query->where('status', $status);
        }
        
        // Apply patient search
        if ($patientSearch) {
            $query->whereHas('patient', function($q) use ($patientSearch) {
                $q->where('first_name', 'like', '%' . $patientSearch . '%')
                  ->orWhere('last_name', 'like', '%' . $patientSearch . '%')
                  ->orWhere('phone', 'like', '%' . $patientSearch . '%');
            });
        }
        
        // Get paginated results
        $consultations = $query->orderBy('appointment_datetime', 'desc')
            ->paginate(20)
            ->withQueryString();
            
        // Get summary statistics
        if ($user->hasRole('admin')) {
            // Statistics for all doctors
            $totalConsultations = Appointment::count();
            $completedConsultations = Appointment::where('status', Appointment::STATUS_COMPLETED)->count();
            $thisMonthConsultations = Appointment::whereMonth('appointment_datetime', Carbon::now()->month)
                ->whereYear('appointment_datetime', Carbon::now()->year)
                ->count();
        } else {
            // Statistics for specific doctor
            $totalConsultations = Appointment::where('doctor_id', $doctor->id)->count();
            $completedConsultations = Appointment::where('doctor_id', $doctor->id)
                ->where('status', Appointment::STATUS_COMPLETED)
                ->count();
            $thisMonthConsultations = Appointment::where('doctor_id', $doctor->id)
                ->whereMonth('appointment_datetime', Carbon::now()->month)
                ->whereYear('appointment_datetime', Carbon::now()->year)
                ->count();
        }

        $stats = [
            'total' => $totalConsultations,
            'completed' => $completedConsultations,
            'this_month' => $thisMonthConsultations,
            'completion_rate' => $totalConsultations > 0 ? round(($completedConsultations / $totalConsultations) * 100, 1) : 0
        ];
        
        return view('doctor.consultations.history', compact('consultations', 'stats'));
    }
    
    /**
     * Show the consultation form for the specified appointment.
     */
    public function show(Appointment $appointment)
    {
        $this->authorize('view', $appointment);
        
        // Load appointment with related data
        $appointment->load([
            'patient', 
            'patient.vitalSigns' => function($query) {
                $query->latest()->limit(5);
            },
            'service', 
            'doctor.user',
            'prescriptions',
            'labResults',
        ]);
        
        // Get patient history
        $patientHistory = Appointment::with(['doctor.user', 'prescriptions', 'labResults'])
            ->where('patient_id', $appointment->patient_id)
            ->where('id', '!=', $appointment->id)
            ->where('status', Appointment::STATUS_COMPLETED)
            ->orderBy('appointment_datetime', 'desc')
            ->limit(10)
            ->get();
        
        // Get available medications for prescriptions
        $medications = Medication::where('is_active', true)
            ->orderBy('name')
            ->get();
            
        // Get available lab tests grouped by category
        $labTests = LabTest::where('is_active', true)
            ->orderBy('category')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get()
            ->groupBy('category');
            
        return view('doctor.consultations.show', compact(
            'appointment', 
            'patientHistory', 
            'medications',
            'labTests'
        ));
    }
    
    /**
     * Update the consultation notes and diagnosis.
     */
    public function update(Request $request, Appointment $appointment)
    {
        $this->authorize('update', $appointment);
        
        $validated = $request->validate([
            'doctor_notes' => 'nullable|string',
            'diagnosis' => 'nullable|string',
            'treatment_plan' => 'nullable|string',
            'status' => 'required|in:' . implode(',', Appointment::VALID_STATUSES),
        ]);
        
        // Validate status transition
        if (!$appointment->canTransitionTo($validated['status'])) {
            return back()->withErrors([
                'status' => 'Transition de statut invalide. Impossible de passer de "' . $appointment->status_label . '" à "' .
                           (new Appointment(['status' => $validated['status']]))->status_label . '".'
            ])->withInput();
        }
        
        // If completing the appointment, make sure it has a diagnosis
        if ($validated['status'] === Appointment::STATUS_COMPLETED && empty($validated['diagnosis'])) {
            return back()->withErrors([
                'diagnosis' => 'Un diagnostic est requis pour compléter la consultation.'
            ])->withInput();
        }
        
        try {
            $appointment->update($validated);
            
            // Log the status change
            Log::info('Appointment status updated', [
                'appointment_id' => $appointment->id,
                'old_status' => $appointment->getOriginal('status'),
                'new_status' => $validated['status'],
                'updated_by' => Auth::id(),
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to update appointment status', [
                'appointment_id' => $appointment->id,
                'status' => $validated['status'],
                'error' => $e->getMessage(),
            ]);
            
            return back()->withErrors([
                'error' => 'Erreur lors de la mise à jour du statut: ' . $e->getMessage()
            ])->withInput();
        }
        
        // Message de succès spécifique selon le statut
        $successMessage = match($validated['status']) {
            Appointment::STATUS_IN_PROGRESS => 'Consultation démarrée avec succès.',
            Appointment::STATUS_COMPLETED => 'Consultation terminée avec succès.',
            Appointment::STATUS_CANCELLED => 'Consultation annulée.',
            default => 'Consultation mise à jour avec succès.',
        };
        
        $flashData = ['success' => $successMessage];
        
        // Ajouter un flag spécial pour les consultations terminées
        if ($validated['status'] === Appointment::STATUS_COMPLETED) {
            $flashData['consultation_completed'] = true;
        }
        
        return redirect()->route('doctor.consultations.show', $appointment)
            ->with($flashData);
    }
    
    /**
     * Create a new prescription for the appointment.
     */
    public function createPrescription(Request $request, Appointment $appointment)
    {
        $this->authorize('update', $appointment);
        
        $validated = $request->validate([
            'diagnosis' => 'required|string',
            'notes' => 'nullable|string',
            'medications' => 'required|array',
            'medications.*.name' => 'required|string',
            'medications.*.form' => 'required|string',
            'medications.*.dosage' => 'required|string',
            'medications.*.frequency' => 'required|string',
            'medications.*.duration' => 'required|string',
            'medications.*.instructions' => 'nullable|string',
        ]);
        
        DB::beginTransaction();
        
        try {
            // Generate a unique prescription number
            $prescriptionNumber = 'RX-' . date('Ymd') . '-' . random_int(1000, 9999);
            
            // Create the prescription
            $prescription = Prescription::create([
                'prescription_number' => $prescriptionNumber,
                'appointment_id' => $appointment->id,
                'patient_id' => $appointment->patient_id,
                'doctor_id' => $appointment->doctor_id,
                'diagnosis' => $validated['diagnosis'],
                'notes' => $validated['notes'],
                'prescription_date' => now(),
                'status' => 'active',
            ]);
            
            // Create prescription items with manual medication data
            foreach ($validated['medications'] as $med) {
                PrescriptionItem::create([
                    'prescription_id' => $prescription->id,
                    'medication_id' => null, // Pas de référence au modèle Medication
                    'medication_name' => $med['name'],
                    'medication_form' => $med['form'],
                    'dosage' => $med['dosage'],
                    'frequency' => $med['frequency'],
                    'duration' => $med['duration'],
                    'instructions' => $med['instructions'] ?? null,
                    'status' => 'pending',
                ]);
            }
            
            // Update appointment diagnosis if not already set
            if (empty($appointment->diagnosis)) {
                $appointment->update([
                    'diagnosis' => $validated['diagnosis']
                ]);
            }
            
            DB::commit();
            
            return redirect()->route('doctor.consultations.show', $appointment)
                ->with('success', 'Ordonnance créée avec succès.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la création de l\'ordonnance: ' . $e->getMessage()
            ])->withInput();
        }
    }
    
    /**
     * Order lab tests for the patient.
     */
    public function orderLabTests(Request $request, Appointment $appointment)
    {
        $this->authorize('update', $appointment);

        $validated = $request->validate([
            'lab_tests' => 'required|array',
            'lab_tests.*' => 'exists:lab_tests,id',
            'clinical_information' => 'required|string', // Changé de nullable à required
            'urgency' => 'nullable|in:normal,urgent,stat',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // Generate a unique prescription number for this lab order
            $prescriptionNumber = 'LAB-PRESC-' . date('Ymd') . '-' . str_pad(
                LabResult::whereDate('created_at', today())->count() + 1,
                4,
                '0',
                STR_PAD_LEFT
            );

            $labResults = [];
            foreach ($validated['lab_tests'] as $testId) {
                // Generate a unique result number
                $resultNumber = 'LAB-' . date('Ymd') . '-' . random_int(1000, 9999);

                $labResult = LabResult::create([
                    'result_number' => $resultNumber,
                    'prescription_number' => $prescriptionNumber,
                    'appointment_id' => $appointment->id,
                    'patient_id' => $appointment->patient_id,
                    'doctor_id' => $appointment->doctor_id,
                    'lab_test_id' => $testId,
                    'clinical_information' => $validated['clinical_information'],
                    'urgency' => $validated['urgency'] ?? 'normal',
                    'notes' => $validated['notes'] ?? null,
                    'status' => 'ordered',
                    'ordered_at' => now(),
                ]);

                $labResults[] = $labResult;
            }

            DB::commit();

            // Store prescription number in session for PDF generation
            session(['last_lab_prescription' => $prescriptionNumber]);

            return redirect()->route('doctor.consultations.show', $appointment)
                ->with('success', 'Prescription d\'analyses créée avec succès. Numéro: ' . $prescriptionNumber)
                ->with('lab_prescription_number', $prescriptionNumber);

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Log l'erreur pour faciliter le débogage
            \Log::error('Erreur lors de la création de prescription d\'analyses', [
                'appointment_id' => $appointment->id,
                'doctor_id' => $appointment->doctor_id,
                'patient_id' => $appointment->patient_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la demande d\'analyses: ' . $e->getMessage()
            ])->withInput();
        }
    }
    
    /**
     * Record vital signs for the patient.
     */
    public function recordVitalSigns(Request $request, Appointment $appointment)
    {
        $this->authorize('update', $appointment);
        
        $validated = $request->validate([
            'temperature' => 'nullable|numeric',
            'blood_pressure_systolic' => 'nullable|integer',
            'blood_pressure_diastolic' => 'nullable|integer',
            'heart_rate' => 'nullable|integer',
            'respiratory_rate' => 'nullable|integer',
            'oxygen_saturation' => 'nullable|integer',
            'height' => 'nullable|numeric',
            'weight' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);
        
        // Create the vital signs record
        VitalSign::create([
            'patient_id' => $appointment->patient_id,
            'appointment_id' => $appointment->id,
            'temperature' => $validated['temperature'] ?? null,
            'blood_pressure_systolic' => $validated['blood_pressure_systolic'] ?? null,
            'blood_pressure_diastolic' => $validated['blood_pressure_diastolic'] ?? null,
            'heart_rate' => $validated['heart_rate'] ?? null,
            'respiratory_rate' => $validated['respiratory_rate'] ?? null,
            'oxygen_saturation' => $validated['oxygen_saturation'] ?? null,
            'height' => $validated['height'] ?? null,
            'weight' => $validated['weight'] ?? null,
            'bmi' => ($validated['height'] && $validated['weight']) 
                ? round($validated['weight'] / (($validated['height'] / 100) ** 2), 2) 
                : null,
            'notes' => $validated['notes'] ?? null,
            'recorded_by' => Auth::id(),
            'recorded_at' => now(),
        ]);
        
        return redirect()->route('doctor.consultations.show', $appointment)
            ->with('success', 'Constantes vitales enregistrées avec succès.');
    }

    /**
     * Generate PDF for prescription.
     */
    public function generatePrescriptionPDF(Prescription $prescription)
    {
        // Vérifier que l'utilisateur peut voir cette prescription
        $user = Auth::user();
        if (!$user->hasRole('admin') && (!$user->hasRole('doctor') || !$user->doctor || $prescription->doctor_id !== $user->doctor->id)) {
            abort(403, 'Accès non autorisé à cette ordonnance.');
        }

        // Charger les relations nécessaires
        $prescription->load([
            'patient',
            'doctor.user',
            'doctor.service',
            'prescriptionItems',
            'appointment'
        ]);

        // Générer le PDF
        $pdf = Pdf::loadView('doctor.prescriptions.pdf', compact('prescription'));

        return $pdf->download('ordonnance-' . $prescription->prescription_number . '.pdf');
    }

    /**
     * Generate lab tests prescription PDF.
     */
    public function generateLabTestsPDF(Request $request, Appointment $appointment)
    {
        $user = Auth::user();
        if (!$user->hasRole('admin') && (!$user->hasRole('doctor') || !$user->doctor || $appointment->doctor_id !== $user->doctor->id)) {
            abort(403, 'Accès non autorisé à cette consultation.');
        }

        $prescriptionNumber = $request->get('prescription_number');

        if (!$prescriptionNumber) {
            // Si le numéro de prescription n'est pas fourni, essayer de le récupérer depuis la session
            $prescriptionNumber = session('last_lab_prescription');
            
            if (!$prescriptionNumber) {
                return back()->withErrors(['error' => 'Numéro de prescription manquant.']);
            }
        }

        // Get lab results for this prescription
        $labResults = LabResult::with(['labTest', 'patient', 'doctor.user', 'appointment.service'])
            ->where('prescription_number', $prescriptionNumber)
            ->where('appointment_id', $appointment->id)
            ->get();

        if ($labResults->isEmpty()) {
            // Log pour débogage
            \Log::warning('Aucune analyse trouvée pour la prescription', [
                'prescription_number' => $prescriptionNumber,
                'appointment_id' => $appointment->id
            ]);
            
            return back()->withErrors(['error' => 'Aucune analyse trouvée pour cette prescription.']);
        }

        $patient = $appointment->patient;
        $doctor = $appointment->doctor;

        try {
            $pdf = Pdf::loadView('doctor.lab-tests.prescription-pdf', compact(
                'labResults',
                'patient',
                'doctor',
                'appointment',
                'prescriptionNumber'
            ));

            return $pdf->download('prescription-analyses-' . $prescriptionNumber . '.pdf');
        } catch (\Exception $e) {
            // Log l'erreur
            \Log::error('Erreur lors de la génération du PDF', [
                'prescription_number' => $prescriptionNumber,
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return back()->withErrors(['error' => 'Erreur lors de la génération du PDF: ' . $e->getMessage()]);
        }
    }
}