@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-5xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-3xl font-bold text-primary">
                        Bon de Travail #{{ $workOrder->work_order_number }}
                    </h2>
                    <div class="mt-2 flex flex-wrap gap-2 text-sm">
                        <span class="badge badge-ghost"><i class="fas fa-calendar-alt mr-1"></i> Reçu le: {{ \Carbon\Carbon::parse($workOrder->created_at)->format('d/m/Y à H:i') }}</span>
                        <span class="badge badge-outline"><i class="fas fa-user mr-1"></i> {{ $workOrder->patient->first_name }} {{ $workOrder->patient->last_name }}</span>
                        <span class="badge badge-info"><i class="fas fa-tag mr-1"></i> Priorité: {{ ucfirst($workOrder->priority) }}</span>
                    </div>
                </div>
                <a href="{{ route('lab-technician.dashboard') }}" class="btn btn-outline btn-sm">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Retour au tableau de bord
                </a>
            </div>
            <div class="mt-4">
                <span class="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium
                    @switch($workOrder->status)
                        @case('completed') bg-green-100 text-green-800 @break
                        @case('in_progress') bg-yellow-100 text-yellow-800 @break
                        @case('pending') bg-blue-100 text-blue-800 @break
                        @case('received') bg-indigo-100 text-indigo-800 @break
                        @case('cancelled') bg-red-100 text-red-800 @break
                        @default bg-gray-100 text-gray-800
                    @endswitch
                ">
                    {{ ucfirst($workOrder->status) }}
                </span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Sidebar: Patient & Payment Info -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Patient Card -->
                <div class="card bg-base-100 shadow-md border">
                    <div class="card-body">
                        <h3 class="card-title">Informations du Patient</h3>
                        <p><strong>Nom:</strong> {{ $workOrder->patient->first_name }} {{ $workOrder->patient->last_name }}</p>
                        <p><strong>Age:</strong> {{ $workOrder->patient->getAge() }} ans</p>
                        <p><strong>Sexe:</strong> {{ $workOrder->patient->gender }}</p>
                        <p><strong>Téléphone:</strong> {{ $workOrder->patient->phone_number }}</p>
                    </div>
                </div>

                <!-- Payment Card -->
                <div class="card bg-base-100 shadow-md border">
                    <div class="card-body">
                        <h3 class="card-title">Informations de Paiement</h3>
                        <p><strong>Numéro de Reçu:</strong> {{ $workOrder->labPayment->payment_number }}</p>
                        <p><strong>Montant Payé:</strong> {{ number_format($workOrder->labPayment->amount, 2) }} FCFA</p>
                        <p><strong>Date de Paiement:</strong> {{ \Carbon\Carbon::parse($workOrder->labPayment->payment_date)->format('d/m/Y') }}</p>
                    </div>
                </div>
            </div>

            <!-- Main Content: Work Order Details -->
            <div class="lg:col-span-2">
                <div class="card bg-base-100 shadow-md border">
                    <div class="card-body">
                        <h3 class="card-title">Détails du Bon de Travail</h3>

                        <div class="mb-4">
                            <h4 class="font-semibold">Informations Cliniques</h4>
                            <p>{{ $workOrder->clinical_information }}</p>
                        </div>

                        <div class="mb-4">
                            <h4 class="font-semibold">Tests à effectuer</h4>
                            <ul class="list-disc list-inside">
                                @foreach($workOrder->lab_tests_details as $test)
                                    <li>{{ $test['name'] }} ({{ $test['code'] }}) - {{ $test['urgency'] }}</li>
                                @endforeach
                            </ul>
                        </div>

                        <div class="mb-4">
                            <h4 class="font-semibold">Échantillons</h4>
                            @if($workOrder->samples->count() > 0)
                                <ul class="list-disc list-inside">
                                    @foreach($workOrder->samples as $sample)
                                        <li>{{ $sample->sample_type }} - {{ $sample->status }}</li>
                                    @endforeach
                                </ul>
                            @else
                                <p>Aucun échantillon collecté pour le moment.</p>
                            @endif
                        </div>

                        @if($workOrder->assignedTechnician)
                            <div class="mb-4">
                                <h4 class="font-semibold">Technicien Assigné</h4>
                                <p>{{ $workOrder->assignedTechnician->name }}</p>
                            </div>
                        @endif

                        <div class="card-actions justify-end">
                            @if($workOrder->status === 'pending' || $workOrder->status === 'received')
                                <form action="{{ route('lab.work-orders.start', $workOrder) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-primary">Démarrer le traitement</button>
                                </form>
                            @elseif($workOrder->status === 'in_progress')
                                <a href="{{ route('lab.samples.create', $workOrder) }}" class="btn btn-secondary">Collecter les échantillons</a>
                            @elseif($workOrder->status === 'samples_collected')
                                <a href="{{ route('lab.results.create', $workOrder) }}" class="btn btn-accent">Saisir les résultats</a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection