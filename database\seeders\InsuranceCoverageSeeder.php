<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class InsuranceCoverageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $insuranceTypes = [
            [
                'insurance_type' => 'AMO',
                'coverage_percentage' => 70.00,
                'description' => 'Assurance Maladie Obligatoire - Couverture de base pour les salariés du secteur privé',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'CNOPS',
                'coverage_percentage' => 80.00,
                'description' => 'Caisse Nationale des Organismes de Prévoyance Sociale - Fonctionnaires et agents publics',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'CNSS',
                'coverage_percentage' => 70.00,
                'description' => 'Caisse Nationale de Sécurité Sociale - Salariés du secteur privé',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'RAMED',
                'coverage_percentage' => 90.00,
                'description' => 'Régime d\'Assistance Médicale - Population économiquement défavorisée',
                'is_active' => true,
            ],
            [
                'insurance_type' => 'Privée',
                'coverage_percentage' => 60.00,
                'description' => 'Assurance privée - Couverture variable selon le contrat',
                'is_active' => true,
            ],
        ];

        foreach ($insuranceTypes as $insuranceType) {
            DB::table('insurance_coverage')->updateOrInsert(
                ['insurance_type' => $insuranceType['insurance_type']],
                $insuranceType
            );
        }
    }
}
