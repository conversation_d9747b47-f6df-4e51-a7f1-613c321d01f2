<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ordonnance #{{ $prescription->prescription_number }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .clinic-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .clinic-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .prescription-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-top: 15px;
        }
        
        .info-section {
            display: table;
            width: 100%;
            margin-bottom: 25px;
        }
        
        .doctor-info, .patient-info {
            display: table-cell;
            width: 48%;
            vertical-align: top;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .doctor-info {
            margin-right: 4%;
            background-color: #f8fafc;
        }
        
        .patient-info {
            background-color: #fef3f2;
        }
        
        .info-title {
            font-size: 14px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 10px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 5px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        .info-label {
            font-weight: bold;
            color: #4b5563;
        }
        
        .prescription-details {
            margin-bottom: 25px;
        }
        
        .diagnosis-section {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .diagnosis-title {
            font-size: 14px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .medications-section {
            margin-bottom: 25px;
        }
        
        .medications-title {
            font-size: 16px;
            font-weight: bold;
            color: #7c2d12;
            margin-bottom: 15px;
            text-align: center;
            background-color: #fed7aa;
            padding: 10px;
            border-radius: 8px;
        }
        
        .medication-item {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fafafa;
        }
        
        .medication-name {
            font-size: 14px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .medication-details {
            display: table;
            width: 100%;
        }
        
        .medication-detail {
            display: table-cell;
            width: 25%;
            padding: 5px;
            border-right: 1px solid #e5e7eb;
        }
        
        .medication-detail:last-child {
            border-right: none;
        }
        
        .detail-label {
            font-size: 10px;
            font-weight: bold;
            color: #6b7280;
            text-transform: uppercase;
        }
        
        .detail-value {
            font-size: 12px;
            color: #374151;
            margin-top: 2px;
        }
        
        .medication-instructions {
            margin-top: 10px;
            padding: 8px;
            background-color: #f0f9ff;
            border-left: 3px solid #0ea5e9;
            font-style: italic;
            color: #0c4a6e;
        }
        
        .general-instructions {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
        }
        
        .instructions-title {
            font-size: 14px;
            font-weight: bold;
            color: #166534;
            margin-bottom: 8px;
        }
        
        .footer {
            margin-top: 40px;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        
        .signature-section {
            display: table;
            width: 100%;
        }
        
        .signature-left, .signature-right {
            display: table-cell;
            width: 48%;
            text-align: center;
            vertical-align: top;
        }
        
        .signature-left {
            margin-right: 4%;
        }
        
        .signature-line {
            border-bottom: 1px solid #374151;
            height: 50px;
            margin-bottom: 10px;
        }
        
        .signature-label {
            font-size: 11px;
            color: #6b7280;
        }
        
        .prescription-number {
            text-align: center;
            font-size: 10px;
            color: #9ca3af;
            margin-top: 20px;
        }
        
        .date-location {
            text-align: right;
            margin-bottom: 20px;
            font-size: 11px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="clinic-name">CLINIQUE MÉDICALE</div>
        <div class="clinic-info">
            Adresse de la clinique • Téléphone: +223 XX XX XX XX • Email: <EMAIL>
        </div>
        <div class="prescription-title">ORDONNANCE MÉDICALE</div>
    </div>

    <div class="date-location">
        Bamako, le {{ $prescription->prescription_date->format('d/m/Y') }}
    </div>

    <div class="info-section">
        <div class="doctor-info">
            <div class="info-title">MÉDECIN PRESCRIPTEUR</div>
            <div class="info-item">
                <span class="info-label">Dr.</span> {{ $prescription->doctor->user->name }}
            </div>
            <div class="info-item">
                <span class="info-label">Spécialité:</span> {{ $prescription->doctor->specialization }}
            </div>
            <div class="info-item">
                <span class="info-label">Service:</span> {{ $prescription->doctor->service->name }}
            </div>
            <div class="info-item">
                <span class="info-label">N° Licence:</span> {{ $prescription->doctor->license_number }}
            </div>
        </div>

        <div class="patient-info">
            <div class="info-title">PATIENT</div>
            <div class="info-item">
                <span class="info-label">Nom:</span> {{ $prescription->patient->first_name }} {{ $prescription->patient->last_name }}
            </div>
            <div class="info-item">
                <span class="info-label">Âge:</span> {{ $prescription->patient->getAge() }} ans
            </div>
            <div class="info-item">
                <span class="info-label">Sexe:</span> {{ $prescription->patient->gender }}
            </div>
            @if($prescription->patient->phone_number)
            <div class="info-item">
                <span class="info-label">Téléphone:</span> {{ $prescription->patient->phone_number }}
            </div>
            @endif
        </div>
    </div>

    @if($prescription->diagnosis)
    <div class="diagnosis-section">
        <div class="diagnosis-title">DIAGNOSTIC</div>
        <div>{{ $prescription->diagnosis }}</div>
    </div>
    @endif

    <div class="medications-section">
        <div class="medications-title">MÉDICAMENTS PRESCRITS</div>
        
        @foreach($prescription->prescriptionItems as $index => $item)
        <div class="medication-item">
            <div class="medication-name">
                {{ $index + 1 }}. {{ $item->medication_name ?? $item->medication->name }}
                @if($item->medication_form ?? $item->medication->form)
                    ({{ $item->medication_form ?? $item->medication->form }})
                @endif
            </div>
            
            <div class="medication-details">
                <div class="medication-detail">
                    <div class="detail-label">Dosage</div>
                    <div class="detail-value">{{ $item->dosage }}</div>
                </div>
                <div class="medication-detail">
                    <div class="detail-label">Fréquence</div>
                    <div class="detail-value">{{ $item->frequency }}</div>
                </div>
                <div class="medication-detail">
                    <div class="detail-label">Durée</div>
                    <div class="detail-value">{{ $item->duration }}</div>
                </div>
                <div class="medication-detail">
                    <div class="detail-label">Statut</div>
                    <div class="detail-value">{{ ucfirst($item->status) }}</div>
                </div>
            </div>
            
            @if($item->instructions)
            <div class="medication-instructions">
                <strong>Instructions:</strong> {{ $item->instructions }}
            </div>
            @endif
        </div>
        @endforeach
    </div>

    @if($prescription->notes)
    <div class="general-instructions">
        <div class="instructions-title">INSTRUCTIONS GÉNÉRALES</div>
        <div>{{ $prescription->notes }}</div>
    </div>
    @endif

    <div class="footer">
        <div class="signature-section">
            <div class="signature-left">
                <div class="signature-line"></div>
                <div class="signature-label">Signature du Patient</div>
            </div>
            <div class="signature-right">
                <div class="signature-line"></div>
                <div class="signature-label">Signature et Cachet du Médecin</div>
            </div>
        </div>
        
        <div class="prescription-number">
            Ordonnance N° {{ $prescription->prescription_number }} - Générée le {{ now()->format('d/m/Y à H:i') }}
        </div>
    </div>
</body>
</html>