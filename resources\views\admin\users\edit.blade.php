@extends('layouts.app')

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Modifier l'Utilisateur
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    Modifier les informations de {{ $user->name }}
                </p>
            </div>
            <div class="mt-4 flex md:ml-4 md:mt-0">
                <a href="{{ route('admin.users') }}" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Retour
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <form action="{{ route('admin.users.update', $user) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Name -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nom complet *</span>
                                </label>
                                <input type="text" name="name" value="{{ old('name', $user->name) }}" 
                                       class="input input-bordered @error('name') input-error @enderror" 
                                       placeholder="Nom complet" required>
                                @error('name')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Email *</span>
                                </label>
                                <input type="email" name="email" value="{{ old('email', $user->email) }}" 
                                       class="input input-bordered @error('email') input-error @enderror" 
                                       placeholder="<EMAIL>" required>
                                @error('email')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Phone -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Téléphone</span>
                                </label>
                                <input type="text" name="phone_number" value="{{ old('phone_number', $user->phone_number) }}" 
                                       class="input input-bordered @error('phone_number') input-error @enderror" 
                                       placeholder="+225 XX XX XX XX">
                                @error('phone_number')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <!-- Role -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Rôle *</span>
                                </label>
                                <select name="role" class="select select-bordered @error('role') select-error @enderror" required>
                                    <option value="">Sélectionner un rôle</option>
                                    @foreach($roles as $role)
                                        <option value="{{ $role->name }}" 
                                                {{ old('role', $user->roles->first()?->name) == $role->name ? 'selected' : '' }}>
                                            {{ ucfirst($role->name) }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('role')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="form-control mt-6">
                            <label class="label">
                                <span class="label-text">Adresse</span>
                            </label>
                            <textarea name="address" class="textarea textarea-bordered @error('address') textarea-error @enderror" 
                                      placeholder="Adresse complète">{{ old('address', $user->address) }}</textarea>
                            @error('address')
                                <label class="label">
                                    <span class="label-text-alt text-error">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Password Fields -->
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 mt-6">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Nouveau mot de passe</span>
                                    <span class="label-text-alt">Laisser vide pour ne pas changer</span>
                                </label>
                                <input type="password" name="password" 
                                       class="input input-bordered @error('password') input-error @enderror" 
                                       placeholder="Nouveau mot de passe">
                                @error('password')
                                    <label class="label">
                                        <span class="label-text-alt text-error">{{ $message }}</span>
                                    </label>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Confirmer le mot de passe</span>
                                </label>
                                <input type="password" name="password_confirmation" 
                                       class="input input-bordered" 
                                       placeholder="Confirmer le mot de passe">
                            </div>
                        </div>

                        <!-- Doctor-specific fields -->
                        <div id="doctor-fields" class="mt-6" style="display: {{ $user->hasRole('doctor') ? 'block' : 'none' }};">
                            <div class="divider">Informations Médecin</div>
                            
                            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Service</span>
                                    </label>
                                    <select name="service_id" class="select select-bordered">
                                        <option value="">Sélectionner un service</option>
                                        @foreach($services as $service)
                                            <option value="{{ $service->id }}" 
                                                    {{ old('service_id', $user->doctor?->service_id) == $service->id ? 'selected' : '' }}>
                                                {{ $service->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Spécialisation</span>
                                    </label>
                                    <input type="text" name="specialization" value="{{ old('specialization', $user->doctor?->specialization) }}" 
                                           class="input input-bordered" placeholder="Spécialisation">
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Qualification</span>
                                    </label>
                                    <input type="text" name="qualification" value="{{ old('qualification', $user->doctor?->qualification) }}" 
                                           class="input input-bordered" placeholder="Diplômes et qualifications">
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Numéro de licence</span>
                                    </label>
                                    <input type="text" name="license_number" value="{{ old('license_number', $user->doctor?->license_number) }}" 
                                           class="input input-bordered" placeholder="Numéro de licence médicale">
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Heure de début</span>
                                    </label>
                                    <input type="time" name="working_hours_start" 
                                           value="{{ old('working_hours_start', $user->doctor?->working_hours_start?->format('H:i')) }}" 
                                           class="input input-bordered">
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Heure de fin</span>
                                    </label>
                                    <input type="time" name="working_hours_end" 
                                           value="{{ old('working_hours_end', $user->doctor?->working_hours_end?->format('H:i')) }}" 
                                           class="input input-bordered">
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Statut</span>
                                    </label>
                                    <label class="label cursor-pointer">
                                        <span class="label-text">Médecin actif</span>
                                        <input type="checkbox" name="is_active" value="1" 
                                               class="checkbox checkbox-primary" 
                                               {{ old('is_active', $user->doctor?->is_active) ? 'checked' : '' }}>
                                    </label>
                                </div>
                            </div>

                            <div class="form-control mt-4">
                                <label class="label">
                                    <span class="label-text">Jours de travail</span>
                                </label>
                                <div class="flex flex-wrap gap-2">
                                    @php
                                        $days = ['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche'];
                                        $workingDays = old('working_days', $user->doctor?->working_days ?? []);
                                    @endphp
                                    @foreach($days as $day)
                                        <label class="label cursor-pointer">
                                            <input type="checkbox" name="working_days[]" value="{{ $day }}" 
                                                   class="checkbox checkbox-primary" 
                                                   {{ in_array($day, $workingDays) ? 'checked' : '' }}>
                                            <span class="label-text ml-2">{{ ucfirst($day) }}</span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>

                            <div class="form-control mt-4">
                                <label class="label">
                                    <span class="label-text">Biographie</span>
                                </label>
                                <textarea name="biography" class="textarea textarea-bordered" 
                                          placeholder="Biographie du médecin">{{ old('biography', $user->doctor?->biography) }}</textarea>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-control mt-8">
                            <button type="submit" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Mettre à jour
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.querySelector('select[name="role"]');
    const doctorFields = document.getElementById('doctor-fields');
    
    function toggleDoctorFields() {
        if (roleSelect.value === 'doctor') {
            doctorFields.style.display = 'block';
        } else {
            doctorFields.style.display = 'none';
        }
    }
    
    roleSelect.addEventListener('change', toggleDoctorFields);
    toggleDoctorFields(); // Initial check
});
</script>
@endsection
