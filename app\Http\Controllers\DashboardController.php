<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Patient;
use App\Models\Appointment;
use App\Models\Payment;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Main dashboard with role-based redirection.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Redirect based on user's primary role
        if ($user->hasRole('receptionist')) {
            return redirect()->route('receptionist.dashboard');
        } elseif ($user->hasRole('doctor')) {
            return redirect()->route('doctor.dashboard');
        } elseif ($user->hasRole('nurse')) {
            return redirect()->route('nurse.dashboard');
        } elseif ($user->hasRole('pharmacist')) {
            return redirect()->route('pharmacist.dashboard');
        } elseif ($user->hasRole('lab_technician')) {
            return redirect()->route('lab-technician.dashboard');
        } elseif ($user->hasRole('accountant')) {
            return redirect()->route('accountant.dashboard');
        }
        
        // Admin or default fallback
        return redirect()->route('admin.dashboard');
    }

    /**
     * Admin dashboard.
     */
    public function admin()
    {
        // No authorization check for admin to allow access to all dashboards
        return view('dashboard.admin', [
            'title' => 'Tableau de bord Administrateur'
        ]);
    }

    /**
     * Receptionist dashboard.
     */
    public function receptionist()
    {
        if (!Auth::user()->hasRole('admin') && !Auth::user()->hasRole('receptionist')) {
            abort(403, 'Accès non autorisé.');
        }
        
        $today = Carbon::today();
        
        // Get today's statistics
        $todayPatientsCount = Patient::whereDate('created_at', $today)->count();
        $todayAppointmentsCount = Appointment::whereDate('appointment_date', $today)->count();
        $todayPaymentsTotal = Payment::whereDate('created_at', $today)->sum('amount');
        $waitingPatientsCount = Appointment::whereDate('appointment_date', $today)
            ->where('status', 'scheduled')
            ->count();
        
        // Get recent patients for activity feed
        $recentPatients = Patient::with('appointments')
            ->latest()
            ->take(5)
            ->get();
        
        return view('dashboard.receptionist', [
            'title' => 'Tableau de bord Réceptionniste',
            'todayPatientsCount' => $todayPatientsCount,
            'todayAppointmentsCount' => $todayAppointmentsCount,
            'todayPaymentsTotal' => $todayPaymentsTotal,
            'waitingPatientsCount' => $waitingPatientsCount,
            'recentPatients' => $recentPatients,
        ]);
    }

    /**
     * Doctor dashboard.
     */
    public function doctor()
    {
        if (!Auth::user()->hasRole('admin') && !Auth::user()->hasRole('doctor')) {
            abort(403, 'Accès non autorisé.');
        }
        
        // Redirect to the doctor's consultations page
        return redirect()->route('doctor.consultations.index');
    }

    /**
     * Nurse dashboard.
     */
    public function nurse()
    {
        if (!Auth::user()->hasRole('admin') && !Auth::user()->hasRole('nurse')) {
            abort(403, 'Accès non autorisé.');
        }
        
        return view('dashboard.nurse', [
            'title' => 'Tableau de bord Infirmier'
        ]);
    }

    /**
     * Pharmacist dashboard.
     */
    public function pharmacist()
    {
        if (!Auth::user()->hasRole('admin') && !Auth::user()->hasRole('pharmacist')) {
            abort(403, 'Accès non autorisé.');
        }
        
        // Rediriger vers le contrôleur spécialisé
        return app(\App\Http\Controllers\PharmacistController::class)->dashboard();
    }

    /**
     * Lab technician dashboard.
     */
    public function labTechnician()
    {
        if (!Auth::user()->hasRole('admin') && !Auth::user()->hasRole('lab_technician')) {
            abort(403, 'Accès non autorisé.');
        }

        // Rediriger vers le contrôleur spécialisé
        return app(\App\Http\Controllers\LabTechnicianController::class)->dashboard();
    }

    /**
     * Accountant dashboard.
     */
    public function accountant()
    {
        if (!Auth::user()->hasRole('admin') && !Auth::user()->hasRole('accountant')) {
            abort(403, 'Accès non autorisé.');
        }
        
        return view('dashboard.accountant', [
            'title' => 'Tableau de bord Comptable'
        ]);
    }

    /**
     * Authorize user role.
     */
    private function authorize($role)
    {
        if (!Auth::user()->hasRole('admin') && !Auth::user()->hasRole($role)) {
            abort(403, 'Accès non autorisé.');
        }
    }
}
