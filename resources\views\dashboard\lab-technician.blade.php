@extends('layouts.app')

@section('content')
<div class="container mx-auto p-6">
    <!-- Header avec breadcrumb -->
    <div class="mb-8">
        <div class="breadcrumbs text-sm mb-4">
            <ul>
                <li><a href="{{ route('lab-technician.dashboard') }}" class="text-primary hover:text-primary-focus">🏠 Dashboard</a></li>
                <li class="text-base-content/70">🧪 Laboratoire</li>
            </ul>
        </div>

        <!-- Hero Section -->
        <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content shadow-2xl">
            <div class="card-body text-center py-8">
                <h1 class="text-4xl font-bold mb-4 text-white">
                    🧪 Laboratoire - Technicien
                </h1>
                <div class="badge badge-accent badge-lg mb-4">
                    👨‍🔬 {{ auth()->user()->name }}
                </div>
                <p class="text-white/90 text-lg">
                    Gestion des analyses et résultats de laboratoire
                </p>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Analyses en attente -->
        <div class="card bg-base-100 shadow-xl border-l-4 border-warning">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-base-content/70">En attente</h3>
                        <p class="text-3xl font-bold text-warning">{{ $pendingTests }}</p>
                    </div>
                    <div class="w-12 h-12 bg-warning/20 rounded-full flex items-center justify-center">
                        ⏳
                    </div>
                </div>
                <div class="text-xs text-base-content/60 mt-2">
                    Analyses à traiter
                </div>
            </div>
        </div>

        <!-- Terminées aujourd'hui -->
        <div class="card bg-base-100 shadow-xl border-l-4 border-success">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-base-content/70">Terminées</h3>
                        <p class="text-3xl font-bold text-success">{{ $completedToday }}</p>
                    </div>
                    <div class="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                        ✅
                    </div>
                </div>
                <div class="text-xs text-base-content/60 mt-2">
                    Aujourd'hui
                </div>
            </div>
        </div>

        <!-- Analyses urgentes -->
        <div class="card bg-base-100 shadow-xl border-l-4 border-error">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-base-content/70">Urgentes</h3>
                        <p class="text-3xl font-bold text-error">{{ $urgentTests }}</p>
                    </div>
                    <div class="w-12 h-12 bg-error/20 rounded-full flex items-center justify-center">
                        🚨
                    </div>
                </div>
                <div class="text-xs text-base-content/60 mt-2">
                    Priorité haute
                </div>
            </div>
        </div>

        <!-- Échantillons -->
        <div class="card bg-base-100 shadow-xl border-l-4 border-info">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-base-content/70">Échantillons</h3>
                        <p class="text-3xl font-bold text-info">{{ $pendingSamples }}</p>
                    </div>
                    <div class="w-12 h-12 bg-info/20 rounded-full flex items-center justify-center">
                        🧪
                    </div>
                </div>
                <div class="text-xs text-base-content/60 mt-2">
                    À traiter
                </div>
            </div>
        </div>
    </div>



    <!-- Analyses en attente -->
    <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body">
            <div class="flex items-center justify-between mb-6">
                <h3 class="card-title text-primary">⏳ Analyses en attente</h3>
                <div class="badge badge-warning badge-lg">{{ $recentWorkOrders->count() }} en attente</div>
            </div>

            @if($recentWorkOrders->count() > 0)
                <div class="space-y-4">
                    @foreach($recentWorkOrders as $workOrder)
                    <div class="card bg-base-200 border-l-4
                        @if($workOrder->priority === 'urgent') border-warning
                        @elseif($workOrder->priority === 'stat') border-error
                        @else border-info @endif">
                        <div class="card-body p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="avatar placeholder">
                                        <div class="bg-primary text-primary-content rounded-full w-12">
                                            <span class="text-sm font-bold">
                                                {{ substr($workOrder->patient->first_name, 0, 1) }}{{ substr($workOrder->patient->last_name, 0, 1) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-base-content">
                                            {{ $workOrder->patient->first_name }} {{ $workOrder->patient->last_name }}
                                        </h4>
                                        <p class="text-sm text-base-content/70">
                                            📋 {{ $workOrder->prescription_number }}
                                        </p>
                                        <p class="text-xs text-base-content/60">
                                            🕒 {{ $workOrder->received_at ? $workOrder->received_at->diffForHumans() : 'Reçu récemment' }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($workOrder->priority === 'urgent')
                                        <div class="badge badge-warning">⚡ Urgent</div>
                                    @elseif($workOrder->priority === 'stat')
                                        <div class="badge badge-error">🚨 STAT</div>
                                    @else
                                        <div class="badge badge-info">📋 Normal</div>
                                    @endif
                                    <a href="{{ route('lab.work-orders.show', $workOrder) }}"
                                       class="btn btn-primary btn-sm">
                                        Traiter
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <div class="text-6xl mb-4">🎉</div>
                    <h3 class="text-xl font-bold text-base-content mb-2">Aucune analyse en attente</h3>
                    <p class="text-base-content/70">Toutes les analyses sont à jour !</p>
                </div>
            @endif
        </div>
    </div>
    <!-- Résultats récents -->
    <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body">
            <div class="flex items-center justify-between mb-6">
                <h3 class="card-title text-success">✅ Résultats récents</h3>
                <div class="badge badge-success badge-lg">{{ $recentResults->count() }} terminées</div>
            </div>

            @if($recentResults->count() > 0)
                <div class="space-y-4">
                    @foreach($recentResults as $result)
                    <div class="card bg-success/10 border border-success/20">
                        <div class="card-body p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                                        ✅
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-base-content">
                                            {{ $result->patient->first_name }} {{ $result->patient->last_name }}
                                        </h4>
                                        <p class="text-sm text-base-content/70">
                                            📋 {{ $result->prescription_number }}
                                        </p>
                                        <p class="text-xs text-base-content/60">
                                            🕒 {{ $result->completed_at ? $result->completed_at->diffForHumans() : 'Terminé récemment' }}
                                        </p>
                                    </div>
                                </div>
                                <a href="{{ route('lab.results.show', $result) }}" class="btn btn-success btn-sm">
                                    Voir Résultats
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <div class="text-6xl mb-4">📊</div>
                    <h3 class="text-xl font-bold text-base-content mb-2">Aucun résultat récent</h3>
                    <p class="text-base-content/70">Les résultats terminés apparaîtront ici</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h3 class="card-title text-primary mb-6">⚡ Actions rapides</h3>
            <div class="grid grid-cols-1 gap-6">
                <!-- Traiter Analyses -->
                <a href="{{ route('lab.work-orders.index') }}" class="card bg-gradient-to-br from-warning/10 to-accent/10 border border-warning/20 hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="card-body text-center">
                        <div class="text-4xl mb-4">🧪</div>
                        <h4 class="font-bold text-warning mb-2">Traiter Analyses</h4>
                        <p class="text-sm text-base-content/70">
                            Voir les bons de travail
                        </p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>


@endsection
