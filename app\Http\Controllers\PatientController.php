<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PatientController extends Controller
{
    /**
     * Display a listing of patients.
     */
    public function index(Request $request)
    {
        $query = Patient::with(['appointments.service', 'appointments.doctor.user'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('patient_number', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by gender
        if ($request->filled('gender')) {
            $query->where('gender', $request->gender);
        }

        $patients = $query->paginate(15);

        return view('patients.index', compact('patients'));
    }

    /**
     * Show the form for creating a new patient.
     */
    public function create()
    {
        return view('patients.create');
    }

    /**
     * Store a newly created patient.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female,other',
            'phone_number' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'medical_history' => 'nullable|string',
            'allergies' => 'nullable|string',
            'blood_group' => 'nullable|string|max:10',
            // Insurance fields
            'has_insurance' => 'boolean',
            'insurance_type' => 'nullable|string|in:AMO,CNOPS,CNSS,RAMED,Privée,Mutuelle,Assurance_Internationale',
            'insurance_name' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:100',
            'nina' => 'nullable|string|max:20',
            'custom_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'insurance_start_date' => 'nullable|date|before_or_equal:today',
            'insurance_expiry_date' => 'nullable|date|after:insurance_start_date',
            'insurance_notes' => 'nullable|string|max:1000',
        ]);

        // Generate unique patient number
        $validated['patient_number'] = $this->generatePatientNumber();

        $patient = Patient::create($validated);

        return redirect()->route('patients.show', $patient)
            ->with('success', 'Patient enregistré avec succès.');
    }

    /**
     * Display the specified patient.
     */
    public function show(Patient $patient)
    {
        $patient->load([
            'appointments.service',
            'appointments.doctor.user',
            'appointments.payment',
            'payments',
            'vitalSigns' => function ($query) {
                $query->latest()->take(5);
            },
            'labResults' => function ($query) {
                $query->latest()->take(5);
            }
        ]);

        // Calculate age
        $age = Carbon::parse($patient->date_of_birth)->age;

        // Get recent appointments
        $recentAppointments = $patient->appointments()
            ->with(['service', 'doctor.user', 'payment'])
            ->orderBy('appointment_datetime', 'desc')
            ->take(10)
            ->get();

        // Get upcoming appointments
        $upcomingAppointments = $patient->appointments()
            ->with(['service', 'doctor.user'])
            ->where('appointment_datetime', '>', now())
            ->where('status', '!=', 'cancelled')
            ->orderBy('appointment_datetime', 'asc')
            ->get();

        // Get payments
        $payments = $patient->payments()
            ->with(['appointment.service'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return view('patients.show', compact('patient', 'age', 'recentAppointments', 'upcomingAppointments', 'payments'));
    }

    /**
     * Show the form for editing the patient.
     */
    public function edit(Patient $patient)
    {
        return view('patients.edit', compact('patient'));
    }

    /**
     * Update the specified patient.
     */
    public function update(Request $request, Patient $patient)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female,other',
            'phone_number' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'medical_history' => 'nullable|string',
            'allergies' => 'nullable|string',
            'blood_group' => 'nullable|string|max:10',
            // Insurance fields
            'has_insurance' => 'boolean',
            'insurance_type' => 'nullable|string|in:AMO,CNOPS,CNSS,RAMED,Privée,Mutuelle,Assurance_Internationale',
            'insurance_name' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:100',
            'nina' => 'nullable|string|max:20',
            'custom_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'insurance_start_date' => 'nullable|date|before_or_equal:today',
            'insurance_expiry_date' => 'nullable|date|after:insurance_start_date',
            'insurance_notes' => 'nullable|string|max:1000',
        ]);

        $patient->update($validated);

        return redirect()->route('patients.show', $patient)
            ->with('success', 'Informations patient mises à jour avec succès.');
    }

    /**
     * Remove the specified patient.
     */
    public function destroy(Patient $patient)
    {
        // Check if patient has appointments
        if ($patient->appointments()->count() > 0) {
            return redirect()->route('patients.index')
                ->with('error', 'Impossible de supprimer un patient ayant des rendez-vous.');
        }

        $patient->delete();

        return redirect()->route('patients.index')
            ->with('success', 'Patient supprimé avec succès.');
    }

    /**
     * Search patients via AJAX.
     */
    public function search(Request $request)
    {
        $search = $request->get('q');
        
        $patients = Patient::where('first_name', 'like', "%{$search}%")
            ->orWhere('last_name', 'like', "%{$search}%")
            ->orWhere('patient_number', 'like', "%{$search}%")
            ->orWhere('phone_number', 'like', "%{$search}%")
            ->limit(10)
            ->get(['id', 'first_name', 'last_name', 'patient_number', 'phone_number']);

        return response()->json($patients);
    }

    /**
     * Generate unique patient number.
     */
    private function generatePatientNumber(): string
    {
        $year = date('Y');
        $month = date('m');
        
        // Get the last patient number for this month
        $lastPatient = Patient::where('patient_number', 'like', "PAT{$year}{$month}%")
            ->orderBy('patient_number', 'desc')
            ->first();

        if ($lastPatient) {
            $lastNumber = (int) substr($lastPatient->patient_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return "PAT{$year}{$month}" . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get patient statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_patients' => Patient::count(),
            'new_patients_today' => Patient::whereDate('created_at', today())->count(),
            'new_patients_this_month' => Patient::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)->count(),
            'patients_with_appointments_today' => Patient::whereHas('appointments', function ($query) {
                $query->whereDate('appointment_datetime', today());
            })->count(),
        ];

        return $stats;
    }
}
