<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Doctor extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'service_id',
        'specialization',
        'qualification',
        'license_number',
        'biography',
        'is_active',
        'working_hours_start',
        'working_hours_end',
        'working_days',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'working_hours_start' => 'datetime',
        'working_hours_end' => 'datetime',
        'working_days' => 'array',
    ];

    /**
     * Get the user associated with the doctor.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the service associated with the doctor.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the appointments for the doctor.
     */
    public function appointments(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get the prescriptions written by the doctor.
     */
    public function prescriptions(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Prescription::class);
    }

    /**
     * Get the lab results ordered by the doctor.
     */
    public function labResults(): HasMany
    {
        return $this->hasMany(LabResult::class);
    }

    /**
     * Get the full name of the doctor.
     */
    public function getFullNameAttribute(): string
    {
        return $this->user->name;
    }
}