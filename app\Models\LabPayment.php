<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LabPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_number',
        'prescription_number',
        'patient_id',
        'appointment_id',
        'lab_tests',
        'total_amount',
        'original_amount',
        'insurance_applied',
        'insurance_type',
        'insurance_number',
        'insurance_coverage_percentage',
        'insurance_discount',
        'insurance_details',
        'insurance_notes',
        'payment_method',
        'transaction_reference',
        'status',
        'received_by',
        'notes',
        'payment_date',
        'work_order_generated_at',
    ];

    protected $casts = [
        'lab_tests' => 'array',
        'total_amount' => 'decimal:2',
        'original_amount' => 'decimal:2',
        'insurance_applied' => 'boolean',
        'insurance_coverage_percentage' => 'decimal:2',
        'insurance_discount' => 'decimal:2',
        'insurance_details' => 'array',
        'payment_date' => 'datetime',
        'work_order_generated_at' => 'datetime',
    ];

    /**
     * Get the patient associated with the lab payment.
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the appointment associated with the lab payment.
     */
    public function appointment(): BelongsTo
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the user who received the payment.
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Get the work order associated with this payment.
     */
    public function workOrder(): HasOne
    {
        return $this->hasOne(LabWorkOrder::class);
    }

    /**
     * Get the lab results associated with this payment.
     */
    public function labResults(): HasMany
    {
        return $this->hasMany(LabResult::class, 'prescription_number', 'prescription_number');
    }

    /**
     * Generate a unique payment number.
     */
    public static function generatePaymentNumber(): string
    {
        $date = now()->format('Ymd');
        $count = self::whereDate('created_at', today())->count() + 1;
        return 'LAB-PAY-' . $date . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate insurance discount for lab tests
     */
    public function calculateInsuranceDiscount(Patient $patient, array $labTests): array
    {
        if (!$patient->hasValidInsurance()) {
            return [
                'total_discount' => 0,
                'final_amount' => array_sum(array_column($labTests, 'price')),
                'coverage_percentage' => 0,
                'insurance_type' => null,
                'details' => [],
            ];
        }

        $coveragePercentage = $patient->getInsuranceCoveragePercentage();
        $totalOriginal = 0;
        $totalDiscount = 0;
        $details = [];

        foreach ($labTests as $test) {
            $testPrice = $test['price'] ?? 0;
            $testDiscount = $testPrice * ($coveragePercentage / 100);

            $totalOriginal += $testPrice;
            $totalDiscount += $testDiscount;

            $details[] = [
                'test_id' => $test['id'] ?? null,
                'test_name' => $test['name'] ?? '',
                'original_price' => $testPrice,
                'discount' => $testDiscount,
                'final_price' => $testPrice - $testDiscount,
                'coverage_percentage' => $coveragePercentage,
            ];
        }

        return [
            'total_discount' => $totalDiscount,
            'final_amount' => $totalOriginal - $totalDiscount,
            'coverage_percentage' => $coveragePercentage,
            'insurance_type' => $patient->insurance_type,
            'insurance_number' => $patient->insurance_number,
            'details' => $details,
        ];
    }

    /**
     * Apply insurance to this lab payment
     */
    public function applyInsurance(Patient $patient, array $labTests, float $originalAmount): void
    {
        $insuranceData = $this->calculateInsuranceDiscount($patient, $labTests);

        $this->update([
            'original_amount' => $originalAmount,
            'total_amount' => $insuranceData['final_amount'],
            'insurance_applied' => $insuranceData['total_discount'] > 0,
            'insurance_type' => $insuranceData['insurance_type'],
            'insurance_number' => $insuranceData['insurance_number'],
            'insurance_coverage_percentage' => $insuranceData['coverage_percentage'],
            'insurance_discount' => $insuranceData['total_discount'],
            'insurance_details' => $insuranceData['details'],
        ]);
    }

    /**
     * Get insurance summary
     */
    public function getInsuranceSummary(): array
    {
        return [
            'applied' => $this->insurance_applied,
            'type' => $this->insurance_type,
            'number' => $this->insurance_number,
            'coverage_percentage' => $this->insurance_coverage_percentage,
            'original_amount' => $this->original_amount,
            'discount' => $this->insurance_discount,
            'final_amount' => $this->total_amount,
            'savings' => $this->insurance_discount,
            'details' => $this->insurance_details,
        ];
    }

    /**
     * Get payment method label.
     */
    public function getPaymentMethodLabelAttribute(): string
    {
        return match($this->payment_method) {
            'cash' => 'Espèces',
            'mobile_money' => 'Mobile Money',
            'card' => 'Carte bancaire',
            'insurance' => 'Assurance',
            'credit' => 'Crédit',
            default => ucfirst($this->payment_method),
        };
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'paid' => 'Payé',
            'partially_paid' => 'Partiellement payé',
            'refunded' => 'Remboursé',
            default => ucfirst($this->status),
        };
    }
}
