<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('insurance_coverage', function (Blueprint $table) {
            $table->id();
            $table->string('insurance_type')->unique(); // AMO, CNOPS, CNSS, RAMED, Privée
            $table->decimal('coverage_percentage', 5, 2); // Pourcentage de couverture (0.00 à 100.00)
            $table->text('description')->nullable(); // Description du type d'assurance
            $table->boolean('is_active')->default(true); // Si ce type d'assurance est actif
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('insurance_coverage');
    }
};
