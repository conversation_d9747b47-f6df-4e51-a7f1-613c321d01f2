<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LabSample extends Model
{
    use HasFactory;

    protected $fillable = [
        'sample_code',
        'lab_work_order_id',
        'lab_result_id',
        'patient_id',
        'sample_type',
        'container_type',
        'volume',
        'status',
        'collected_at',
        'collected_by',
        'analyzed_at',
        'analyzed_by',
        'collection_notes',
        'storage_conditions',
        'expiry_date',
    ];

    protected $casts = [
        'volume' => 'decimal:2',
        'collected_at' => 'datetime',
        'analyzed_at' => 'datetime',
        'expiry_date' => 'datetime',
    ];

    /**
     * Get the work order associated with this sample.
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(LabWorkOrder::class, 'lab_work_order_id');
    }

    /**
     * Get the lab result associated with this sample.
     */
    public function labResult(): BelongsTo
    {
        return $this->belongsTo(LabResult::class);
    }

    /**
     * Get the patient associated with this sample.
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the user who collected this sample.
     */
    public function collector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'collected_by');
    }

    /**
     * Get the user who analyzed this sample.
     */
    public function analyzer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'analyzed_by');
    }

    /**
     * Generate a unique sample code.
     */
    public static function generateSampleCode(): string
    {
        $date = now()->format('Ymd');
        $count = self::whereDate('created_at', today())->count() + 1;
        return 'SAMP-' . $date . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get sample type label.
     */
    public function getSampleTypeLabelAttribute(): string
    {
        return match($this->sample_type) {
            'blood' => 'Sang',
            'urine' => 'Urine',
            'stool' => 'Selles',
            'saliva' => 'Salive',
            'swab' => 'Prélèvement',
            'other' => 'Autre',
            default => ucfirst($this->sample_type),
        };
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'collected' => 'Prélevé',
            'processing' => 'En traitement',
            'analyzed' => 'Analysé',
            'discarded' => 'Éliminé',
            default => ucfirst($this->status),
        };
    }

    /**
     * Check if sample is expired.
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }
}
