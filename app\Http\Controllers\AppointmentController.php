<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Patient;
use App\Models\Doctor;
use App\Models\Service;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    /**
     * Display a listing of appointments.
     */
    public function index(Request $request)
    {
        $query = Appointment::with(['patient', 'doctor.user', 'service', 'payment'])
            ->orderBy('appointment_datetime', 'desc');

        // Filter by date
        if ($request->filled('date')) {
            $query->whereDate('appointment_datetime', $request->date);
        } else {
            // Default to today's appointments
            $query->whereDate('appointment_datetime', today());
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by doctor
        if ($request->filled('doctor_id')) {
            $query->where('doctor_id', $request->doctor_id);
        }

        // Filter by service
        if ($request->filled('service_id')) {
            $query->where('service_id', $request->service_id);
        }

        $appointments = $query->paginate(20);

        // Get filters data
        $doctors = Doctor::with('user')->where('is_active', true)->get();
        $services = Service::where('is_active', true)->get();

        return view('appointments.index', compact('appointments', 'doctors', 'services'));
    }

    /**
     * Show the form for creating a new appointment.
     */
    public function create(Request $request)
    {
        $patients = Patient::orderBy('first_name')->get();
        $services = Service::where('is_active', true)->get();
        $doctors = Doctor::with('user')->where('is_active', true)->get();

        // Pre-select patient if provided
        $selectedPatient = null;
        if ($request->filled('patient_id')) {
            $selectedPatient = Patient::find($request->patient_id);
        }

        return view('appointments.create', compact('patients', 'services', 'doctors', 'selectedPatient'));
    }

    /**
     * Store a newly created appointment.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'service_id' => 'required|exists:services,id',
            'doctor_id' => 'required|exists:doctors,id',
            'appointment_date' => 'required|date|after_or_equal:today',
            'appointment_time' => 'required|date_format:H:i',
            'estimated_duration' => 'nullable|date_format:H:i',
            'reason' => 'nullable|string|max:500',
            'notes' => 'nullable|string',
            'is_followup' => 'boolean',
            'previous_appointment_id' => 'nullable|exists:appointments,id',
        ]);

        // Combine date and time
        $appointmentDateTime = Carbon::createFromFormat(
            'Y-m-d H:i',
            $validated['appointment_date'] . ' ' . $validated['appointment_time']
        );

        // Check if doctor is available
        if (!$this->isDoctorAvailable($validated['doctor_id'], $appointmentDateTime)) {
            return back()->withErrors(['appointment_time' => 'Le médecin n\'est pas disponible à cette heure.'])
                ->withInput();
        }

        // Create appointment
        $appointment = Appointment::create([
            'patient_id' => $validated['patient_id'],
            'service_id' => $validated['service_id'],
            'doctor_id' => $validated['doctor_id'],
            'appointment_datetime' => $appointmentDateTime,
            'estimated_duration' => $validated['estimated_duration'] ?? '00:30:00',
            'reason' => $validated['reason'] ?? null,
            'notes' => $validated['notes'] ?? null,
            'is_followup' => $validated['is_followup'] ?? false,
            'previous_appointment_id' => $validated['previous_appointment_id'] ?? null,
            'status' => 'scheduled',
        ]);

        return redirect()->route('appointments.show', $appointment)
            ->with('success', 'Rendez-vous créé avec succès.');
    }

    /**
     * Display the specified appointment.
     */
    public function show(Appointment $appointment)
    {
        $appointment->load(['patient', 'doctor.user', 'service', 'payment', 'vitalSigns', 'labResults']);

        return view('appointments.show', compact('appointment'));
    }

    /**
     * Show the form for editing the appointment.
     */
    public function edit(Appointment $appointment)
    {
        $patients = Patient::orderBy('first_name')->get();
        $services = Service::where('is_active', true)->get();
        $doctors = Doctor::with('user')->where('is_active', true)->get();

        return view('appointments.edit', compact('appointment', 'patients', 'services', 'doctors'));
    }

    /**
     * Update the specified appointment.
     */
    public function update(Request $request, Appointment $appointment)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'service_id' => 'required|exists:services,id',
            'doctor_id' => 'required|exists:doctors,id',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required|date_format:H:i',
            'estimated_duration' => 'nullable|date_format:H:i',
            'status' => 'required|in:scheduled,confirmed,completed,cancelled,no_show',
            'reason' => 'nullable|string|max:500',
            'notes' => 'nullable|string',
            'doctor_notes' => 'nullable|string',
            'diagnosis' => 'nullable|string',
        ]);

        // Combine date and time
        $appointmentDateTime = Carbon::createFromFormat(
            'Y-m-d H:i',
            $validated['appointment_date'] . ' ' . $validated['appointment_time']
        );

        // Check if doctor is available (only if changing doctor or time)
        if ($appointment->doctor_id != $validated['doctor_id'] || 
            $appointment->appointment_datetime != $appointmentDateTime) {
            if (!$this->isDoctorAvailable($validated['doctor_id'], $appointmentDateTime, $appointment->id)) {
                return back()->withErrors(['appointment_time' => 'Le médecin n\'est pas disponible à cette heure.'])
                    ->withInput();
            }
        }

        $appointment->update([
            'patient_id' => $validated['patient_id'],
            'service_id' => $validated['service_id'],
            'doctor_id' => $validated['doctor_id'],
            'appointment_datetime' => $appointmentDateTime,
            'estimated_duration' => $validated['estimated_duration'] ?? '00:30:00',
            'status' => $validated['status'],
            'reason' => $validated['reason'],
            'notes' => $validated['notes'],
            'doctor_notes' => $validated['doctor_notes'],
            'diagnosis' => $validated['diagnosis'],
        ]);

        return redirect()->route('appointments.show', $appointment)
            ->with('success', 'Rendez-vous mis à jour avec succès.');
    }

    /**
     * Remove the specified appointment.
     */
    public function destroy(Appointment $appointment)
    {
        // Check if appointment has payment
        if ($appointment->payment) {
            return redirect()->route('appointments.index')
                ->with('error', 'Impossible de supprimer un rendez-vous avec paiement.');
        }

        $appointment->delete();

        return redirect()->route('appointments.index')
            ->with('success', 'Rendez-vous supprimé avec succès.');
    }

    /**
     * Get available time slots for a doctor on a specific date.
     */
    public function getAvailableSlots(Request $request)
    {
        $doctorId = $request->doctor_id;
        $date = $request->date;
        $appointmentId = $request->appointment_id; // For editing

        if (!$doctorId || !$date) {
            return response()->json(['error' => 'Paramètres manquants'], 400);
        }

        $doctor = Doctor::find($doctorId);
        if (!$doctor) {
            return response()->json(['error' => 'Médecin non trouvé'], 404);
        }

        $slots = $this->generateTimeSlots($doctor, $date, $appointmentId);

        return response()->json($slots);
    }

    /**
     * Check if doctor is available at given time.
     */
    private function isDoctorAvailable($doctorId, $dateTime, $excludeAppointmentId = null): bool
    {
        $query = Appointment::where('doctor_id', $doctorId)
            ->where('appointment_datetime', $dateTime)
            ->where('status', '!=', 'cancelled');

        if ($excludeAppointmentId) {
            $query->where('id', '!=', $excludeAppointmentId);
        }

        return $query->count() === 0;
    }

    /**
     * Generate available time slots for a doctor on a specific date.
     */
    private function generateTimeSlots($doctor, $date, $excludeAppointmentId = null): array
    {
        $slots = [];
        $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $date . ' 08:00:00');
        $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $date . ' 18:00:00');

        // Get existing appointments for this doctor on this date
        $existingAppointments = Appointment::where('doctor_id', $doctor->id)
            ->whereDate('appointment_datetime', $date)
            ->where('status', '!=', 'cancelled')
            ->when($excludeAppointmentId, function ($query) use ($excludeAppointmentId) {
                return $query->where('id', '!=', $excludeAppointmentId);
            })
            ->pluck('appointment_datetime')
            ->map(function ($datetime) {
                return Carbon::parse($datetime)->format('H:i');
            })
            ->toArray();

        while ($startTime->lt($endTime)) {
            $timeSlot = $startTime->format('H:i');
            
            if (!in_array($timeSlot, $existingAppointments)) {
                $slots[] = [
                    'time' => $timeSlot,
                    'display' => $startTime->format('H:i'),
                    'available' => true
                ];
            }

            $startTime->addMinutes(30);
        }

        return $slots;
    }

    /**
     * Get appointment statistics.
     */
    public function getStats()
    {
        $today = today();
        
        $stats = [
            'total_today' => Appointment::whereDate('appointment_datetime', $today)->count(),
            'completed_today' => Appointment::whereDate('appointment_datetime', $today)
                ->where('status', 'completed')->count(),
            'pending_today' => Appointment::whereDate('appointment_datetime', $today)
                ->whereIn('status', ['scheduled', 'confirmed'])->count(),
            'cancelled_today' => Appointment::whereDate('appointment_datetime', $today)
                ->where('status', 'cancelled')->count(),
        ];

        return $stats;
    }
}
